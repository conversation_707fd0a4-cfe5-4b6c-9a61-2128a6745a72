{"name": "@mass/admin", "type": "module", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint --fix .", "preview": "vite preview", "generate:icons": "pnpm dlx iconify-transpile", "get-url": "./scripts/get-deployment-url.sh", "knip": "knip --config ../../knip.config.ts", "build:icons": "iconify-build --config ../../iconify.config.json", "test": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@iconify/react": "^5.2.0", "@iconify/tools": "^4.0.7", "@iconify/types": "^2.0.0", "@mass/shared": "workspace:*", "@mdx-js/react": "^3.1.0", "@mdx-js/rollup": "^3.1.0", "@radix-ui/react-icons": "^1.3.2", "@tanstack/react-query": "^5.62.3", "@tanstack/react-router": "^1.86.1", "@tanstack/react-table": "^8.21.2", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "i18next": "^24.2.2", "js-cookie": "^3.0.5", "lucide-react": "^0.468.0", "motion": "^12.4.10", "nanoid": "^5.1.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.0", "react-i18next": "^15.4.1", "recharts": "^2.14.1", "sonner": "^2.0.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@antfu/eslint-config": "^4.4.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query-devtools": "^5.62.3", "@tanstack/router-devtools": "^1.86.1", "@tanstack/router-plugin": "^1.86.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/mdx": "^2.0.13", "@types/node": "^22.10.1", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.1", "knip": "^5.41.1", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "~5.7.2", "vite": "^6.0.9", "vite-plugin-i18next-loader": "^3.1.2"}, "overrides": {"@radix-ui/react-focus-scope": "1.1.1"}}