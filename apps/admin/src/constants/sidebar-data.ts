import type { SidebarData } from "@mass/shared/components/organisms/layout/types";
import { LinkProps } from "@tanstack/react-router";

export const sidebarData: SidebarData = {
  navGroups: [
    {
      titleKey: "sidebar.groups.general",
      items: [
        {
          titleKey: "sidebar.items.dashboard",
          url: "/dashboard" as LinkProps["to"],
          icon: "untitled:line-chart-up-01",
        },
        {
          titleKey: "sidebar.items.user_management",
          url: "/user-management",
          icon: "untitled:users-edit",
        },
        {
          titleKey: "sidebar.items.document_management",
          icon: "untitled:file-05",
          items: [
            {
              titleKey:
                "sidebar.items.document_management_detail.agreement_guidelines",
              url: "/document-management/agreement-guidelines" as LinkProps["to"],
            },
            {
              titleKey: "sidebar.items.document_management_detail.edas_info",
              url: "/document-management/edas-info" as LinkProps["to"],
            },
            {
              titleKey: "sidebar.items.document_management_detail.faq",
              url: "/document-management/faq" as LinkProps["to"],
            },
          ],
        },
        {
          titleKey: "sidebar.items.notification_management",
          icon: "untitled:bell-01",
          items: [
            {
              titleKey:
                "sidebar.items.notification_management_detail.notification_history",
              url: "/notification-management/history" as LinkProps["to"],
            },
            {
              titleKey: "sidebar.items.notification_management_detail.settings",
              url: "/notification-management/settings" as LinkProps["to"],
            },
          ],
        },
        {
          titleKey: "sidebar.items.complaint_management",
          icon: "untitled:message-chat-square",
          url: "/complaint-management" as LinkProps["to"],
        },
        {
          titleKey: "sidebar.items.admin_settings.parameters_settings",
          icon: "untitled:settings-04",
          url: "/settings/parameter-settings" as LinkProps["to"],
        },
        {
          titleKey: "sidebar.items.admin_settings.page_settings",
          icon: "untitled:file-04",
          url: "/settings/page-settings",
        },
        {
          titleKey: "sidebar.items.admin_settings.account_settings",
          icon: "untitled:settings-02",
          url: "/settings/account-settings" as LinkProps["to"],
        },
        {
          titleKey: "sidebar.items.logout",
          icon: "untitled:log-out-01",
          url: "/logout",
        },
      ],
    },
  ],
};
