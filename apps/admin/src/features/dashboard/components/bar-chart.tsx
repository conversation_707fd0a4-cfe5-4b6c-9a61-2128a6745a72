"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON> as Re<PERSON>rtsBar<PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  <PERSON>lt<PERSON>,
} from "recharts";

const data = [
  {
    name: "<PERSON><PERSON>",
    toplam: 3240,
  },
  {
    name: "<PERSON><PERSON>",
    toplam: 4180,
  },
  {
    name: "<PERSON>",
    toplam: 5940,
  },
  {
    name: "<PERSON><PERSON>",
    toplam: 6320,
  },
  {
    name: "May",
    toplam: 7850,
  },
  {
    name: "<PERSON><PERSON>",
    toplam: 6980,
  },
  {
    name: "<PERSON><PERSON>",
    toplam: 5560,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    toplam: 5480,
  },
  {
    name: "<PERSON><PERSON>",
    toplam: 6780,
  },
  {
    name: "<PERSON><PERSON>",
    toplam: 5890,
  },
  {
    name: "<PERSON><PERSON>",
    toplam: 5740,
  },
  {
    name: "<PERSON>",
    toplam: 5430,
  },
];

export function BarChart() {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <RechartsBarChart data={data}>
        <XAxis
          dataKey="name"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <YAxis
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${value}`}
        />
        <Tooltip
          formatter={(value: number) => [
            `${value.toLocaleString()} kullanıcı`,
            "Aktif Kullanıcı",
          ]}
          labelFormatter={(label) => `${label} 2025`}
        />
        <Bar dataKey="toplam" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
      </RechartsBarChart>
    </ResponsiveContainer>
  );
}
