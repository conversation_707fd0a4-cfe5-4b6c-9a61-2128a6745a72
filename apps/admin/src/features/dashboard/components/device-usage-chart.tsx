"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Toolt<PERSON>,
} from "recharts";

const data = [
  { name: "<PERSON><PERSON>", value: 68 },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", value: 23 },
  { name: "<PERSON>t", value: 9 },
];

const COLORS = ["#3b82f6", "#10b981", "#f59e0b"];

export function DeviceUsageChart() {
  return (
    <div className="h-[350px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={120}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) =>
              `${name}: ${(percent * 100).toFixed(0)}%`
            }
          >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [`${value}%`, "Kullanım Oranı"]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
