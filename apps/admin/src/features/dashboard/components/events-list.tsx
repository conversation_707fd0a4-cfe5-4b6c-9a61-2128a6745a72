const events = [
  {
    id: 1,
    name: "Abonelik Sorgulama",
    count: "24,532",
    percentage: "+12.3%",
  },
  {
    id: 2,
    name: "<PERSON><PERSON> Sorgulama",
    count: "18,453",
    percentage: "+5.4%",
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    count: "15,876",
    percentage: "+18.2%",
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    count: "12,345",
    percentage: "+3.5%",
  },
  {
    id: 5,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    count: "8,532",
    percentage: "+7.1%",
  },
];

export function EventsList() {
  return (
    <div className="space-y-8">
      {events.map((event) => (
        <div key={event.id} className="flex items-center">
          <div className="flex h-9 w-9 items-center justify-center rounded-full bg-blue-100">
            <span className="text-sm font-medium text-blue-600">
              {event.id}
            </span>
          </div>
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{event.name}</p>
            <p className="text-sm text-muted-foreground">
              Toplam: {event.count}
            </p>
          </div>
          <div className="ml-auto font-medium text-green-600">
            {event.percentage}
          </div>
        </div>
      ))}
    </div>
  );
}
