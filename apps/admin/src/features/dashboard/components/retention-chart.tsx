"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  ResponsiveContainer,
} from "recharts";

const data = [
  {
    name: "1. <PERSON>ün",
    oran: 100,
  },
  {
    name: "3. <PERSON><PERSON><PERSON>",
    oran: 82,
  },
  {
    name: "7. <PERSON><PERSON><PERSON>",
    oran: 65,
  },
  {
    name: "14. <PERSON>ü<PERSON>",
    oran: 48,
  },
  {
    name: "30. <PERSON>ü<PERSON>",
    oran: 37,
  },
  {
    name: "60. <PERSON>ün",
    oran: 28,
  },
  {
    name: "90. Gün",
    oran: 22,
  },
];

export function RetentionChart() {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <LineChart
        data={data}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#888888" opacity={0.2} />
        <XAxis dataKey="name" stroke="#888888" />
        <YAxis stroke="#888888" tickFormatter={(value) => `${value}%`} />
        <Tooltip
          formatter={(value: number) => [`${value}%`, "Kullanıcı Tutma Oranı"]}
        />
        <Line
          type="monotone"
          dataKey="oran"
          stroke="#3b82f6"
          strokeWidth={2}
          dot={{ r: 4 }}
          activeDot={{ r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
