import { sidebarData } from "@/constants/sidebar-data";
import { Head<PERSON> } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@mass/shared/components/ui/card";
import { Activity, BarChart3, Mouse<PERSON>ointer, Users } from "lucide-react";
import { EventsList } from "./components/events-list";
import { BarChart } from "./components/bar-chart";
import { DeviceUsageChart } from "./components/device-usage-chart";
import { RetentionChart } from "./components/retention-chart";

export default function Dashboard() {
  return (
    <>
      <Header
        sidebarData={sidebarData}
        title="Genel Bakış"
        description="Genel bakış sayfası, sistemin genel durumu hakkında bilgi verir."
      />
      <Main>
        <div className="flex min-h-screen flex-col bg-background w-full">
          <main className="flex-1 space-y-4">
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Toplam Kullanıcı
                    </CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">8,453</div>
                    <p className="text-xs text-muted-foreground">
                      +14.2% geçen aydan
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Aktif Kullanıcılar
                    </CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">5,234</div>
                    <p className="text-xs text-muted-foreground">
                      +22.5% geçen aydan
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Ortalama Oturum Süresi
                    </CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">4.2 dk</div>
                    <p className="text-xs text-muted-foreground">
                      +8.1% geçen aydan
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Şu Anda Aktif
                    </CardTitle>
                    <MousePointer className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">573</div>
                    <p className="text-xs text-muted-foreground">
                      +201 son saatten
                    </p>
                  </CardContent>
                </Card>
              </div>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                <Card className="col-span-4">
                  <CardHeader>
                    <CardTitle>Aylık Kullanıcı Aktivitesi</CardTitle>
                  </CardHeader>
                  <CardContent className="pl-2">
                    <BarChart />
                  </CardContent>
                </Card>
                <Card className="col-span-3">
                  <CardHeader>
                    <CardTitle>Popüler Analitik Olayları</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <EventsList />
                  </CardContent>
                </Card>
              </div>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                <Card className="col-span-3">
                  <CardHeader>
                    <CardTitle>Cihaz Kullanım Dağılımı</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <DeviceUsageChart />
                  </CardContent>
                </Card>
                <Card className="col-span-4">
                  <CardHeader>
                    <CardTitle>Kullanıcı Tutma Oranı</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RetentionChart />
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </Main>
    </>
  );
}
