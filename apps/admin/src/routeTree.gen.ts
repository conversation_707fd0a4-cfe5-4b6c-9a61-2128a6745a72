/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as AuthenticatedLogoutImport } from './routes/_authenticated/logout'
import { Route as authOtpImport } from './routes/(auth)/otp'
import { Route as authLoginImport } from './routes/(auth)/login'
import { Route as auth500Import } from './routes/(auth)/500'

// Create Virtual Routes

const errors503LazyImport = createFileRoute('/(errors)/503')()
const errors500LazyImport = createFileRoute('/(errors)/500')()
const errors404LazyImport = createFileRoute('/(errors)/404')()
const errors403LazyImport = createFileRoute('/(errors)/403')()
const errors401LazyImport = createFileRoute('/(errors)/401')()
const authResetPasswordLazyImport = createFileRoute('/(auth)/reset-password')()
const authForgotPasswordLazyImport = createFileRoute(
  '/(auth)/forgot-password',
)()
const AuthenticatedUserManagementIndexLazyImport = createFileRoute(
  '/_authenticated/user-management/',
)()
const AuthenticatedSettingsIndexLazyImport = createFileRoute(
  '/_authenticated/settings/',
)()
const AuthenticatedDocumentManagementIndexLazyImport = createFileRoute(
  '/_authenticated/document-management/',
)()
const AuthenticatedDashboardIndexLazyImport = createFileRoute(
  '/_authenticated/dashboard/',
)()
const AuthenticatedComplaintManagementIndexLazyImport = createFileRoute(
  '/_authenticated/complaint-management/',
)()
const AuthenticatedSettingsParameterSettingsLazyImport = createFileRoute(
  '/_authenticated/settings/parameter-settings',
)()
const AuthenticatedSettingsPageSettingsLazyImport = createFileRoute(
  '/_authenticated/settings/page-settings',
)()
const AuthenticatedSettingsAccountSettingsLazyImport = createFileRoute(
  '/_authenticated/settings/account-settings',
)()
const AuthenticatedNotificationManagementSettingsLazyImport = createFileRoute(
  '/_authenticated/notification-management/settings',
)()
const AuthenticatedNotificationManagementHistoryLazyImport = createFileRoute(
  '/_authenticated/notification-management/history',
)()
const AuthenticatedDocumentManagementFaqLazyImport = createFileRoute(
  '/_authenticated/document-management/faq',
)()
const AuthenticatedDocumentManagementEdasInfoLazyImport = createFileRoute(
  '/_authenticated/document-management/edas-info',
)()
const AuthenticatedDocumentManagementAgreementGuidelinesLazyImport =
  createFileRoute('/_authenticated/document-management/agreement-guidelines')()

// Create/Update Routes

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errors503LazyRoute = errors503LazyImport
  .update({
    id: '/(errors)/503',
    path: '/503',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/503.lazy').then((d) => d.Route))

const errors500LazyRoute = errors500LazyImport
  .update({
    id: '/(errors)/500',
    path: '/500',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/500.lazy').then((d) => d.Route))

const errors404LazyRoute = errors404LazyImport
  .update({
    id: '/(errors)/404',
    path: '/404',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/404.lazy').then((d) => d.Route))

const errors403LazyRoute = errors403LazyImport
  .update({
    id: '/(errors)/403',
    path: '/403',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/403.lazy').then((d) => d.Route))

const errors401LazyRoute = errors401LazyImport
  .update({
    id: '/(errors)/401',
    path: '/401',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() => import('./routes/(errors)/401.lazy').then((d) => d.Route))

const authResetPasswordLazyRoute = authResetPasswordLazyImport
  .update({
    id: '/(auth)/reset-password',
    path: '/reset-password',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() =>
    import('./routes/(auth)/reset-password.lazy').then((d) => d.Route),
  )

const authForgotPasswordLazyRoute = authForgotPasswordLazyImport
  .update({
    id: '/(auth)/forgot-password',
    path: '/forgot-password',
    getParentRoute: () => rootRoute,
  } as any)
  .lazy(() =>
    import('./routes/(auth)/forgot-password.lazy').then((d) => d.Route),
  )

const AuthenticatedLogoutRoute = AuthenticatedLogoutImport.update({
  id: '/logout',
  path: '/logout',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const authOtpRoute = authOtpImport.update({
  id: '/(auth)/otp',
  path: '/otp',
  getParentRoute: () => rootRoute,
} as any)

const authLoginRoute = authLoginImport.update({
  id: '/(auth)/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const auth500Route = auth500Import.update({
  id: '/(auth)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedUserManagementIndexLazyRoute =
  AuthenticatedUserManagementIndexLazyImport.update({
    id: '/user-management/',
    path: '/user-management/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/user-management/index.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedSettingsIndexLazyRoute =
  AuthenticatedSettingsIndexLazyImport.update({
    id: '/settings/',
    path: '/settings/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/settings/index.lazy').then((d) => d.Route),
  )

const AuthenticatedDocumentManagementIndexLazyRoute =
  AuthenticatedDocumentManagementIndexLazyImport.update({
    id: '/document-management/',
    path: '/document-management/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/document-management/index.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedDashboardIndexLazyRoute =
  AuthenticatedDashboardIndexLazyImport.update({
    id: '/dashboard/',
    path: '/dashboard/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/dashboard/index.lazy').then((d) => d.Route),
  )

const AuthenticatedComplaintManagementIndexLazyRoute =
  AuthenticatedComplaintManagementIndexLazyImport.update({
    id: '/complaint-management/',
    path: '/complaint-management/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/complaint-management/index.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedSettingsParameterSettingsLazyRoute =
  AuthenticatedSettingsParameterSettingsLazyImport.update({
    id: '/settings/parameter-settings',
    path: '/settings/parameter-settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/settings/parameter-settings.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedSettingsPageSettingsLazyRoute =
  AuthenticatedSettingsPageSettingsLazyImport.update({
    id: '/settings/page-settings',
    path: '/settings/page-settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/settings/page-settings.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedSettingsAccountSettingsLazyRoute =
  AuthenticatedSettingsAccountSettingsLazyImport.update({
    id: '/settings/account-settings',
    path: '/settings/account-settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/settings/account-settings.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedNotificationManagementSettingsLazyRoute =
  AuthenticatedNotificationManagementSettingsLazyImport.update({
    id: '/notification-management/settings',
    path: '/notification-management/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import(
      './routes/_authenticated/notification-management/settings.lazy'
    ).then((d) => d.Route),
  )

const AuthenticatedNotificationManagementHistoryLazyRoute =
  AuthenticatedNotificationManagementHistoryLazyImport.update({
    id: '/notification-management/history',
    path: '/notification-management/history',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/notification-management/history.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedDocumentManagementFaqLazyRoute =
  AuthenticatedDocumentManagementFaqLazyImport.update({
    id: '/document-management/faq',
    path: '/document-management/faq',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/document-management/faq.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedDocumentManagementEdasInfoLazyRoute =
  AuthenticatedDocumentManagementEdasInfoLazyImport.update({
    id: '/document-management/edas-info',
    path: '/document-management/edas-info',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import('./routes/_authenticated/document-management/edas-info.lazy').then(
      (d) => d.Route,
    ),
  )

const AuthenticatedDocumentManagementAgreementGuidelinesLazyRoute =
  AuthenticatedDocumentManagementAgreementGuidelinesLazyImport.update({
    id: '/document-management/agreement-guidelines',
    path: '/document-management/agreement-guidelines',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any).lazy(() =>
    import(
      './routes/_authenticated/document-management/agreement-guidelines.lazy'
    ).then((d) => d.Route),
  )

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/500': {
      id: '/(auth)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof auth500Import
      parentRoute: typeof rootRoute
    }
    '/(auth)/login': {
      id: '/(auth)/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof authLoginImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/otp': {
      id: '/(auth)/otp'
      path: '/otp'
      fullPath: '/otp'
      preLoaderRoute: typeof authOtpImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/logout': {
      id: '/_authenticated/logout'
      path: '/logout'
      fullPath: '/logout'
      preLoaderRoute: typeof AuthenticatedLogoutImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/(auth)/forgot-password': {
      id: '/(auth)/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof authForgotPasswordLazyImport
      parentRoute: typeof rootRoute
    }
    '/(auth)/reset-password': {
      id: '/(auth)/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof authResetPasswordLazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401LazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403LazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404LazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500LazyImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503LazyImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/document-management/agreement-guidelines': {
      id: '/_authenticated/document-management/agreement-guidelines'
      path: '/document-management/agreement-guidelines'
      fullPath: '/document-management/agreement-guidelines'
      preLoaderRoute: typeof AuthenticatedDocumentManagementAgreementGuidelinesLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/document-management/edas-info': {
      id: '/_authenticated/document-management/edas-info'
      path: '/document-management/edas-info'
      fullPath: '/document-management/edas-info'
      preLoaderRoute: typeof AuthenticatedDocumentManagementEdasInfoLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/document-management/faq': {
      id: '/_authenticated/document-management/faq'
      path: '/document-management/faq'
      fullPath: '/document-management/faq'
      preLoaderRoute: typeof AuthenticatedDocumentManagementFaqLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/notification-management/history': {
      id: '/_authenticated/notification-management/history'
      path: '/notification-management/history'
      fullPath: '/notification-management/history'
      preLoaderRoute: typeof AuthenticatedNotificationManagementHistoryLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/notification-management/settings': {
      id: '/_authenticated/notification-management/settings'
      path: '/notification-management/settings'
      fullPath: '/notification-management/settings'
      preLoaderRoute: typeof AuthenticatedNotificationManagementSettingsLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/account-settings': {
      id: '/_authenticated/settings/account-settings'
      path: '/settings/account-settings'
      fullPath: '/settings/account-settings'
      preLoaderRoute: typeof AuthenticatedSettingsAccountSettingsLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/page-settings': {
      id: '/_authenticated/settings/page-settings'
      path: '/settings/page-settings'
      fullPath: '/settings/page-settings'
      preLoaderRoute: typeof AuthenticatedSettingsPageSettingsLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/parameter-settings': {
      id: '/_authenticated/settings/parameter-settings'
      path: '/settings/parameter-settings'
      fullPath: '/settings/parameter-settings'
      preLoaderRoute: typeof AuthenticatedSettingsParameterSettingsLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/complaint-management/': {
      id: '/_authenticated/complaint-management/'
      path: '/complaint-management'
      fullPath: '/complaint-management'
      preLoaderRoute: typeof AuthenticatedComplaintManagementIndexLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/dashboard/': {
      id: '/_authenticated/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthenticatedDashboardIndexLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/document-management/': {
      id: '/_authenticated/document-management/'
      path: '/document-management'
      fullPath: '/document-management'
      preLoaderRoute: typeof AuthenticatedDocumentManagementIndexLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsIndexLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/user-management/': {
      id: '/_authenticated/user-management/'
      path: '/user-management'
      fullPath: '/user-management'
      preLoaderRoute: typeof AuthenticatedUserManagementIndexLazyImport
      parentRoute: typeof AuthenticatedRouteImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedRouteRouteChildren {
  AuthenticatedLogoutRoute: typeof AuthenticatedLogoutRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedDocumentManagementAgreementGuidelinesLazyRoute: typeof AuthenticatedDocumentManagementAgreementGuidelinesLazyRoute
  AuthenticatedDocumentManagementEdasInfoLazyRoute: typeof AuthenticatedDocumentManagementEdasInfoLazyRoute
  AuthenticatedDocumentManagementFaqLazyRoute: typeof AuthenticatedDocumentManagementFaqLazyRoute
  AuthenticatedNotificationManagementHistoryLazyRoute: typeof AuthenticatedNotificationManagementHistoryLazyRoute
  AuthenticatedNotificationManagementSettingsLazyRoute: typeof AuthenticatedNotificationManagementSettingsLazyRoute
  AuthenticatedSettingsAccountSettingsLazyRoute: typeof AuthenticatedSettingsAccountSettingsLazyRoute
  AuthenticatedSettingsPageSettingsLazyRoute: typeof AuthenticatedSettingsPageSettingsLazyRoute
  AuthenticatedSettingsParameterSettingsLazyRoute: typeof AuthenticatedSettingsParameterSettingsLazyRoute
  AuthenticatedComplaintManagementIndexLazyRoute: typeof AuthenticatedComplaintManagementIndexLazyRoute
  AuthenticatedDashboardIndexLazyRoute: typeof AuthenticatedDashboardIndexLazyRoute
  AuthenticatedDocumentManagementIndexLazyRoute: typeof AuthenticatedDocumentManagementIndexLazyRoute
  AuthenticatedSettingsIndexLazyRoute: typeof AuthenticatedSettingsIndexLazyRoute
  AuthenticatedUserManagementIndexLazyRoute: typeof AuthenticatedUserManagementIndexLazyRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedLogoutRoute: AuthenticatedLogoutRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedDocumentManagementAgreementGuidelinesLazyRoute:
    AuthenticatedDocumentManagementAgreementGuidelinesLazyRoute,
  AuthenticatedDocumentManagementEdasInfoLazyRoute:
    AuthenticatedDocumentManagementEdasInfoLazyRoute,
  AuthenticatedDocumentManagementFaqLazyRoute:
    AuthenticatedDocumentManagementFaqLazyRoute,
  AuthenticatedNotificationManagementHistoryLazyRoute:
    AuthenticatedNotificationManagementHistoryLazyRoute,
  AuthenticatedNotificationManagementSettingsLazyRoute:
    AuthenticatedNotificationManagementSettingsLazyRoute,
  AuthenticatedSettingsAccountSettingsLazyRoute:
    AuthenticatedSettingsAccountSettingsLazyRoute,
  AuthenticatedSettingsPageSettingsLazyRoute:
    AuthenticatedSettingsPageSettingsLazyRoute,
  AuthenticatedSettingsParameterSettingsLazyRoute:
    AuthenticatedSettingsParameterSettingsLazyRoute,
  AuthenticatedComplaintManagementIndexLazyRoute:
    AuthenticatedComplaintManagementIndexLazyRoute,
  AuthenticatedDashboardIndexLazyRoute: AuthenticatedDashboardIndexLazyRoute,
  AuthenticatedDocumentManagementIndexLazyRoute:
    AuthenticatedDocumentManagementIndexLazyRoute,
  AuthenticatedSettingsIndexLazyRoute: AuthenticatedSettingsIndexLazyRoute,
  AuthenticatedUserManagementIndexLazyRoute:
    AuthenticatedUserManagementIndexLazyRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/500': typeof errors500LazyRoute
  '/login': typeof authLoginRoute
  '/otp': typeof authOtpRoute
  '/logout': typeof AuthenticatedLogoutRoute
  '/forgot-password': typeof authForgotPasswordLazyRoute
  '/reset-password': typeof authResetPasswordLazyRoute
  '/401': typeof errors401LazyRoute
  '/403': typeof errors403LazyRoute
  '/404': typeof errors404LazyRoute
  '/503': typeof errors503LazyRoute
  '/': typeof AuthenticatedIndexRoute
  '/document-management/agreement-guidelines': typeof AuthenticatedDocumentManagementAgreementGuidelinesLazyRoute
  '/document-management/edas-info': typeof AuthenticatedDocumentManagementEdasInfoLazyRoute
  '/document-management/faq': typeof AuthenticatedDocumentManagementFaqLazyRoute
  '/notification-management/history': typeof AuthenticatedNotificationManagementHistoryLazyRoute
  '/notification-management/settings': typeof AuthenticatedNotificationManagementSettingsLazyRoute
  '/settings/account-settings': typeof AuthenticatedSettingsAccountSettingsLazyRoute
  '/settings/page-settings': typeof AuthenticatedSettingsPageSettingsLazyRoute
  '/settings/parameter-settings': typeof AuthenticatedSettingsParameterSettingsLazyRoute
  '/complaint-management': typeof AuthenticatedComplaintManagementIndexLazyRoute
  '/dashboard': typeof AuthenticatedDashboardIndexLazyRoute
  '/document-management': typeof AuthenticatedDocumentManagementIndexLazyRoute
  '/settings': typeof AuthenticatedSettingsIndexLazyRoute
  '/user-management': typeof AuthenticatedUserManagementIndexLazyRoute
}

export interface FileRoutesByTo {
  '/500': typeof errors500LazyRoute
  '/login': typeof authLoginRoute
  '/otp': typeof authOtpRoute
  '/logout': typeof AuthenticatedLogoutRoute
  '/forgot-password': typeof authForgotPasswordLazyRoute
  '/reset-password': typeof authResetPasswordLazyRoute
  '/401': typeof errors401LazyRoute
  '/403': typeof errors403LazyRoute
  '/404': typeof errors404LazyRoute
  '/503': typeof errors503LazyRoute
  '/': typeof AuthenticatedIndexRoute
  '/document-management/agreement-guidelines': typeof AuthenticatedDocumentManagementAgreementGuidelinesLazyRoute
  '/document-management/edas-info': typeof AuthenticatedDocumentManagementEdasInfoLazyRoute
  '/document-management/faq': typeof AuthenticatedDocumentManagementFaqLazyRoute
  '/notification-management/history': typeof AuthenticatedNotificationManagementHistoryLazyRoute
  '/notification-management/settings': typeof AuthenticatedNotificationManagementSettingsLazyRoute
  '/settings/account-settings': typeof AuthenticatedSettingsAccountSettingsLazyRoute
  '/settings/page-settings': typeof AuthenticatedSettingsPageSettingsLazyRoute
  '/settings/parameter-settings': typeof AuthenticatedSettingsParameterSettingsLazyRoute
  '/complaint-management': typeof AuthenticatedComplaintManagementIndexLazyRoute
  '/dashboard': typeof AuthenticatedDashboardIndexLazyRoute
  '/document-management': typeof AuthenticatedDocumentManagementIndexLazyRoute
  '/settings': typeof AuthenticatedSettingsIndexLazyRoute
  '/user-management': typeof AuthenticatedUserManagementIndexLazyRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/(auth)/500': typeof auth500Route
  '/(auth)/login': typeof authLoginRoute
  '/(auth)/otp': typeof authOtpRoute
  '/_authenticated/logout': typeof AuthenticatedLogoutRoute
  '/(auth)/forgot-password': typeof authForgotPasswordLazyRoute
  '/(auth)/reset-password': typeof authResetPasswordLazyRoute
  '/(errors)/401': typeof errors401LazyRoute
  '/(errors)/403': typeof errors403LazyRoute
  '/(errors)/404': typeof errors404LazyRoute
  '/(errors)/500': typeof errors500LazyRoute
  '/(errors)/503': typeof errors503LazyRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/document-management/agreement-guidelines': typeof AuthenticatedDocumentManagementAgreementGuidelinesLazyRoute
  '/_authenticated/document-management/edas-info': typeof AuthenticatedDocumentManagementEdasInfoLazyRoute
  '/_authenticated/document-management/faq': typeof AuthenticatedDocumentManagementFaqLazyRoute
  '/_authenticated/notification-management/history': typeof AuthenticatedNotificationManagementHistoryLazyRoute
  '/_authenticated/notification-management/settings': typeof AuthenticatedNotificationManagementSettingsLazyRoute
  '/_authenticated/settings/account-settings': typeof AuthenticatedSettingsAccountSettingsLazyRoute
  '/_authenticated/settings/page-settings': typeof AuthenticatedSettingsPageSettingsLazyRoute
  '/_authenticated/settings/parameter-settings': typeof AuthenticatedSettingsParameterSettingsLazyRoute
  '/_authenticated/complaint-management/': typeof AuthenticatedComplaintManagementIndexLazyRoute
  '/_authenticated/dashboard/': typeof AuthenticatedDashboardIndexLazyRoute
  '/_authenticated/document-management/': typeof AuthenticatedDocumentManagementIndexLazyRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexLazyRoute
  '/_authenticated/user-management/': typeof AuthenticatedUserManagementIndexLazyRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/500'
    | '/login'
    | '/otp'
    | '/logout'
    | '/forgot-password'
    | '/reset-password'
    | '/401'
    | '/403'
    | '/404'
    | '/503'
    | '/'
    | '/document-management/agreement-guidelines'
    | '/document-management/edas-info'
    | '/document-management/faq'
    | '/notification-management/history'
    | '/notification-management/settings'
    | '/settings/account-settings'
    | '/settings/page-settings'
    | '/settings/parameter-settings'
    | '/complaint-management'
    | '/dashboard'
    | '/document-management'
    | '/settings'
    | '/user-management'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/500'
    | '/login'
    | '/otp'
    | '/logout'
    | '/forgot-password'
    | '/reset-password'
    | '/401'
    | '/403'
    | '/404'
    | '/503'
    | '/'
    | '/document-management/agreement-guidelines'
    | '/document-management/edas-info'
    | '/document-management/faq'
    | '/notification-management/history'
    | '/notification-management/settings'
    | '/settings/account-settings'
    | '/settings/page-settings'
    | '/settings/parameter-settings'
    | '/complaint-management'
    | '/dashboard'
    | '/document-management'
    | '/settings'
    | '/user-management'
  id:
    | '__root__'
    | '/_authenticated'
    | '/(auth)/500'
    | '/(auth)/login'
    | '/(auth)/otp'
    | '/_authenticated/logout'
    | '/(auth)/forgot-password'
    | '/(auth)/reset-password'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/'
    | '/_authenticated/document-management/agreement-guidelines'
    | '/_authenticated/document-management/edas-info'
    | '/_authenticated/document-management/faq'
    | '/_authenticated/notification-management/history'
    | '/_authenticated/notification-management/settings'
    | '/_authenticated/settings/account-settings'
    | '/_authenticated/settings/page-settings'
    | '/_authenticated/settings/parameter-settings'
    | '/_authenticated/complaint-management/'
    | '/_authenticated/dashboard/'
    | '/_authenticated/document-management/'
    | '/_authenticated/settings/'
    | '/_authenticated/user-management/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  auth500Route: typeof auth500Route
  authLoginRoute: typeof authLoginRoute
  authOtpRoute: typeof authOtpRoute
  authForgotPasswordLazyRoute: typeof authForgotPasswordLazyRoute
  authResetPasswordLazyRoute: typeof authResetPasswordLazyRoute
  errors401LazyRoute: typeof errors401LazyRoute
  errors403LazyRoute: typeof errors403LazyRoute
  errors404LazyRoute: typeof errors404LazyRoute
  errors500LazyRoute: typeof errors500LazyRoute
  errors503LazyRoute: typeof errors503LazyRoute
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  auth500Route: auth500Route,
  authLoginRoute: authLoginRoute,
  authOtpRoute: authOtpRoute,
  authForgotPasswordLazyRoute: authForgotPasswordLazyRoute,
  authResetPasswordLazyRoute: authResetPasswordLazyRoute,
  errors401LazyRoute: errors401LazyRoute,
  errors403LazyRoute: errors403LazyRoute,
  errors404LazyRoute: errors404LazyRoute,
  errors500LazyRoute: errors500LazyRoute,
  errors503LazyRoute: errors503LazyRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/(auth)/500",
        "/(auth)/login",
        "/(auth)/otp",
        "/(auth)/forgot-password",
        "/(auth)/reset-password",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/logout",
        "/_authenticated/",
        "/_authenticated/document-management/agreement-guidelines",
        "/_authenticated/document-management/edas-info",
        "/_authenticated/document-management/faq",
        "/_authenticated/notification-management/history",
        "/_authenticated/notification-management/settings",
        "/_authenticated/settings/account-settings",
        "/_authenticated/settings/page-settings",
        "/_authenticated/settings/parameter-settings",
        "/_authenticated/complaint-management/",
        "/_authenticated/dashboard/",
        "/_authenticated/document-management/",
        "/_authenticated/settings/",
        "/_authenticated/user-management/"
      ]
    },
    "/(auth)/500": {
      "filePath": "(auth)/500.tsx"
    },
    "/(auth)/login": {
      "filePath": "(auth)/login.tsx"
    },
    "/(auth)/otp": {
      "filePath": "(auth)/otp.tsx"
    },
    "/_authenticated/logout": {
      "filePath": "_authenticated/logout.tsx",
      "parent": "/_authenticated"
    },
    "/(auth)/forgot-password": {
      "filePath": "(auth)/forgot-password.lazy.tsx"
    },
    "/(auth)/reset-password": {
      "filePath": "(auth)/reset-password.lazy.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.lazy.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.lazy.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.lazy.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.lazy.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.lazy.tsx"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/document-management/agreement-guidelines": {
      "filePath": "_authenticated/document-management/agreement-guidelines.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/document-management/edas-info": {
      "filePath": "_authenticated/document-management/edas-info.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/document-management/faq": {
      "filePath": "_authenticated/document-management/faq.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/notification-management/history": {
      "filePath": "_authenticated/notification-management/history.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/notification-management/settings": {
      "filePath": "_authenticated/notification-management/settings.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/account-settings": {
      "filePath": "_authenticated/settings/account-settings.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/page-settings": {
      "filePath": "_authenticated/settings/page-settings.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/parameter-settings": {
      "filePath": "_authenticated/settings/parameter-settings.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/complaint-management/": {
      "filePath": "_authenticated/complaint-management/index.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/dashboard/": {
      "filePath": "_authenticated/dashboard/index.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/document-management/": {
      "filePath": "_authenticated/document-management/index.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/": {
      "filePath": "_authenticated/settings/index.lazy.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/user-management/": {
      "filePath": "_authenticated/user-management/index.lazy.tsx",
      "parent": "/_authenticated"
    }
  }
}
ROUTE_MANIFEST_END */
