/**
 * @vitest-environment jsdom
 */
/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, cleanup } from '@testing-library/react';
import { Title } from './Title';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => (key === 'app_title' ? 'My App' : key),
  }),
}));

beforeEach(() => {
  cleanup();
  document.title = '';
});

describe('<Title />', () => {
  it('sets document.title to the explicit title prop', () => {
    render(<Title title="Custom Page" />);
    expect(document.title).toBe('Custom Page');
  });

  it('falls back to t("app_title") when no title prop is given', () => {
    render(<Title />);
    expect(document.title).toBe('My App');
  });
});
