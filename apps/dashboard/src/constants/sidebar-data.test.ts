import { describe, it, expect, vi } from 'vitest';
import { SidebarNumber } from '@/features/notifications/components/sidebar-number';

// Mock the sidebar-data module
vi.mock('./sidebar-data', () => ({
  sidebarData: {
    navGroups: [
      {
        titleKey: 'sidebar.groups.general',
        items: [
          {
            titleKey: 'sidebar.items.subscriptions',
            url: '/subscriptions',
            icon: 'untitled:building-02',
            disabledKey: 'subscriptions.view'
          },
          {
            titleKey: 'sidebar.items.notifications',
            url: '/notifications',
            icon: 'untitled:bell-01',
            badge: SidebarNumber,
            disabledKey: 'notifications.view'
          },
          {
            titleKey: 'sidebar.items.complaints',
            url: '/complaints-requests',
            icon: 'untitled:file-05',
            disabledKey: 'complaints.view'
          }
        ]
      },
      {
        titleKey: 'sidebar.groups.settings_help',
        items: [
          {
            titleKey: 'sidebar.items.settings',
            url: '/settings',
            icon: 'untitled:settings-02',
            disabledKey: 'settings.view'
          },
          {
            titleKey: 'sidebar.items.help_support',
            icon: 'untitled:life-buoy-01',
            items: [
              {
                titleKey: 'sidebar.items.faq',
                url: '/help-center/faq',
              }
            ]
          },
          {
            titleKey: 'sidebar.items.logout',
            url: '/logout',
            icon: 'untitled:log-out-01'
          }
        ]
      }
    ]
  }
}));

// Import the mocked sidebarData
import { sidebarData } from './sidebar-data';

describe('sidebarData', () => {
  it('should have the correct structure', () => {
    expect(sidebarData).toBeDefined();
    expect(sidebarData).toHaveProperty('navGroups');
    expect(sidebarData.navGroups).toBeDefined();
    if (sidebarData.navGroups) {
      expect(sidebarData.navGroups).toBeInstanceOf(Array);
      expect(sidebarData.navGroups).toHaveLength(2);
    }
  });

  it('should have correct general nav group', () => {
    const generalGroup = sidebarData.navGroups[0];
    expect(generalGroup.titleKey).toBe('sidebar.groups.general');
    expect(generalGroup.items).toHaveLength(3);
    
    const subscriptionsItem = generalGroup.items[0];
    expect(subscriptionsItem.titleKey).toBe('sidebar.items.subscriptions');
    expect(subscriptionsItem.url).toBe('/subscriptions');
    expect(subscriptionsItem.icon).toBe('untitled:building-02');
    expect(subscriptionsItem.disabledKey).toBe('subscriptions.view');

    const notificationsItem = generalGroup.items[1];
    expect(notificationsItem.titleKey).toBe('sidebar.items.notifications');
    expect(notificationsItem.url).toBe('/notifications');
    expect(notificationsItem.icon).toBe('untitled:bell-01');
    expect('badge' in notificationsItem && notificationsItem.badge).toBe(SidebarNumber);
    expect(notificationsItem.disabledKey).toBe('notifications.view');

    const complaintsItem = generalGroup.items[2];
    expect(complaintsItem.titleKey).toBe('sidebar.items.complaints');
    expect(complaintsItem.url).toBe('/complaints-requests');
    expect(complaintsItem.icon).toBe('untitled:file-05');
    expect(complaintsItem.disabledKey).toBe('complaints.view');
  });

  it('should have correct settings/help nav group', () => {
    const settingsGroup = sidebarData.navGroups[1];
    expect(settingsGroup.titleKey).toBe('sidebar.groups.settings_help');
    expect(settingsGroup.items).toHaveLength(3);

    const settingsItem = settingsGroup.items[0];
    expect(settingsItem.titleKey).toBe('sidebar.items.settings');
    expect(settingsItem.url).toBe('/settings');
    expect(settingsItem.icon).toBe('untitled:settings-02');
    expect(settingsItem.disabledKey).toBe('settings.view');

    const helpSupportItem = settingsGroup.items[1];
    expect(helpSupportItem.titleKey).toBe('sidebar.items.help_support');
    expect(helpSupportItem.icon).toBe('untitled:life-buoy-01');
    expect('items' in helpSupportItem && helpSupportItem.items).toHaveLength(1);
    expect('items' in helpSupportItem && helpSupportItem.items![0].titleKey).toBe('sidebar.items.faq');
    expect('items' in helpSupportItem && helpSupportItem.items![0].url).toBe('/help-center/faq');

    const logoutItem = settingsGroup.items[2];
    expect(logoutItem.titleKey).toBe('sidebar.items.logout');
    expect(logoutItem.url).toBe('/logout');
    expect(logoutItem.icon).toBe('untitled:log-out-01');
  });

  it('should export valid sidebar data structure', () => {
    expect(sidebarData).toMatchObject({
      navGroups: expect.arrayContaining([
        expect.objectContaining({
          titleKey: expect.any(String),
          items: expect.any(Array)
        })
      ])
    });
  });
});
