import { SidebarNumber } from "@/features/notifications/components/sidebar-number";
import type { SidebarData } from "@mass/shared/components/organisms/layout/types";
import { LinkProps } from "@tanstack/react-router";

export const sidebarData = {
  navGroups: [
    {
      titleKey: "sidebar.groups.general",
      items: [
        {
          titleKey: "sidebar.items.subscriptions",
          url: "/subscriptions",
          icon: "untitled:building-02",
          disabledKey: "subscriptions.view"
        },
        {
          titleKey: "sidebar.items.notifications",
          url: "/notifications",
          icon: "untitled:bell-01",
          badge: SidebarNumber,
          disabledKey: "notifications.view"
        },
        {
          titleKey: "sidebar.items.complaints",
          url: "/complaints-requests",
          icon: "untitled:file-05",
          disabledKey: "complaints.view"
        },
      ],
    },
    {
      titleKey: "sidebar.groups.settings_help",
      items: [
        {
          titleKey: "sidebar.items.settings",
          url: "/settings",
          icon: "untitled:settings-02",
          disabled<PERSON>ey: "settings.view"
        },
        {
          titleKey: "sidebar.items.help_support",
          icon: "untitled:life-buoy-01",
          items: [
            {
              titleKey: "sidebar.items.faq",
              url: "/help-center/faq" as LinkProps["to"],
            },
          ],
        },
        {
          titleKey: "sidebar.items.logout",
          icon: "untitled:log-out-01",
          url: "/logout",
        },
      ],
    },
  ],
};
