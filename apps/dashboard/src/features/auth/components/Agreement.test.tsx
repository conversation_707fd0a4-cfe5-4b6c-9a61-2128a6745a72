/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AgreementModal from './Agreement';
import { toast } from 'sonner';

// Mock react-pdf
vi.mock('react-pdf', () => ({
  Document: ({ children, onLoadSuccess, onLoadError, loading }: any) => (
    <div data-testid="pdf-document">
      {loading}
      <button onClick={() => onLoadSuccess({ numPages: 3 })}>Load Success</button>
      <button onClick={() => onLoadError(new Error('Load Error'))}>Load Error</button>
      {children}
    </div>
  ),
  Page: ({ pageNumber, onRenderSuccess }: any) => (
    <div data-testid={`pdf-page-${pageNumber}`}>
      Page {pageNumber}
      <button onClick={onRenderSuccess}>Render Page {pageNumber}</button>
    </div>
  ),
  pdfjs: {
    GlobalWorkerOptions: { workerSrc: '' },
    version: '2.0.0',
  },
}));

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
  },
}));

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

describe('AgreementModal', () => {
  const mockOnApprove = vi.fn();
  const mockOnHide = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderModal = (props = {}) => {
    return render(
      <AgreementModal
        url="test.pdf"
        onApprove={mockOnApprove}
        onHide={mockOnHide}
        show={true}
        disableClose={false}
        {...props}
      />
    );
  };

  it('should render PDF document', () => {
    renderModal();
    expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
  });

  it('should handle PDF load success', async () => {
    renderModal();
    
    const loadSuccessButton = screen.getByText('Load Success');
    fireEvent.click(loadSuccessButton);

    await waitFor(() => {
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-2')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-3')).toBeInTheDocument();
    });
  });

  it('should handle PDF load error', async () => {
    renderModal();
    
    const loadErrorButton = screen.getByText('Load Error');
    fireEvent.click(loadErrorButton);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('auth.register.pdfError');
      expect(mockOnHide).toHaveBeenCalled();
    });
  });

  it('should track viewed pages', async () => {
    renderModal();
    
    fireEvent.click(screen.getByText('Load Success'));
    
    await waitFor(() => {
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
    });

    const page1RenderButton = screen.getByText('Render Page 1');
    fireEvent.click(page1RenderButton);

    const approveButton = screen.getByRole('button', { name: /auth.register.scrollToEnd/i });
    expect(approveButton).toBeDisabled();
  });

  it('should enable approve button when scrolled to bottom', async () => {
    const { container } = renderModal();
    
    fireEvent.click(screen.getByText('Load Success'));
    
    const scrollContainer = container.querySelector('.overflow-auto');
    if (scrollContainer) {
      // Simulate scroll to bottom
      Object.defineProperty(scrollContainer, 'scrollTop', { value: 1000, writable: true });
      Object.defineProperty(scrollContainer, 'clientHeight', { value: 500, writable: true });
      Object.defineProperty(scrollContainer, 'scrollHeight', { value: 1500, writable: true });
      
      fireEvent.scroll(scrollContainer);
    }

    await waitFor(() => {
      const approveButton = container.querySelector('.btn.btn-primary');
      expect(approveButton).not.toBeDisabled();
    });
  });

  it('should call onApprove and onHide when approved', async () => {
    const { container } = renderModal();
    
    fireEvent.click(screen.getByText('Load Success'));
    
    // Wait for pages to render
    await waitFor(() => {
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
    });

    // Render all pages
    fireEvent.click(screen.getByText('Render Page 1'));
    fireEvent.click(screen.getByText('Render Page 2'));
    fireEvent.click(screen.getByText('Render Page 3'));
    
    const scrollContainer = container.querySelector('.overflow-auto');
    if (scrollContainer) {
      Object.defineProperty(scrollContainer, 'scrollTop', { value: 1000, writable: true });
      Object.defineProperty(scrollContainer, 'clientHeight', { value: 500, writable: true });
      Object.defineProperty(scrollContainer, 'scrollHeight', { value: 1502, writable: true });
      
      fireEvent.scroll(scrollContainer);
    }

    // Wait for button to be enabled
    await waitFor(() => {
      const approveButton = container.querySelector('.btn.btn-primary') as HTMLButtonElement;
      expect(approveButton).not.toBeDisabled();
    });

    const approveButton = container.querySelector('.btn.btn-primary') as HTMLButtonElement;
    fireEvent.click(approveButton);

    expect(mockOnApprove).toHaveBeenCalled();
    expect(mockOnHide).toHaveBeenCalled();
  });

  it('should prevent closing when disableClose is true', () => {
    renderModal({ disableClose: true });
    
    const event = new KeyboardEvent('keydown', { key: 'Escape' });
    const preventDefaultSpy = vi.spyOn(event, 'preventDefault');
    
    window.dispatchEvent(event);
    
    expect(preventDefaultSpy).toHaveBeenCalled();
  });

  it('should handle beforeunload event', () => {
    renderModal();
    
    const event = new Event('beforeunload') as BeforeUnloadEvent;
    const preventDefaultSpy = vi.spyOn(event, 'preventDefault');
    
    window.dispatchEvent(event);
    
    expect(preventDefaultSpy).toHaveBeenCalled();
  });

  it('should handle approval errors', async () => {
    mockOnApprove.mockImplementation(() => {
      throw new Error('Approval error');
    });

    const { container } = renderModal();
    
    fireEvent.click(screen.getByText('Load Success'));
    
    // Wait for pages to render
    await waitFor(() => {
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
    });

    // Render all pages
    fireEvent.click(screen.getByText('Render Page 1'));
    fireEvent.click(screen.getByText('Render Page 2'));
    fireEvent.click(screen.getByText('Render Page 3'));
    
    const scrollContainer = container.querySelector('.overflow-auto');
    if (scrollContainer) {
      Object.defineProperty(scrollContainer, 'scrollTop', { value: 1000, writable: true });
      Object.defineProperty(scrollContainer, 'clientHeight', { value: 500, writable: true });
      Object.defineProperty(scrollContainer, 'scrollHeight', { value: 1502, writable: true });
      
      fireEvent.scroll(scrollContainer);
    }

    // Wait for button to be enabled
    await waitFor(() => {
      const approveButton = container.querySelector('.btn.btn-primary') as HTMLButtonElement;
      expect(approveButton).not.toBeDisabled();
    });

    const approveButton = container.querySelector('.btn.btn-primary') as HTMLButtonElement;
    fireEvent.click(approveButton);

    expect(toast.error).toHaveBeenCalledWith({ defaultValue: 'Onaylama işlemi sırasında bir hata oluştu' });
    expect(mockOnHide).toHaveBeenCalled();
  });
});
