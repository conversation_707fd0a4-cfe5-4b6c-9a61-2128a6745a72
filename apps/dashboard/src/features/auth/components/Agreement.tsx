import { Button } from "@mass/shared/components/ui/button";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Document, Page, pdfjs } from "react-pdf";
import { toast } from "sonner";

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

interface AgreementModalProps {
  url: string;
  onApprove: () => void;
  onHide: () => void;
  show: boolean;
  disableClose?: boolean;
}

export default function AgreementModal({
  url,
  onApprove,
  onHide,
  show,
  disableClose,
}: AgreementModalProps) {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [viewedPages, setViewedPages] = useState<boolean[]>([]);
  const [approved, setApproved] = useState(false);
  const [hasError, setHasError] = useState(false);
  const scrollRef = React.useRef<HTMLDivElement>(null);
  const [scrolledToBottom, setScrolledToBottom] = useState(false);

  const { t } = useTranslation("common");

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setViewedPages(Array(numPages).fill(false));
    const el = scrollRef.current;
    if (!el) return;
    handleScroll();
  };

  const handlePageRender = (index: number) => {
    if (hasError) return;
    
    setViewedPages((prev) => {
      if (!prev[index]) {
        const updated = [...prev];
        updated[index] = true;
        return updated;
      }
      return prev;
    });
  };

  const handleScroll = () => {
    const el = scrollRef.current;
    if (!el) return;
    setTimeout(() => {
      if (el.scrollTop + el.clientHeight >= el.scrollHeight - 2) {
        setScrolledToBottom(true);
      }
    }, 100);
  };

  const allPagesViewed =
    numPages && viewedPages.length === numPages && viewedPages.every(Boolean);

  React.useEffect(() => {
    if (!show) return;
    const beforeUnload = (e: BeforeUnloadEvent) => {
      if (!approved) {
        e.preventDefault();
        e.returnValue = "";
        return "";
      }
    };
    window.addEventListener("beforeunload", beforeUnload);
    return () => window.removeEventListener("beforeunload", beforeUnload);
  }, [approved, show]);

  React.useEffect(() => {
    if (!show) return;
    const handler = (e: KeyboardEvent) => {
      if (!approved && (e.key === "Escape" || e.keyCode === 27)) {
        e.preventDefault();
        e.stopPropagation();
      }
    };
    window.addEventListener("keydown", handler, true);
    return () => window.removeEventListener("keydown", handler, true);
  }, [approved, show]);

  return (
    <div className="w-full h-[80vh] bg-white relative flex flex-col items-center">
      <div
        className="flex-1 overflow-auto w-full flex flex-col items-center"
        ref={scrollRef}
        onScroll={handleScroll}
      >
        <Document
          file={url}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={(error) => {
            console.error("PDF load error:", error);
            setHasError(true);
            toast.error(t("auth.register.pdfError"));
            setTimeout(() => {
              onHide();
            }, 500);
          }}
          loading={<div className="text-center p-4">{t("loading")}</div>}
          className="flex flex-col"
        >
          {Array.from(new Array(numPages || 0), (el, index) => (
            <Page
              key={`page_${index + 1}`}
              pageNumber={index + 1}
              width={700}
              onRenderSuccess={() => handlePageRender(index)}
            />
          ))}
        </Document>
      </div>
      <div className="p-4 border-t bg-white flex flex-col gap-2 w-full max-w-2xl mx-auto">
        <Button
          className="btn btn-primary w-full disabled:opacity-50"
          disabled={approved || !scrolledToBottom || hasError}
          onClick={() => {
            if (hasError) return;
            
            setApproved(true);
            try {
              onApprove();
              onHide();
            } catch (error) {
              toast.error(t("auth.register.approveError", { defaultValue: "Onaylama işlemi sırasında bir hata oluştu" }));
              onHide();
            }
          }}
        >
          {allPagesViewed && scrolledToBottom
            ? t("auth.register.approveButton")
            : t("auth.register.scrollToEnd")}
        </Button>
      </div>
    </div>
  );
}
