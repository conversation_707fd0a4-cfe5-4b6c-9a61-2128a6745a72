/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import '@testing-library/jest-dom';
import AgreementWrapper from './AgreementWrapper';
import * as globalSettingsModule from '@mass/shared/hooks/use-global-settings';

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

const mockOpen = vi.fn();
const mockSafeTryClose = vi.fn();
vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: () => ({
    open: mockOpen,
    safeTryClose: mockSafeTryClose,
    modals: [],
    hide: vi.fn(),
    remove: vi.fn(),
    patch: vi.fn(),
  }),
}));

vi.mock('@mass/shared/hooks/use-global-settings', () => ({
  globalSettingsKeys: {
    documents: {
      pdf: () => 'documents.pdf',
    },
  },
  useGlobalSettings: vi.fn(),
}));

vi.mock('../../../services/api', () => ({
  default: vi.fn(),
}));

vi.mock('./Agreement', () => ({
  default: () => <div data-testid="agreement-modal">Agreement Modal</div>,
}));

describe('AgreementWrapper', () => {
  let queryClient: QueryClient;
  let mockApi: ReturnType<typeof vi.fn>;

  beforeEach(async () => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false, gcTime: 0 },
        mutations: { retry: false },
      },
    });

    mockOpen.mockClear();
    mockSafeTryClose.mockClear();
    
    const apiModule = await import('../../../services/api');
    mockApi = apiModule.default as any;
    vi.clearAllMocks();
  });

  const renderWrapper = (children = <div data-testid="child">Child Component</div>) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AgreementWrapper>{children}</AgreementWrapper>
      </QueryClientProvider>
    );
  };

  it('should render children', () => {
    vi.mocked(globalSettingsModule.useGlobalSettings).mockReturnValue({
      data: null,
      isLoading: true,
    } as any);

    renderWrapper();
    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('should not open modals when agreements are already accepted', async () => {
    vi.mocked(globalSettingsModule.useGlobalSettings).mockReturnValue({
      data: {
        value: {
          kvkk: { url: 'kvkk.pdf' },
          agreement: { url: 'agreement.pdf' },
        },
      },
      isLoading: false,
    } as any);

    mockApi.mockImplementation((endpoint: string) => {
      if (endpoint === '/setting/user/agreements.kvkk') {
        return Promise.resolve({ value: true });
      }
      if (endpoint === '/setting/user/agreements.user-agreement') {
        return Promise.resolve({ value: true });
      }
      return Promise.resolve({ success: true });
    });

    renderWrapper();

    await waitFor(() => {
      expect(mockOpen).not.toHaveBeenCalled();
    });
  });

  it('should open KVKK modal when not accepted', async () => {
    vi.mocked(globalSettingsModule.useGlobalSettings).mockReturnValue({
      data: {
        value: {
          kvkk: { url: 'kvkk.pdf' },
          agreement: { url: 'agreement.pdf' },
        },
      },
      isLoading: false,
    } as any);

    mockApi.mockImplementation((endpoint: string) => {
      if (endpoint === '/setting/user/agreements.kvkk') {
        return Promise.resolve({ value: false });
      }
      if (endpoint === '/setting/user/agreements.user-agreement') {
        return Promise.resolve({ value: true });
      }
      return Promise.resolve({ success: true });
    });

    renderWrapper();

    await waitFor(() => {
      expect(mockOpen).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining({
          name: 'pdf-modal-kvkk',
          url: 'kvkk.pdf',
          disableClose: true,
        })
      );
    });
  });

  it('should open user agreement modal when not accepted', async () => {
    vi.mocked(globalSettingsModule.useGlobalSettings).mockReturnValue({
      data: {
        value: {
          kvkk: { url: 'kvkk.pdf' },
          agreement: { url: 'agreement.pdf' },
        },
      },
      isLoading: false,
    } as any);

    mockApi.mockImplementation((endpoint: string) => {
      if (endpoint === '/setting/user/agreements.kvkk') {
        return Promise.resolve({ value: true });
      }
      if (endpoint === '/setting/user/agreements.user-agreement') {
        return Promise.resolve({ value: false });
      }
      return Promise.resolve({ success: true });
    });

    renderWrapper();

    await waitFor(() => {
      expect(mockOpen).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining({
          name: 'pdf-modal-sozlesme',
          url: 'agreement.pdf',
          disableClose: true,
        })
      );
    });
  });

  it('should handle API errors gracefully', async () => {
    vi.mocked(globalSettingsModule.useGlobalSettings).mockReturnValue({
      data: {
        value: {
          kvkk: { url: 'kvkk.pdf' },
          agreement: { url: 'agreement.pdf' },
        },
      },
      isLoading: false,
    } as any);

    mockApi.mockRejectedValue(new Error('API Error'));

    renderWrapper();

    await waitFor(() => {
      expect(mockOpen).not.toHaveBeenCalled();
    });
  });

  it('should not check agreements when data is loading', () => {
    vi.mocked(globalSettingsModule.useGlobalSettings).mockReturnValue({
      data: {
        value: {
          kvkk: { url: 'kvkk.pdf' },
          agreement: { url: 'agreement.pdf' },
        },
      },
      isLoading: true,
    } as any);

    renderWrapper();

    expect(mockOpen).not.toHaveBeenCalled();
  });
});
