import { useEffect, useMemo, useRef } from "react";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { useTranslation } from "react-i18next";
import AgreementModal from "./Agreement";
import {
    globalSettingsKeys,
    useGlobalSettings,
} from "@mass/shared/hooks/use-global-settings";
import api from "../../../services/api";
import { useMutation, useQueries, useQueryClient } from "@tanstack/react-query";

interface AgreementsData {
    'agreements.kvkk'?: boolean;
    'agreements.user-agreement'?: boolean;
    [key: string]: any;
}

interface AgreementWrapperProps {
    children: React.ReactNode;
}

export default function AgreementWrapper({ children }: AgreementWrapperProps) {
    const { t } = useTranslation("common");
    const { open, safeTryClose } = useModal();
    const queryClient = useQueryClient();
    const checkedRef = useRef(false);

    const { data: pdfData, isLoading: isPdfLoading } = useGlobalSettings(globalSettingsKeys.documents.pdf(), api);

    const agreementsQueries = useQueries({
        queries: [
            {
                queryKey: ['userSettings', 'agreements.kvkk'],
                queryFn: async () => {
                    const res = await api('/setting/user/agreements.kvkk', { method: 'GET' });
                    return { 'agreements.kvkk': res.value };
                },
                staleTime: 60000,
                retry: 2,
            },
            {
                queryKey: ['userSettings', 'agreements.user-agreement'],
                queryFn: async () => {
                    const res = await api('/setting/user/agreements.user-agreement', { method: 'GET' });
                    return { 'agreements.user-agreement': res.value };
                },
                staleTime: 60000,
                retry: 2,
            },
        ],
    });

    const agreementsData = useMemo(() => {
        return agreementsQueries.every(query => query.status === 'success')
            ? agreementsQueries.reduce<AgreementsData>((acc, result) => {
                return { ...acc, ...(result.data || {}) };
              }, {})
            : {};
    }, [agreementsQueries]);

    const isLoading = useMemo(() => {
        const isAgreementsLoading = agreementsQueries.some(query => query.isLoading || query.isFetching);
        const areAllQueriesFetched = agreementsQueries.every(query => query.isFetched);
        return isPdfLoading || isAgreementsLoading || !areAllQueriesFetched;
    }, [isPdfLoading, agreementsQueries]);

    const isError = useMemo(() => {
        return agreementsQueries.some(query => query.isError);
    }, [agreementsQueries]);

    const updateKvkkMutation = useMutation({
        mutationFn: async () => {
            return await api('/setting/user/agreements.kvkk', {
                method: 'PATCH',
                body: JSON.stringify({ value: true }),
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['userSettings', 'agreements.kvkk'] });
        }
    });

    const updateUserAgreementMutation = useMutation({
        mutationFn: async () => {
            return await api('/setting/user/agreements.user-agreement', {
                method: 'PATCH',
                body: JSON.stringify({ value: true }),
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['userSettings', 'agreements.user-agreement'] });
        }
    });

    const shouldCheck = useMemo(() => {
        return !!(
            pdfData?.value &&
            !isLoading &&
            !isError &&
            agreementsData &&
            Object.keys(agreementsData).length > 0 &&
            !checkedRef.current
        );
    }, [pdfData?.value, isLoading, isError, agreementsData]);

    useEffect(() => {
        if (!shouldCheck) return;

        const checkAgreements = async () => {
            checkedRef.current = true;

            const kvkkValue = agreementsData?.['agreements.kvkk'];
            const sozlesmeValue = agreementsData?.['agreements.user-agreement'];
            
            const kvkkAccepted = kvkkValue === true || String(kvkkValue) === 'true';
            const sozlesmeAccepted = sozlesmeValue === true || String(sozlesmeValue) === 'true';

            const modalsToShow = [];

            if (!kvkkAccepted) {
                modalsToShow.push({
                    name: "pdf-modal-kvkk",
                    title: t("auth.register.kvkkTitle", { defaultValue: "KVKK Aydınlatma Metni" }),
                    description: t("auth.register.kvkkDescription", { defaultValue: "KVKK metnini okuduğunuzu onaylayın." }),
                    url: pdfData?.value?.kvkk?.url,
                    onApprove: () => {
                        return new Promise((resolve, reject) => {
                            updateKvkkMutation.mutate(undefined, {
                                onSuccess: () => {
                                    safeTryClose("pdf-modal-kvkk"); 
                                    resolve(undefined);
                                },
                                onError: (error) => reject(error)
                            });
                        });
                    }
                });
            }

            if (!sozlesmeAccepted) {
                modalsToShow.push({
                    name: "pdf-modal-sozlesme",
                    title: t("auth.register.userAgreementTitle", { defaultValue: "Kullanıcı Sözleşmesi" }),
                    description: t("auth.register.userAgreementDescription", { defaultValue: "Kullanıcı Sözleşmesi metnini okuduğunuzu onaylayın." }),
                    url: pdfData?.value?.agreement?.url,
                    onApprove: () => {
                        return new Promise((resolve, reject) => {
                            updateUserAgreementMutation.mutate(undefined, {
                                onSuccess: () => {
                                    safeTryClose("pdf-modal-sozlesme");
                                    resolve(undefined);
                                },
                                onError: (error) => reject(error)
                            });
                        });
                    }
                });
            }

            for (const modal of modalsToShow) {
                if (!modal.url) continue;

                await new Promise<void>((resolve, reject) => {
                    open(AgreementModal, {
                        name: modal.name,
                        size: "large",
                        header: {
                            title: modal.title,
                            description: modal.description,
                        },
                        url: modal.url,
                        disableClose: true,
                        onApprove: async () => {
                            try {
                                await modal.onApprove();
                                resolve();
                            } catch (error) {
                                reject(error);
                            }
                        },
                    });
                });

                await new Promise(resolve => setTimeout(resolve, 500));
            }
        };

        checkAgreements();
    }, [shouldCheck]);

    return children;
}
