/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, act } from '@testing-library/react';

// Mock all image imports before importing the component
vi.mock('@mass/shared/assets/auth/slider-1.png', () => ({ default: 'mocked-slider-1' }));
vi.mock('@mass/shared/assets/auth/slider-2.png', () => ({ default: 'mocked-slider-2' }));
vi.mock('@mass/shared/assets/auth/slider-3.png', () => ({ default: 'mocked-slider-3' }));

// Mock mobile images (only the ones actually used)
vi.mock('@mass/shared/assets/auth/slider-1-mobile.png', () => ({ default: 'mocked-slider-1-mobile' }));
vi.mock('@mass/shared/assets/auth/slider-2-mobile.png', () => ({ default: 'mocked-slider-2-mobile' }));
vi.mock('@mass/shared/assets/auth/slider-3-mobile.png', () => ({ default: 'mocked-slider-3-mobile' }));

// Import the component after all mocks are defined
import AuthLayout from './AuthLayout';

describe('AuthLayout Component', () => {
    beforeEach(() => {
        vi.useFakeTimers();
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.clearAllMocks();
    });

    it('renders the layout with children content', () => {
        render(<AuthLayout>Test Content</AuthLayout>);
        expect(screen.getByTestId('auth-layout')).toBeInTheDocument();
        expect(screen.getByTestId('logo')).toBeInTheDocument();
        expect(screen.getByTestId('children-container')).toHaveTextContent('Test Content');
    });

    it('displays the first slide (index 1) by default', () => {
        render(<AuthLayout>Test Content</AuthLayout>);
        
        const slide1 = screen.getByTestId('slide-1');
        expect(slide1.classList.contains('opacity-100')).toBeTruthy();
        
        // Check that the correct navigation dot is active
        const dot1 = screen.getByTestId('slide-dot-1');
        expect(dot1.classList.contains('bg-slate-950')).toBeTruthy();
    });

    it('changes slides when navigation dots are clicked', () => {
        render(<AuthLayout>Test Content</AuthLayout>);
        
        // Click on dot for slide 2
        fireEvent.click(screen.getByTestId('slide-dot-2'));
        
        // After animation completes
        act(() => {
            vi.advanceTimersByTime(600);
        });
        
        // Now slide 2 should be active
        expect(screen.getByTestId('slide-2').classList.contains('opacity-100')).toBeTruthy();
        expect(screen.getByTestId('slide-dot-2').classList.contains('bg-slate-950')).toBeTruthy();
    });

    it('automatically transitions to the next slide after interval', () => {
        render(<AuthLayout>Test Content</AuthLayout>);
        
        // Initially slide 1 is active
        expect(screen.getByTestId('slide-1').classList.contains('opacity-100')).toBeTruthy();
        
        // Advance time to trigger slide change
        act(() => {
            vi.advanceTimersByTime(5000);
        });
        
        // After animation completes
        act(() => {
            vi.advanceTimersByTime(600);
        });
        
        // Slide 2 should now be active
        expect(screen.getByTestId('slide-2').classList.contains('opacity-100')).toBeTruthy();
    });

    it('shows correct slides in mobile view', () => {
        // Mock window resize for mobile view
        global.innerWidth = 600;
        global.dispatchEvent(new Event('resize'));
        
        render(<AuthLayout>Test Content</AuthLayout>);
        
        const mobileSlider = screen.getByTestId('mobile-slider');
        expect(mobileSlider).toBeInTheDocument();
        
        const mobileSlide1 = screen.getByTestId('mobile-slide-1');
        expect(mobileSlide1.classList.contains('opacity-100')).toBeTruthy();
    });
    
    it('does not change slide when clicking the currently active slide dot', () => {
        const { rerender } = render(<AuthLayout>Test Content</AuthLayout>);
        
        // Initially slide 1 is active
        expect(screen.getByTestId('slide-1').classList.contains('opacity-100')).toBeTruthy();
        
        // Click on already active dot (slide 1)
        fireEvent.click(screen.getByTestId('slide-dot-1'));
        rerender(<AuthLayout>Test Content</AuthLayout>);
        
        // Slide 1 should still be active
        expect(screen.getByTestId('slide-1').classList.contains('opacity-100')).toBeTruthy();
    });

    it('cleans up timers on component unmount', () => {
        const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
        const { unmount } = render(<AuthLayout>Test Content</AuthLayout>);
        
        unmount();
        expect(clearIntervalSpy).toHaveBeenCalled();
    });

    it('correctly applies background colors to slides', () => {
        render(<AuthLayout>Test Content</AuthLayout>);
        
        const slide0Container = screen.getByTestId('slide-0').querySelector('div');
        const slide1Container = screen.getByTestId('slide-1').querySelector('div');
        const slide2Container = screen.getByTestId('slide-2').querySelector('div');
        
        // Use substring match for Tailwind's escaped class names
        expect(slide0Container?.className).toContain('bg-[');
        expect(slide0Container?.className).toContain('#EE46BC');
        expect(slide1Container?.className).toContain('bg-[');
        expect(slide1Container?.className).toContain('#FDB022');
        expect(slide2Container?.className).toContain('bg-[');
        expect(slide2Container?.className).toContain('#2E90FA');
    });
});
