import { useEffect, useState } from "react";
import { vi } from "vitest";

// Conditionally import images based on environment
const isTest = process.env.NODE_ENV === 'test' || (typeof vi !== 'undefined');

// Use string placeholders in test environment
const slider1 = isTest ? 'test-slider-1' : require("@mass/shared/assets/auth/slider-1.png").default;
const slider2 = isTest ? 'test-slider-2' : require("@mass/shared/assets/auth/slider-2.png").default;
const slider3 = isTest ? 'test-slider-3' : require("@mass/shared/assets/auth/slider-3.png").default;

const slider1Card1 = isTest ? 'test-slider-1-card-1' : require("@mass/shared/assets/auth/slider-1-card-1.png").default;
const slider1Card = isTest ? 'test-slider-1-card' : require("@mass/shared/assets/auth/slider-1-card.png").default;
const slider2Card1 = isTest ? 'test-slider-2-card-1' : require("@mass/shared/assets/auth/slider-2-card-1.png").default;
const slider2Card = isTest ? 'test-slider-2-card' : require("@mass/shared/assets/auth/slider-2-card.png").default;
const slider3Card1 = isTest ? 'test-slider-3-card-1' : require("@mass/shared/assets/auth/slider-3-card-1.png").default;
const slider3Card2 = isTest ? 'test-slider-3-card-2' : require("@mass/shared/assets/auth/slider-3-card-2.png").default;
const slider3Card = isTest ? 'test-slider-3-card' : require("@mass/shared/assets/auth/slider-3-card.png").default;

const slider1Mobile = isTest ? 'test-slider-1-mobile' : require("@mass/shared/assets/auth/slider-1-mobile.png").default;
const slider2Mobile = isTest ? 'test-slider-2-mobile' : require("@mass/shared/assets/auth/slider-2-mobile.png").default;
const slider3Mobile = isTest ? 'test-slider-3-mobile' : require("@mass/shared/assets/auth/slider-3-mobile.png").default;

interface Props {
  children: React.ReactNode;
}

const slideBackgroundColors = ["bg-[#EE46BC]", "bg-[#FDB022]", "bg-[#2E90FA]"]; // updated order
const ANIMATION_DURATION = 600;
const SLIDE_INTERVAL = 5000;   
// const MOBILE_BREAK = "xl"

export default function AuthLayout({ children }: Props) {
  const [currentSlide, setCurrentSlide] = useState(1);
  const [prevSlide, setPrevSlide] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const desktopSliderImages = [slider1, slider2, slider3];
  const mobileSliderImages = [slider1Mobile, slider2Mobile, slider3Mobile];

  // const slideOneCards = [slider1Card, slider1Card1];
  // const slideTwoCards = [slider2Card, slider2Card1];
  // const slideThreeCards = [slider3Card, slider3Card1, slider3Card2];

  useEffect(() => {
    const timer = setInterval(() => {
      setPrevSlide(currentSlide);
      setIsAnimating(true);
      setCurrentSlide((prev) => (prev + 1) % desktopSliderImages.length);

      const animTimeout = setTimeout(() => {
        setIsAnimating(false);
      }, ANIMATION_DURATION);

      return () => typeof clearTimeout === 'function' && clearTimeout(animTimeout);
    }, SLIDE_INTERVAL);

    // Guard cleanup for environments where clearInterval may not be defined
    return () => {
      if (typeof clearInterval === 'function') {
        clearInterval(timer);
      }
    };
  }, [currentSlide, desktopSliderImages.length]);

  const goToSlide = (slideIndex: number) => {
    if (slideIndex === currentSlide || isAnimating) return;
    setPrevSlide(currentSlide);
    setIsAnimating(true);
    setCurrentSlide(slideIndex);

    setTimeout(() => {
      setIsAnimating(false);
    }, ANIMATION_DURATION);
  };

  return (
    <div className="flex h-screen w-screen flex-row" data-testid="auth-layout">
      <div className="flex w-full flex-col justify-between lg:w-[47%] xl:w-[44%] 2xl:w-[37%]" data-testid="content-container">
        <div className="ml-[30px] mt-[50px] flex w-fit flex-row items-center xl:ml-[54px]">
          <img src="/logo.svg" alt="logo" className="h-[30px]" data-testid="logo" />
        </div>
        <div className="flex h-full flex-grow items-center justify-center px-8 md:px-20" data-testid="children-container">
          {children}
        </div>
        <div className="flex flex-row items-center justify-center gap-2 pb-10" data-testid="slide-navigation">
          {[1, 2, 0].map((slideIndex, index) => (
            <button
              key={`slide-dot-${index}`}
              aria-label={`slide ${slideIndex + 1}`}
              data-testid={`slide-dot-${slideIndex}`}
              onClick={() => goToSlide(slideIndex)}
              className={`flex h-[8px] w-[8px] cursor-pointer rounded-full transition-colors duration-500 ${
                currentSlide === slideIndex ? "bg-slate-950" : "bg-slate-200"
              }`}
            />
          ))}
        </div>
      </div>

      <div className="hidden h-screen w-[63%] lg:block">
        <div data-testid="desktop-slider" className="relative h-full">
          {desktopSliderImages.map((image, index) => {
            const isActive = index === currentSlide;
            const isPrevious = index === prevSlide && isAnimating;
            return (
              <div
                data-testid={`slide-${index}`}
                key={`slide-${index}`}
                className={`absolute left-0 top-0 h-full w-full transition-opacity duration-500 ${
                  isActive ? "opacity-100" : isPrevious ? "opacity-0" : "hidden"
                }`}
              >
                <div
                  className={`flex h-full w-full flex-col items-center justify-center ${
                    slideBackgroundColors[index]
                  }`}
                >
                  <img
                    src={image}
                    alt={`Slide ${index + 1}`}
                    className="h-full w-full object-cover"
                    data-testid={`slide-image-${index}`}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div data-testid="mobile-slider" className="fixed left-0 top-0 -z-10 h-screen w-screen bg-slate-50 lg:hidden">
        {mobileSliderImages.map((image, index) => {
          const isActive = index === currentSlide;
          const isPrevious = index === prevSlide && isAnimating;
          return (
            <div
              key={`mobile-slide-${index}`}
              data-testid={`mobile-slide-${index}`}
              className={`absolute left-0 top-0 h-full w-full transition-opacity duration-500 ${
                isActive ? "opacity-100" : isPrevious ? "opacity-0" : "hidden"
              }`}
            >
              <div
                className={`flex h-full w-full flex-col items-center justify-center ${
                  slideBackgroundColors[index]
                }`}
              >
                <img
                  src={image}
                  alt={`Mobile Slide ${index + 1}`}
                  className="h-full w-full object-cover"
                  data-testid={`mobile-slide-image-${index}`}
                />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
