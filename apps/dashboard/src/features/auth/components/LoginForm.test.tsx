import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { LoginForm } from './LoginForm';

vi.mock('@mass/shared/components/organisms/auth/alert', () => ({
  AuthAlert: ({ title, description }: any) => (
    <div data-testid="auth-alert">
      <h3>{title}</h3>
      <p>{description}</p>
    </div>
  ),
}));

const mockOpen = vi.fn();

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => {
      const translations: Record<string, string> = {
        'auth.login.title': 'Login',
        'auth.login.subtitle': 'Login to your account',
        'auth.login.errorTitle': 'Login Error',
        'auth.login.eDevletLogin': 'Login with e-Government',
        'auth.register.kvkkTitle': 'KVKK Information Text',
        'auth.register.kvkkDescription': 'Please confirm that you have read the KVKK text.',
        'auth.register.userAgreementTitle': 'User Agreement',
        'auth.register.userAgreementDescription': 'Please confirm that you have read the User Agreement text.',
      };
      return options?.defaultValue || translations[key] || key;
    },
  }),
  Trans: ({ i18nKey, components }: any) => (
    <div data-testid="trans-component">
      {i18nKey}
      {components?.kvkk && (
        <button data-testid="kvkk-link" onClick={() => components.kvkk.props.onClick()}>
          KVKV
        </button>
      )}
      {components?.userAgreement && (
        <button data-testid="agreement-link" onClick={() => components.userAgreement.props.onClick()}>
          User Agreement
        </button>
      )}
    </div>
  ),
}));

vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: () => ({
    open: mockOpen,
  }),
}));

vi.mock('@mass/shared/hooks/use-global-settings', () => ({
  globalSettingsKeys: {
    documents: {
      pdf: () => 'documents.pdf',
    },
  },
  useGlobalSettings: () => ({
    data: {
      value: {
        kvkk: { url: 'https://example.com/kvkk.pdf' },
        agreement: { url: 'https://example.com/agreement.pdf' },
      },
    },
  }),
}));

vi.mock('@mass/shared/components/atoms/Iconify', () => ({
  default: ({ name, className }: any) => (
    <div data-testid={`icon-${name}`} className={className} />
  ),
}));

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, type, ...props }: any) => (
    <button {...props} onClick={onClick} disabled={disabled} type={type}>
      {children}
    </button>
  ),
}));

vi.mock('@mass/shared/assets/logo-mark.svg', () => ({
  default: 'logo-mark.svg',
}));

const mockWindowOpen = vi.fn();

describe('LoginForm', () => {
  const mockOnSubmit = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    Object.defineProperty(window, 'open', {
      value: mockWindowOpen,
      writable: true,
    });
  });

  const renderLoginForm = (props = {}) => {
    return render(
      <LoginForm
        onSubmit={mockOnSubmit}
        error=""
        isLoading={false}
        {...props}
      />
    );
  };

  it('should render login form elements', () => {
    renderLoginForm();

    expect(screen.getByAltText('logo')).toBeInTheDocument();
    expect(screen.getByTestId('icon-mass:login')).toBeInTheDocument();
    expect(screen.getByText('Login')).toBeInTheDocument();
    expect(screen.getByText('Login to your account')).toBeInTheDocument();
    expect(screen.getByText('Login with e-Government')).toBeInTheDocument();
  });

  it('should display error message when provided', () => {
    renderLoginForm({ error: 'Invalid credentials' });

    expect(screen.getByText('Login Error')).toBeInTheDocument();
    expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
  });

  it('should not display error when no error provided', () => {
    renderLoginForm();

    expect(screen.queryByTestId('auth-alert')).not.toBeInTheDocument();
  });

  it('should handle e-devlet login click', async () => {
    renderLoginForm();

    const eDevletButton = screen.getByText('Login with e-Government');
    fireEvent.click(eDevletButton);

    await waitFor(() => {
      expect(mockWindowOpen).toHaveBeenCalledWith('/api/auth/edevlet', '_self');
    });
  });

  it('should disable e-devlet button when loading', () => {
    renderLoginForm({ isLoading: true });

    const eDevletButton = screen.getByText('Login with e-Government');
    expect(eDevletButton).toBeDisabled();
  });

  it('should enable e-devlet button when not loading', () => {
    renderLoginForm({ isLoading: false });

    const eDevletButton = screen.getByText('Login with e-Government');
    expect(eDevletButton).not.toBeDisabled();
  });

  it('should render agreement links', () => {
    renderLoginForm();

    expect(screen.getByTestId('trans-component')).toBeInTheDocument();
    expect(screen.getByTestId('kvkk-link')).toBeInTheDocument();
    expect(screen.getByTestId('agreement-link')).toBeInTheDocument();
  });

  it('should open KVKK modal when KVKK button is clicked', async () => {
    renderLoginForm();

    const kvkkButton = screen.getByTestId('kvkk-link');
    fireEvent.click(kvkkButton);

    await waitFor(() => {
      expect(mockOpen).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining({
          name: 'pdf-modal-kvkk',
          size: 'large',
          url: 'https://example.com/kvkk.pdf',
          disableClose: true,
        })
      );
    });
  });

  it('should open User Agreement modal when agreement button is clicked', async () => {
    renderLoginForm();

    const agreementButton = screen.getByTestId('agreement-link');
    fireEvent.click(agreementButton);

    await waitFor(() => {
      expect(mockOpen).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining({
          name: 'pdf-modal-userAgreement',
          size: 'large',
          url: 'https://example.com/agreement.pdf',
          disableClose: true,
        })
      );
    });
  });

  it('should display footer text', () => {
    renderLoginForm();

    expect(screen.getByText(/MASS © Tüm hakları saklıdır/)).toBeInTheDocument();
  });

  it('should render correct icons', () => {
    renderLoginForm();

    expect(screen.getByTestId('icon-mass:login')).toBeInTheDocument();
    expect(screen.getByTestId('icon-mass:edevlet')).toBeInTheDocument();
  });

  it('should handle modal callbacks correctly', async () => {
    renderLoginForm();

    const kvkkButton = screen.getByTestId('kvkk-link');
    fireEvent.click(kvkkButton);

    await waitFor(() => {
      expect(mockOpen).toHaveBeenCalled();
    });

    // Get the modal configuration
    const modalCall = mockOpen.mock.calls[0];
    const modalConfig = modalCall[1];

    // Test the onApprove callback exists
    expect(typeof modalConfig.onApprove).toBe('function');

    // Call the callback to ensure it doesn't throw
    expect(() => modalConfig.onApprove()).not.toThrow();
  });

  it('should handle missing onSubmit prop gracefully', () => {
    // Test that component works with minimal props
    expect(() => {
      render(<LoginForm onSubmit={vi.fn()} isLoading={false} />);
    }).not.toThrow();
  });

  it('should have proper component structure', () => {
    renderLoginForm();

    // Check main container
    const container = screen.getByText('Login').closest('.space-y-6');
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass('mt-4', 'max-w-md', 'mx-auto');
  });
});

