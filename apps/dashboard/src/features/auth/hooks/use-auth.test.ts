import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useMe, useLoginMutation, useLogoutMutation, authKeys } from './use-auth';
import { authService } from '@/services/api/auth';
import axios from 'axios';

// Mock the auth service
vi.mock('@/services/api/auth', () => ({
  authService: {
    me: vi.fn(),
    login: vi.fn(),
    logout: vi.fn(),
  },
}));

// Mock axios
vi.mock('axios', () => ({
  default: {
    isAxiosError: vi.fn(),
  },
}));

describe('use-auth hooks', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    React.createElement(QueryClientProvider, { client: queryClient }, children)
  );

  describe('authKeys', () => {
    it('should return correct query keys', () => {
      expect(authKeys.all).toEqual(['auth']);
      expect(authKeys.session()).toEqual(['auth', 'session']);
      expect(authKeys.user()).toEqual(['auth', 'user']);
    });
  });

  describe('useMe', () => {
    it('should fetch user data successfully', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        permissions: [{ scope: 'read' }],
        phone: '',
        tckn: '',
        firstName: '',
        lastName: '',
      };

      vi.mocked(authService.me).mockResolvedValue(mockUser);

      const { result } = renderHook(() => useMe(), { wrapper });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockUser);
      expect(authService.me).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when fetching user data', async () => {
      const mockError = new Error('Failed to fetch user');
      vi.mocked(authService.me).mockRejectedValue(mockError);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useMe(), { wrapper });

      // Wait for all retries to complete (initial attempt + 2 retries = 3 calls)
      await waitFor(() => {
        expect(authService.me).toHaveBeenCalledTimes(3);
      }, { timeout: 5000 });

      // After all retries, the query should be in error state
      expect(result.current.isError).toBe(true);
      expect(result.current.error).toBeTruthy();
      expect(result.current.data).toBeUndefined();
      expect(result.current.isLoading).toBe(false);

      consoleSpy.mockRestore();
    });
  });

  describe('useLoginMutation', () => {
    it('should login successfully', async () => {
      const mockResponse = {
        session: true,
        message: 'Login successful',
      };

      vi.mocked(authService.login).mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useLoginMutation(), { wrapper });

      result.current.mutate({ email: '<EMAIL>', password: 'password123' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockResponse);
      expect(authService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    it('should handle login errors', async () => {
      const mockError = {
        response: { data: { message: 'Invalid credentials' } },
        isAxiosError: true,
      };

      vi.mocked(authService.login).mockRejectedValue(mockError);
      vi.mocked(axios.isAxiosError).mockReturnValue(true);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useLoginMutation(), { wrapper });

      result.current.mutate({ email: '<EMAIL>', password: 'wrong' });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Auth API Error: Invalid credentials');

      consoleSpy.mockRestore();
    });

    it('should reset queries on login', async () => {
      const resetQueriesSpy = vi.spyOn(queryClient, 'resetQueries');

      const { result } = renderHook(() => useLoginMutation(), { wrapper });

      result.current.mutate({ email: '<EMAIL>', password: 'password123' });

      await waitFor(() => {
        expect(resetQueriesSpy).toHaveBeenCalled();
      });
    });
  });

  describe('useLogoutMutation', () => {
    it('should logout successfully', async () => {
      vi.mocked(authService.logout).mockResolvedValue(undefined);

      const { result } = renderHook(() => useLogoutMutation(), { wrapper });

      result.current.mutate();

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(authService.logout).toHaveBeenCalledTimes(1);
    });

    it('should handle logout errors', async () => {
      const mockError = new Error('Logout failed');
      vi.mocked(authService.logout).mockRejectedValue(mockError);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useLogoutMutation(), { wrapper });

      result.current.mutate();

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Unexpected auth error:', mockError);

      consoleSpy.mockRestore();
    });

    it('should handle axios errors in logout', async () => {
      const axiosError = {
        response: {
          data: {
            message: 'Session expired',
          },
        },
        message: 'Request failed',
      };

      vi.mocked(axios.isAxiosError).mockReturnValue(true);
      vi.mocked(authService.logout).mockRejectedValue(axiosError);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useLogoutMutation(), { wrapper });

      result.current.mutate();

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Auth API Error: Session expired');

      consoleSpy.mockRestore();
    });
  });

  describe('error logging function', () => {
    it('should handle axios errors with response data message', async () => {
      const axiosError = {
        response: {
          data: {
            message: 'Custom error message',
          },
        },
        message: 'Request failed',
      };

      vi.mocked(axios.isAxiosError).mockReturnValue(true);
      vi.mocked(authService.login).mockRejectedValue(axiosError);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useLoginMutation(), { wrapper });

      await act(async () => {
        try {
          await result.current.mutateAsync({ email: '<EMAIL>', password: 'password' });
        } catch (error) {
          // Expected to fail
        }
      });

      expect(consoleSpy).toHaveBeenCalledWith('Auth API Error: Custom error message');

      consoleSpy.mockRestore();
    });

    it('should handle axios errors with fallback to error message', async () => {
      const axiosError = {
        message: 'Network Error',
      };

      vi.mocked(axios.isAxiosError).mockReturnValue(true);
      vi.mocked(authService.login).mockRejectedValue(axiosError);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useLoginMutation(), { wrapper });

      await act(async () => {
        try {
          await result.current.mutateAsync({ email: '<EMAIL>', password: 'password' });
        } catch (error) {
          // Expected to fail
        }
      });

      expect(consoleSpy).toHaveBeenCalledWith('Auth API Error: Network Error');

      consoleSpy.mockRestore();
    });

    it('should handle non-axios errors', async () => {
      const generalError = new Error('Some unexpected error');
      
      vi.mocked(axios.isAxiosError).mockReturnValue(false);
      vi.mocked(authService.login).mockRejectedValue(generalError);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useLoginMutation(), { wrapper });

      await act(async () => {
        try {
          await result.current.mutateAsync({ email: '<EMAIL>', password: 'password' });
        } catch (error) {
          // Expected to fail
        }
      });

      expect(consoleSpy).toHaveBeenCalledWith('Unexpected auth error:', generalError);

      consoleSpy.mockRestore();
    });
  });

  describe('authKeys', () => {
    it('should return correct key structures', () => {
      expect(authKeys.all).toEqual(['auth']);
      expect(authKeys.session()).toEqual(['auth', 'session']);
      expect(authKeys.user()).toEqual(['auth', 'user']);
    });

    it('should maintain key consistency', () => {
      const userKey1 = authKeys.user();
      const userKey2 = authKeys.user();
      expect(userKey1).toEqual(userKey2);
    });
  });
});

