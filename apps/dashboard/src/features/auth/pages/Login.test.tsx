import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import '@testing-library/jest-dom';
import Login from './Login';
import * as authHooks from '../hooks/use-auth';

// Mock dependencies
const mockNavigate = vi.fn();
vi.mock('@tanstack/react-router', () => ({
  useNavigate: () => mockNavigate,
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

vi.mock('../components/AuthLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="auth-layout">{children}</div>,
}));

vi.mock('../components/LoginForm', () => ({
  LoginForm: ({ onSubmit, error, isLoading }: any) => (
    <div data-testid="login-form">
      <button 
        data-testid="submit-button" 
        onClick={() => onSubmit({ email: '<EMAIL>', password: 'password123' })}
        disabled={isLoading}
      >
        Submit
      </button>
      {error && <div data-testid="error-message">{error}</div>}
    </div>
  ),
}));

describe('Login Page', () => {
  let queryClient: QueryClient;
  let mockLoginMutation: any;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockNavigate.mockClear();

    mockLoginMutation = {
      mutateAsync: vi.fn(),
      isPending: false,
    };

    vi.spyOn(authHooks, 'useLoginMutation').mockReturnValue(mockLoginMutation);
  });

  const renderLogin = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <Login />
      </QueryClientProvider>
    );
  };

  it('should render login form within auth layout', () => {
    renderLogin();

    expect(screen.getByTestId('auth-layout')).toBeInTheDocument();
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
  });

  it('should handle successful login', async () => {
    mockLoginMutation.mutateAsync.mockResolvedValue({
      session: { token: 'test-token' },
    });

    renderLogin();

    const submitButton = screen.getByTestId('submit-button');
    submitButton.click();

    await waitFor(() => {
      expect(mockLoginMutation.mutateAsync).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
      expect(mockNavigate).toHaveBeenCalledWith({ to: '/' });
    });
  });

  it('should display error message on login failure', async () => {
    mockLoginMutation.mutateAsync.mockResolvedValue({
      message: 'Invalid credentials',
    });

    renderLogin();

    const submitButton = screen.getByTestId('submit-button');
    submitButton.click();

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Invalid credentials');
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  it('should handle login exception', async () => {
    mockLoginMutation.mutateAsync.mockRejectedValue(new Error('Network error'));

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    renderLogin();

    const submitButton = screen.getByTestId('submit-button');
    submitButton.click();

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('An error occurred while logging in');
      expect(consoleSpy).toHaveBeenCalledWith('Login error:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it('should disable submit button when loading', () => {
    mockLoginMutation.isPending = true;

    renderLogin();

    const submitButton = screen.getByTestId('submit-button');
    expect(submitButton).toBeDisabled();
  });
});
