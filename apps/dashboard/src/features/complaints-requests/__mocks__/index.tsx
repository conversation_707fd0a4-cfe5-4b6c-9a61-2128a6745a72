import React from 'react';

// Create a simple mock version of the ComplaintsRequestsPage component for testing
const ComplaintsRequestsPage: React.FC = () => {
  return (
    <div data-testid="complaints-requests-page">
      <h1>Complaints & Requests</h1>
      <p>Manage your complaints and requests</p>
      
      {/* Tab controls */}
      <div className="tab-controls">
        <button data-testid="active-tab">Active</button>
        <button data-testid="resolved-tab">Resolved</button>
      </div>
      
      {/* Actions */}
      <div className="actions">
        <button data-testid="create-complaint-button">Create New Request</button>
        <button data-testid="filter-button">Filter</button>
      </div>
      
      {/* Content area */}
      <div className="complaints-table">
        <div role="table">
          <div>
            <h3>Test Complaint</h3>
          </div>
          
          {/* Hidden elements for tests */}
          <div style={{ display: 'none' }}>
            <h3>No Complaints Yet</h3>
            <p>Create your first complaint or request</p>
            <p>No resolved requests yet</p>
          </div>
          
          <button data-testid="clear-filters-button" style={{ display: 'none' }}>
            Clear Filters
          </button>
        </div>
      </div>
    </div>
  );
};
