import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { columns, ComplaintRequestType } from './columns';
import * as queries from '../data/queries';
import { useModal } from '@mass/shared/components/organisms/modal/provider';
import { useTranslation } from 'react-i18next';
import type { ColumnDef } from '@tanstack/react-table';

// We'll use a direct import approach for mocking i18next
vi.mock('react-i18next', () => {
  return {
    // This is a more complete solution to properly mock the i18n library
    useTranslation: () => {
      return {
        t: (str: string) => str,
        i18n: {
          language: 'tr',
          // Additional properties required by the interface
          changeLanguage: vi.fn(),
          // We add the "exists" property to satisfy the i18n interface requirements
          exists: vi.fn()
        }
      };
    },
    // Mock any other exports as needed
    initReactI18next: { type: '3rdParty', init: vi.fn() }
  };
});

vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: vi.fn().mockReturnValue({
    open: vi.fn()
  })
}));

vi.mock('@/hooks/use-modal', () => ({
  useModal: () => ({ open: vi.fn() }),
}));

vi.mock('../data/queries', () => ({
  useComplaintCategories: vi.fn(),
  useCancelComplaint: vi.fn()
}));

// Setup test data
const mockComplaint: ComplaintRequestType = {
  id: 'COMP123',
  createdAt: '2023-06-15T10:30:00Z',
  updatedAt: '2023-06-16T14:45:00Z',
  userId: 'user123',
  subscriptionId: 'sub456',
  type: 'TECHNICAL',
  subtype: 'CONNECTION_ISSUE',
  status: 'PENDING',
  body: 'Internet connection problems',
  response: null,
  documents: [],
  subscription: {
    name: 'Premium Package'
  }
};

const mockCategories = {
  TECHNICAL: {
    subcategories: {
      CONNECTION_ISSUE: {
        label: {
          TR: 'Bağlantı Sorunu',
          EN: 'Connection Issue'
        }
      }
    }
  }
};

describe('Complaints-Requests Columns', () => {
  beforeEach(() => {
    // Reset mocks to ensure clean state between tests
    vi.clearAllMocks();

    vi.mocked(queries.useComplaintCategories).mockReturnValue({
      data: mockCategories,
      isLoading: false
    } as any);

    vi.mocked(queries.useCancelComplaint).mockReturnValue({
      mutate: vi.fn()
    } as any);
  });

  it('should render the ID column correctly', () => {
    // Find column by id accessor key
    const column = columns.find(col => 
      'accessorKey' in col && col.accessorKey === 'body'
    ) as ColumnDef<ComplaintRequestType, unknown>;
    expect(column).toBeDefined();
    
    const Header = column.header as any;
    const Cell = column.cell as any;
    
    const { container: headerContainer } = render(<Header />);
    expect(headerContainer.textContent).toContain('columns.application_id');
    
    const { container: cellContainer } = render(<Cell row={{ original: mockComplaint }} />);
    expect(cellContainer.textContent).toContain('COMP123');
  });

  it('should render subscription column correctly', () => {
    const column = columns.find(col => 
      'accessorKey' in col && col.accessorKey === 'subscriptionId'
    ) as ColumnDef<ComplaintRequestType, unknown>;
    expect(column).toBeDefined();
    
    const Cell = column.cell as any;
    const { container } = render(<Cell row={{ original: mockComplaint }} />);
    expect(container.textContent).toContain('Premium Package');
  });

  it('should render type column with correct category label', () => {
    const column = columns.find(col => 
      'accessorKey' in col && col.accessorKey === 'type'
    ) as ColumnDef<ComplaintRequestType, unknown>;
    expect(column).toBeDefined();
    
    const Cell = column.cell as any;
    const { container } = render(<Cell row={{ original: mockComplaint }} />);
    expect(container.textContent).toContain('Bağlantı Sorunu');
  });

  it('should handle loading state for categories', () => {
    vi.mocked(queries.useComplaintCategories).mockReturnValueOnce({
      isLoading: true
    } as any);
    
    const column = columns.find(col => 
      'accessorKey' in col && col.accessorKey === 'type'
    ) as ColumnDef<ComplaintRequestType, unknown>;
    const Cell = column.cell as any;
    
    const { container } = render(<Cell row={{ original: mockComplaint }} />);
    expect(container.textContent).toContain('loading');
  });

  it('should format dates correctly', () => {
    const createdAtColumn = columns.find(col => 
      'accessorKey' in col && col.accessorKey === 'createdAt'
    ) as ColumnDef<ComplaintRequestType, unknown>;
    const updatedAtColumn = columns.find(col => 
      'accessorKey' in col && col.accessorKey === 'updatedAt'
    ) as ColumnDef<ComplaintRequestType, unknown>;
    
    expect(createdAtColumn).toBeDefined();
    expect(updatedAtColumn).toBeDefined();
    
    const CreatedAtCell = createdAtColumn.cell as any;
    const UpdatedAtCell = updatedAtColumn.cell as any;
    
    const { container: createdContainer } = render(
      <CreatedAtCell row={{ getValue: () => mockComplaint.createdAt }} />
    );
    
    const { container: updatedContainer } = render(
      <UpdatedAtCell row={{ getValue: () => mockComplaint.updatedAt }} />
    );
    
    // Check for date format (actual format will depend on the locale)
    expect(createdContainer.textContent).not.toBe('-');
    expect(updatedContainer.textContent).not.toBe('-');
  });

  it('should render action column with restore button for PENDING status', () => {
    const actionColumn = columns.find(col => col.id === 'actions');
    expect(actionColumn).toBeDefined();
    
    const Cell = actionColumn!.cell as any;
    const open = vi.fn();
    vi.mocked(useModal).mockReturnValue({ open } as any);
    
    const { container: testContainer } = render(<Cell row={{ original: mockComplaint }} />);
    
    // Check for the SVG icon instead of the text
    const iconElement = testContainer.querySelector('.iconify.iconify--untitled');
    expect(iconElement).toBeInTheDocument();
    
    // Alternatively, check for the action button's container
    const actionButtonContainer = testContainer.querySelector('.flex.items-center.justify-center.w-8.h-8');
    expect(actionButtonContainer).toBeInTheDocument();
  });

  it('should not render action button for non-PENDING status', () => {
    const actionColumn = columns.find(col => col.id === 'actions');
    const Cell = actionColumn!.cell as any;
    
    const open = vi.fn();
    vi.mocked(useModal).mockReturnValue({ open } as any);
    
    const resolvedComplaint = { ...mockComplaint, status: 'RESOLVED' };
    
    const { container: testContainer } = render(<Cell row={{ original: resolvedComplaint }} />);
    
    // Check that the icon is not present
    const iconElement = testContainer.querySelector('.iconify.iconify--untitled');
    expect(iconElement).not.toBeInTheDocument();
    
    // Alternatively, check that the action button container is not present
    const actionButtonContainer = testContainer.querySelector('.flex.items-center.justify-center.w-8.h-8');
    expect(actionButtonContainer).not.toBeInTheDocument();
  });
});
