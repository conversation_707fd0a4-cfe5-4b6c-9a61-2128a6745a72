import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { DocIcon } from "./document";

describe("DocIcon Component", () => {
  it("renders without crashing", () => {
    render(<DocIcon filename="test.pdf" type="application/pdf" />);
    expect(document.querySelector("svg")).toBeInTheDocument();
  });

  it("displays the correct extension for PDF files", () => {
    render(<DocIcon filename="document.pdf" type="application/pdf" />);
    expect(screen.getByText("PDF")).toBeInTheDocument();
  });

  it("displays the correct extension for DOCX files", () => {
    render(<DocIcon filename="document.docx" type="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />);
    expect(screen.getByText("DOCX")).toBeInTheDocument();
  });

  it("uses extension from filename when type is not recognized", () => {
    render(<DocIcon filename="custom.xyz" type="application/unknown" />);
    expect(screen.getByText("XYZ")).toBeInTheDocument();
  });

  it("applies the correct color based on file type", () => {
    const { container } = render(<DocIcon filename="document.pdf" type="application/pdf" />);
    const svg = container.querySelector("svg");
    expect(svg).toHaveStyle("color: #E74C3C");
  });

  it("applies default color when file type is not recognized", () => {
    const { container } = render(<DocIcon filename="file.unknown" type="unknown/type" />);
    const svg = container.querySelector("svg");
    expect(svg).toHaveStyle("color: #34495E");
  });

  it("applies custom size when specified", () => {
    const { container } = render(<DocIcon filename="document.pdf" type="application/pdf" size="30" />);
    const svg = container.querySelector("svg");
    expect(svg).toHaveAttribute("width", "30");
    expect(svg).toHaveAttribute("height", "30");
  });

  it("uses default size when not specified", () => {
    const { container } = render(<DocIcon filename="document.pdf" type="application/pdf" />);
    const svg = container.querySelector("svg");
    expect(svg).toHaveAttribute("width", "20");
    expect(svg).toHaveAttribute("height", "20");
  });

  it("truncates long extensions to 4 characters", () => {
    render(<DocIcon filename="document.longext" type="unknown/type" />);
    expect(screen.getByText("LONG")).toBeInTheDocument();
  });

  it("doesn't show extension area when no extension is available", () => {
    const { container } = render(<DocIcon filename="" type="" />);
    expect(container.querySelector("mask")).not.toBeInTheDocument();
  });
});
