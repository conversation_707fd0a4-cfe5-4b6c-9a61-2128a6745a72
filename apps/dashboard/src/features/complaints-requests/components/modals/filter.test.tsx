import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Filter from './filter';
import { useComplaintCategories } from '../../data/queries';

// Mock the hooks and i18n
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: { language: 'en' }
  })
}));

vi.mock('../../data/queries', () => ({
  useComplaintCategories: vi.fn()
}));

// Mock components used in the filter component
vi.mock('@mass/shared/components/atoms/featured-icon', () => ({
  default: ({ name }: { name: string }) => <div data-testid="featured-icon">{name}</div>
}));

vi.mock('@mass/shared/components/ui/date-picker', () => ({
  DatePicker: ({ date, setDate }: { date?: Date; setDate: (date?: Date) => void }) => (
    <input 
      type="date" 
      data-testid="date-picker" 
      value={date ? date.toISOString().split('T')[0] : ''} 
      onChange={(e) => setDate(e.target.value ? new Date(e.target.value) : undefined)}
    />
  )
}));

describe('Filter Component', () => {
  const onHideMock = vi.fn();
  const onConfirmMock = vi.fn();
  const clearFiltersMock = vi.fn();
  
  const defaultProps = {
    onHide: onHideMock,
    onConfirm: onConfirmMock,
    clearFilters: clearFiltersMock,
    currentFilters: {
      type: 'all',
      subtype: 'all',
      startDate: undefined,
      endDate: undefined
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock the categories data
    (useComplaintCategories as any).mockReturnValue({
      data: {
        'complaint': {
          label: { EN: 'Complaint' },
          subcategories: {
            'billing': { label: { EN: 'Billing' } },
            'service': { label: { EN: 'Service' } }
          }
        },
        'request': {
          label: { EN: 'Request' },
          subcategories: {
            'new': { label: { EN: 'New Request' } }
          }
        }
      }
    });
  });

  it('renders the filter modal with correct initial values', () => {
    render(<Filter {...defaultProps} />);
    
    // Check title is rendered - targeting specifically the heading
    const filterHeading = screen.getAllByText('filter')[0]; // First occurrence is the heading
    expect(filterHeading).toBeInTheDocument();
    expect(filterHeading.className).toContain('self-stretch');
    
    // Check date pickers are rendered
    const datePickers = screen.getAllByTestId('date-picker');
    expect(datePickers).toHaveLength(2);
    
    // Check type select is rendered with default value
    const typeSelect = screen.getByText('application_type');
    expect(typeSelect).toBeInTheDocument();
  });

  it('clears filters when clear button is clicked', async () => {
    const user = userEvent.setup();
    render(<Filter {...defaultProps} />);
    
    const clearButton = screen.getByText('clear_filters');
    await user.click(clearButton);
    
    expect(clearFiltersMock).toHaveBeenCalled();
    expect(onHideMock).toHaveBeenCalled();
  });

  it('applies filters with selected values when filter button is clicked', async () => {
    const user = userEvent.setup();
    
    // Create a specific date for testing
    const startDate = new Date('2023-01-01');
    const endDate = new Date('2023-12-31');
    
    render(
      <Filter 
        {...defaultProps} 
        currentFilters={{
          type: 'complaint',
          subtype: 'all',
          startDate,
          endDate
        }} 
      />
    );
    
    // Click the filter button - using getAllByText to get the button (last element)
    const filterButtons = screen.getAllByText('filter');
    const filterButton = filterButtons[filterButtons.length - 1];
    await user.click(filterButton);
    
    // Check that onConfirm was called with the right values
    expect(onConfirmMock).toHaveBeenCalledWith({
      type: 'complaint',
      subtype: 'all',
      startDate,
      endDate
    });
    
    expect(onHideMock).toHaveBeenCalled();
  });

  it('handles date range correctly when only one date is provided', async () => {
    const user = userEvent.setup();
    const startDate = new Date('2023-01-01');
    
    render(
      <Filter 
        {...defaultProps} 
        currentFilters={{
          type: 'all',
          subtype: 'all',
          startDate,
          endDate: undefined
        }} 
      />
    );
    
    // Click the filter button
    const filterButtons = screen.getAllByText('filter');
    const filterButton = filterButtons[filterButtons.length - 1];
    await user.click(filterButton);
    
    // Check that onConfirm was called with start date and current date for end date
    expect(onConfirmMock).toHaveBeenCalledWith(
      expect.objectContaining({
        startDate,
        endDate: expect.any(Date)
      })
    );
  });

  it('swaps dates if start date is after end date', async () => {
    const user = userEvent.setup();
    const laterDate = new Date('2023-12-31');
    const earlierDate = new Date('2023-01-01');
    
    render(
      <Filter 
        {...defaultProps} 
        currentFilters={{
          type: 'all',
          subtype: 'all',
          startDate: laterDate,  // Intentionally set later date as start
          endDate: earlierDate   // And earlier date as end
        }} 
      />
    );
    
    // Click the filter button
    const filterButtons = screen.getAllByText('filter');
    const filterButton = filterButtons[filterButtons.length - 1];
    await user.click(filterButton);
    
    // Check that onConfirm was called with swapped dates
    expect(onConfirmMock).toHaveBeenCalledWith({
      type: 'all',
      subtype: 'all',
      startDate: earlierDate,
      endDate: laterDate
    });
  });

  it('preserves the complaint type when filter button is clicked', async () => {
    const user = userEvent.setup();
    vi.clearAllMocks();
    
    // Render with complaint type already selected
    render(
      <Filter 
        {...defaultProps} 
        currentFilters={{
          type: 'complaint',
          subtype: 'all',
          startDate: undefined,
          endDate: undefined
        }} 
      />
    );
    
    // Get the filter button by using getAllByText and selecting the last one (the button)
    const filterButtons = screen.getAllByText('filter');
    // The last one is the button, the first one is the header
    const filterButton = filterButtons[filterButtons.length - 1]; 
    
    await user.click(filterButton);
    
    // Verify that the type value is preserved
    expect(onConfirmMock).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'complaint',
        subtype: 'all'
      })
    );
  });
});
