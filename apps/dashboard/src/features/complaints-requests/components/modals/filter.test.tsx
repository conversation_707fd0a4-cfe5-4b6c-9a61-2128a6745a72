import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, within } from '@testing-library/react';
import Filter from './filter';
import { useComplaintCategories } from '../../data/queries';

// Mock the hooks and i18n
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: { language: 'en' }
  })
}));

vi.mock('../../data/queries', () => ({
  useComplaintCategories: vi.fn()
}));

// Mock components used in the filter component
vi.mock('@mass/shared/components/atoms/featured-icon', () => ({
  default: ({ name }: { name: string }) => <div data-testid="featured-icon">{name}</div>
}));

vi.mock('@mass/shared/components/ui/date-picker', () => ({
  DatePicker: ({ date, setDate }: { date?: Date; setDate: (date?: Date) => void }) => (
    <input
      type="date"
      data-testid="date-picker"
      value={date ? date.toISOString().split('T')[0] : ''}
      onChange={(e) => setDate(e.target.value ? new Date(e.target.value) : undefined)}
    />
  )
}));

// Mock UI components
vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, className, variant, ...props }: any) => {
    const handleClick = (e: any) => {
      if (onClick) {
        onClick(e);
      }
    };

    return (
      <button
        onClick={handleClick}
        className={className}
        data-variant={variant}
        type="button"
        {...props}
      >
        {children}
      </button>
    );
  }
}));

vi.mock('@mass/shared/components/ui/label', () => ({
  Label: ({ children, ...props }: any) => <label {...props}>{children}</label>
}));

vi.mock('@mass/shared/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <div onClick={() => onValueChange && onValueChange('complaint')}>
        {children}
      </div>
    </div>
  ),
  SelectContent: ({ children }: any) => <div data-testid="select-content">{children}</div>,
  SelectItem: ({ children, value, ...props }: any) => (
    <div data-testid="select-item" data-value={value} {...props}>{children}</div>
  ),
  SelectTrigger: ({ children }: any) => <div data-testid="select-trigger">{children}</div>,
  SelectValue: ({ placeholder }: any) => <div data-testid="select-value">{placeholder}</div>
}));

describe('Filter Component', () => {
  const onHideMock = vi.fn();
  const onConfirmMock = vi.fn();
  const clearFiltersMock = vi.fn();
  
  const defaultProps = {
    onHide: onHideMock,
    onConfirm: onConfirmMock,
    clearFilters: clearFiltersMock,
    currentFilters: {
      type: 'all',
      subtype: 'all',
      startDate: undefined,
      endDate: undefined
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock the categories data
    (useComplaintCategories as any).mockReturnValue({
      data: {
        'complaint': {
          label: { EN: 'Complaint' },
          subcategories: {
            'billing': { label: { EN: 'Billing' } },
            'service': { label: { EN: 'Service' } }
          }
        },
        'request': {
          label: { EN: 'Request' },
          subcategories: {
            'new': { label: { EN: 'New Request' } }
          }
        }
      }
    });
  });

  it('renders the filter modal with correct initial values', () => {
    render(<Filter {...defaultProps} />);

    // Check title is rendered - targeting specifically the heading
    const filterHeading = screen.getAllByText('filter')[0]; // First occurrence is the heading
    expect(filterHeading).toBeInTheDocument();
    expect(filterHeading.className).toContain('self-stretch');

    // Check date pickers are rendered
    const datePickers = screen.getAllByTestId('date-picker');
    expect(datePickers).toHaveLength(2);

    // Check type select is rendered with default value - use getAllByText since there are multiple
    const typeSelects = screen.getAllByText('application_type');
    expect(typeSelects.length).toBeGreaterThan(0);
  });

  it('clears filters when clear button is clicked', async () => {
    render(<Filter {...defaultProps} />);

    // Find the clear button by its text content and variant
    const allButtons = screen.getAllByRole('button');
    const clearButton = allButtons.find(button =>
      button.textContent === 'clear_filters' && button.getAttribute('data-variant') === 'outline'
    );

    expect(clearButton).toBeDefined();

    // Use fireEvent instead of userEvent
    fireEvent.click(clearButton!);

    expect(clearFiltersMock).toHaveBeenCalled();
    expect(onHideMock).toHaveBeenCalled();
  });

  it('applies filters with selected values when filter button is clicked', async () => {
    // Create a specific date for testing
    const startDate = new Date('2023-01-01');
    const endDate = new Date('2023-12-31');

    render(
      <Filter
        {...defaultProps}
        currentFilters={{
          type: 'complaint',
          subtype: 'all',
          startDate,
          endDate
        }}
      />
    );

    // Click the filter button - find the button without variant (the main filter button)
    const buttons = screen.getAllByRole('button');
    const filterButton = buttons.find(button =>
      button.textContent === 'filter' && !button.hasAttribute('data-variant')
    );
    expect(filterButton).toBeDefined();
    fireEvent.click(filterButton!);

    // Check that onConfirm was called with the right values
    expect(onConfirmMock).toHaveBeenCalledWith({
      type: 'complaint',
      subtype: 'all',
      startDate,
      endDate
    });

    expect(onHideMock).toHaveBeenCalled();
  });

  it('handles date range correctly when only one date is provided', async () => {
    const startDate = new Date('2023-01-01');

    render(
      <Filter
        {...defaultProps}
        currentFilters={{
          type: 'all',
          subtype: 'all',
          startDate,
          endDate: undefined
        }}
      />
    );

    // Click the filter button
    const buttons = screen.getAllByRole('button');
    const filterButton = buttons.find(button =>
      button.textContent === 'filter' && !button.hasAttribute('data-variant')
    );
    expect(filterButton).toBeDefined();
    fireEvent.click(filterButton!);

    // Check that onConfirm was called with start date and current date for end date
    expect(onConfirmMock).toHaveBeenCalledWith(
      expect.objectContaining({
        startDate,
        endDate: expect.any(Date)
      })
    );
  });

  it('swaps dates if start date is after end date', async () => {
    const laterDate = new Date('2023-12-31');
    const earlierDate = new Date('2023-01-01');

    render(
      <Filter
        {...defaultProps}
        currentFilters={{
          type: 'all',
          subtype: 'all',
          startDate: laterDate,  // Intentionally set later date as start
          endDate: earlierDate   // And earlier date as end
        }}
      />
    );

    // Click the filter button
    const buttons = screen.getAllByRole('button');
    const filterButton = buttons.find(button =>
      button.textContent === 'filter' && !button.hasAttribute('data-variant')
    );
    expect(filterButton).toBeDefined();
    fireEvent.click(filterButton!);

    // Check that onConfirm was called with swapped dates
    expect(onConfirmMock).toHaveBeenCalledWith({
      type: 'all',
      subtype: 'all',
      startDate: earlierDate,
      endDate: laterDate
    });
  });

  it('preserves the complaint type when filter button is clicked', async () => {
    vi.clearAllMocks();

    // Render with complaint type already selected
    render(
      <Filter
        {...defaultProps}
        currentFilters={{
          type: 'complaint',
          subtype: 'all',
          startDate: undefined,
          endDate: undefined
        }}
      />
    );

    // Click the filter button
    const buttons = screen.getAllByRole('button');
    const filterButton = buttons.find(button =>
      button.textContent === 'filter' && !button.hasAttribute('data-variant')
    );
    expect(filterButton).toBeDefined();
    fireEvent.click(filterButton!);

    // Verify that the type value is preserved
    expect(onConfirmMock).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'complaint',
        subtype: 'all'
      })
    );
  });
});
