import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import Reserve from "./reserve";

// Mock translations
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => {
      const translations: Record<string, string> = {
        "reserve.title": "Reserve Title",
        "reserve.description": "Reserve Description",
        "reserve.cancel": "Cancel",
        "reserve.confirm": "Confirm",
      };
      return translations[key] ?? key;
    },
  }),
}));

// Mock SVG import properly with default export
vi.mock("@mass/shared/assets/pattern_decorative.svg", () => ({
  default: "pattern-svg-mock-url"
}));

// Mock FeaturedIcon component - Adding name as an attribute to fix the test
vi.mock("@mass/shared/components/atoms/featured-icon", () => ({
  default: ({ name, className }: { name: string; className?: string }) => (
    // @ts-expect-error: <div> doesn’t normally accept a “name” attribute, we keep it for testing
    <div data-testid="featured-icon" className={className} name={name}>
      {name}
    </div>
  ),
}));

// Mock Button component 
vi.mock("@mass/shared/components/ui/button", () => ({
  Button: ({ children, variant, onClick, className }: any) => (
    <button 
      onClick={onClick} 
      className={className}
      data-variant={variant}
      data-testid={`button-${variant}`}
    >
      {children}
    </button>
  ),
}));

describe("Reserve Component", () => {
  const mockOnHide = vi.fn();
  const mockOnConfirm = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it("renders correctly with all elements", () => {
    render(
      <Reserve 
        onHide={mockOnHide} 
        onConfirm={mockOnConfirm}
        activeTab="subscriptions"
      />
    );
    
    // Check if pattern image is rendered
    expect(screen.getByAltText("pattern")).toBeInTheDocument();
    
    // Check if featured icon is rendered with correct class
    const iconElement = screen.getByTestId("featured-icon");
    expect(iconElement).toBeInTheDocument();
    expect(iconElement).toHaveClass("-mt-16");
    expect(iconElement).toHaveAttribute("name", "untitled:trash-01");
    
    // Check if title and description are rendered
    expect(screen.getByText("Reserve Title")).toBeInTheDocument();
    expect(screen.getByText("Reserve Description")).toBeInTheDocument();
    
    // Check if buttons are rendered
    expect(screen.getByText("Cancel")).toBeInTheDocument();
    expect(screen.getByText("Confirm")).toBeInTheDocument();
    expect(screen.getByTestId("button-outline")).toBeInTheDocument();
    expect(screen.getByTestId("button-destructive")).toBeInTheDocument();
  });
  
  it("calls onHide when cancel button is clicked", () => {
    render(
      <Reserve 
        onHide={mockOnHide} 
        onConfirm={mockOnConfirm}
        activeTab="subscriptions"
      />
    );
    
    fireEvent.click(screen.getByText("Cancel"));
    expect(mockOnHide).toHaveBeenCalledTimes(1);
    expect(mockOnConfirm).not.toHaveBeenCalled();
  });
  
  it("calls both onConfirm and onHide when confirm button is clicked", () => {
    render(
      <Reserve 
        onHide={mockOnHide} 
        onConfirm={mockOnConfirm}
        activeTab="subscriptions"
      />
    );
    
    fireEvent.click(screen.getByText("Confirm"));
    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
    expect(mockOnHide).toHaveBeenCalledTimes(1);
  });
  
  it("only calls onHide when confirm button is clicked without onConfirm prop", () => {
    render(
      <Reserve 
        onHide={mockOnHide}
        activeTab="subscriptions"
      />
    );
    
    fireEvent.click(screen.getByText("Confirm"));
    expect(mockOnHide).toHaveBeenCalledTimes(1);
  });
  
  it("renders with facilities tab", () => {
    render(
      <Reserve 
        onHide={mockOnHide}
        onConfirm={mockOnConfirm}
        activeTab="facilities" 
      />
    );
    
    // The component should render the same regardless of activeTab
    expect(screen.getByText("Reserve Title")).toBeInTheDocument();
    expect(screen.getByText("Reserve Description")).toBeInTheDocument();
  });
  
  it("handles promise-based onConfirm function", async () => {
    const asyncOnConfirm = vi.fn().mockImplementation(() => Promise.resolve());
    
    render(
      <Reserve 
        onHide={mockOnHide}
        onConfirm={asyncOnConfirm}
        activeTab="subscriptions" 
      />
    );
    
    fireEvent.click(screen.getByText("Confirm"));
    expect(asyncOnConfirm).toHaveBeenCalledTimes(1);
    expect(mockOnHide).toHaveBeenCalledTimes(1);
  });
  
  it("renders with custom title and description props (though not used in component)", () => {
    render(
      <Reserve
        onHide={mockOnHide}
        onConfirm={mockOnConfirm}
        activeTab="subscriptions"
        title="Custom Title"
        description="Custom Description"
      />
    );
    
    // The component should still use translation values rather than props
    expect(screen.getByText("Reserve Title")).toBeInTheDocument();
    expect(screen.getByText("Reserve Description")).toBeInTheDocument();
  });
});
