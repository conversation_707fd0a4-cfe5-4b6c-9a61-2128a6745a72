import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import { ComplaintsTableSkeleton, ComplaintFormSkeleton, ComplaintsDetailSkeleton } from './skeleton-loader';

describe('Skeleton Loader Components', () => {
  describe('ComplaintsTableSkeleton', () => {
    it('renders correctly with all skeleton elements', () => {
      const { container } = render(<ComplaintsTableSkeleton />);
      
      // Check for top bar skeletons
      const topSkeletons = container.querySelectorAll('.flex.items-center.justify-between > div');
      expect(topSkeletons.length).toBe(2);
      
      // Check for table rows (5 rows plus header)
      const tableRows = container.querySelectorAll('.border-b.border-gray-100');
      expect(tableRows.length).toBeGreaterThanOrEqual(6);
      
      // Check for skeletons in rows
      const rowSkeletons = container.querySelectorAll('.h-5');
      expect(rowSkeletons.length).toBeGreaterThanOrEqual(15);
    });
  });

  describe('ComplaintFormSkeleton', () => {
    it('renders with 5 form field skeletons', () => {
      const { container } = render(<ComplaintFormSkeleton />);
      
      // Check for label skeletons
      const labelSkeletons = container.querySelectorAll('.h-4.w-24');
      expect(labelSkeletons.length).toBe(5);
      
      // Check for input field skeletons
      const inputSkeletons = container.querySelectorAll('.h-10.w-full');
      expect(inputSkeletons.length).toBe(6); // 5 form fields + submit button
      
      // Check for submit button skeleton
      const submitButtonSkeleton = container.querySelector('.h-10.w-full.mt-6');
      expect(submitButtonSkeleton).toBeTruthy();
    });
  });

  describe('ComplaintsDetailSkeleton', () => {
    it('renders with card header and content sections', () => {
      const { container } = render(<ComplaintsDetailSkeleton />);
      
      // Look for Card structure
      const card = container.querySelector('[class*="rounded-none"]');
      expect(card).toBeTruthy();
      
      // Check for header action buttons (3 skeletons in the header)
      const headerButtons = container.querySelectorAll('.h-8');
      expect(headerButtons.length).toBe(3);
      
      // Check for main content area
      const contentArea = container.querySelector('.space-y-6');
      expect(contentArea).toBeTruthy();
      
      // Check for three main sections in the content area
      const mainSections = contentArea?.children;
      expect(mainSections?.length).toBe(3);
      
      // Check for invoice/contract section (first section with border-b)
      const invoiceSection = container.querySelector('.border-b.border-gray-200');
      expect(invoiceSection).toBeTruthy();
      
      // Check for grid layout in second section
      const gridSection = container.querySelector('.grid.gap-2');
      expect(gridSection).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('should have proper structure for skeleton elements', () => {
      const { container } = render(
        <>
          <ComplaintsTableSkeleton />
          <ComplaintFormSkeleton />
          <ComplaintsDetailSkeleton />
        </>
      );
      
      // Check that skeleton elements are rendered (with h-* classes)
      const heightElements = container.querySelectorAll('[class*="h-"]');
      expect(heightElements.length).toBeGreaterThan(0);
      
      // Check if we can find some specific skeleton sizes we know should exist
      const smallSkeletons = container.querySelectorAll('.h-4, .h-5');
      expect(smallSkeletons.length).toBeGreaterThan(0);
      
      const buttonSkeletons = container.querySelectorAll('.h-8');
      expect(buttonSkeletons.length).toBeGreaterThan(0);
      
      const inputSkeletons = container.querySelectorAll('.h-10');
      expect(inputSkeletons.length).toBeGreaterThan(0);
    });
  });
});
