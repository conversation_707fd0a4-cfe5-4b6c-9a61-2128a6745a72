import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import StatusBadge from './status-badge';

describe('StatusBadge', () => {
  it('renders with pending status correctly', () => {
    render(<StatusBadge status="pending" />);
    const badge = screen.getByText('pending');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('bg-amber-50');
    expect(badge).toHaveClass('text-amber-800');
  });

  it('renders with resolved status correctly', () => {
    render(<StatusBadge status="resolved" />);
    const badge = screen.getByText('resolved');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('bg-green-50');
    expect(badge).toHaveClass('text-green-800');
  });

  it('renders with in process status correctly', () => {
    render(<StatusBadge status="in process" />);
    const badge = screen.getByText('in process');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('bg-purple-50');
    expect(badge).toHaveClass('text-purple-800');
  });

  it('renders with rejected status correctly', () => {
    render(<StatusBadge status="rejected" />);
    const badge = screen.getByText('rejected');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('bg-red-50');
    expect(badge).toHaveClass('text-red-800');
  });
});
