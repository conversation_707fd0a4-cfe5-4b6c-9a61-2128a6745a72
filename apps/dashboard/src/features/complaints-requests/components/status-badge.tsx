export type Status = "pending" | "resolved" | "rejected" | "in process";

export interface StatusBadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  status: Status;
}

// Simple utility function to concatenate class names
function classNames(...classes: (string | undefined | null | false)[]) {
  return classes.filter(Boolean).join(' ');
}

export default function StatusBadge({ status, className, ...props }: StatusBadgeProps) {
  const colors = {
    pending: "bg-amber-50 border-amber-200 text-amber-800",
    resolved: "bg-green-50 border-green-200 text-green-800",
    rejected: "bg-red-50 border-red-200 text-red-800",
    "in process": "bg-purple-50 border-purple-200 text-purple-800"
  };

  const baseClasses = "px-2 py-1 text-xs font-medium rounded-full capitalize";

  return (
    <span
      className={classNames(baseClasses, colors[status], className)}
      {...props}
    >
      {status}
    </span>
  );
}
