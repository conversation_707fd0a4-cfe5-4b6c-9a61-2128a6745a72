import { describe, it, expect, vi, beforeEach, afterEach, afterAll } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import * as React from 'react';
import type { ApiQueryParams } from "@/services/types";

// Mock the API services
vi.mock('@/services/api/complaints', () => ({
  fetchComplaintById: vi.fn(),
  fetchComplaints: vi.fn(),
  fetchDocument: vi.fn(),
  createComplaint: vi.fn(),
  uploadDocument: vi.fn(),
  markDocumentAsDone: vi.fn(),
  cancelComplaint: vi.fn()
}));

import {
  useComplaint,
  useComplaints,
  useDocuments,
  getComplaintRequests,
  useCreateComplaint,
  useSubscriptionById,
  useUploadDocument,
  useMarkDocumentAsDone,
  useCancelComplaint,
  useComplaintCategories,
  processComplaintData,
  useComplaintDetails,
  fetchComplaintDetails,
  getComplaintCategories
} from './queries';

// Import mocked functions
import {
  fetchComplaintById,
  fetchComplaints,
  fetchDocument,
  createComplaint,
  uploadDocument,
  markDocumentAsDone,
  cancelComplaint
} from '@/services/api/complaints';

// Mock fetch API
const mockFetch = vi.fn();
global.fetch = mockFetch as unknown as typeof fetch;

// Test wrapper for react-query hooks
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false
      }
    }
  });
  
  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return React.createElement(QueryClientProvider, { client: queryClient, children });
  };
};

describe('Complaints Queries', () => {
  const originalFetch = global.fetch;
  
  beforeEach(() => {
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  afterAll(() => {
    global.fetch = originalFetch;
  });

  describe('useComplaint', () => {
    it('should return complaint data when id is provided', async () => {
      const mockComplaint = { id: '123', title: 'Test Complaint' };
      (fetchComplaintById as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(mockComplaint);

      const { result } = renderHook(() => useComplaint('123'), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current).toEqual(mockComplaint);
      });

      expect(fetchComplaintById).toHaveBeenCalledWith('123');
    });

    it('should not fetch when id is undefined', () => {
      renderHook(() => useComplaint(undefined), {
        wrapper: createWrapper()
      });

      expect(fetchComplaintById).not.toHaveBeenCalled();
    });
  });

  describe('useComplaints', () => {
    it('should fetch complaints with default pagination', async () => {
      const mockResponse = { 
        content: [{ id: '1', title: 'Complaint 1' }],
        totalElements: 1,
        totalPages: 1
      };
      
      (fetchComplaints as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useComplaints(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.data).toEqual(mockResponse);
      });

      expect(fetchComplaints).toHaveBeenCalledWith({
        pageNumber: 1,
        pageSize: 10,
        orderBy: 'createdAt:desc'
      });
    });

    it('should fetch complaints with custom pagination and options', async () => {
      const mockResponse = { 
        content: [{ id: '1', title: 'Complaint 1' }],
        totalElements: 1,
        totalPages: 1
      };
      
      (fetchComplaints as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(mockResponse);

      // Use only valid ApiQueryParams properties
      const customOptions: Partial<ApiQueryParams> = {
        pageNumber: 2,
        pageSize: 20,
        orderBy: 'name:asc'
      };

      const { result } = renderHook(() => useComplaints(2, 20, customOptions), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.data).toEqual(mockResponse);
      });

      expect(fetchComplaints).toHaveBeenCalledWith({
        pageNumber: 2,
        pageSize: 20,
        orderBy: 'createdAt:desc',
        ...customOptions
      });
    });
  });

  describe('useDocuments', () => {
    it('should fetch multiple documents by ids', async () => {
      const mockDocuments = [
        { id: '1', name: 'Document 1' },
        { id: '2', name: 'Document 2' }
      ];
      
      (fetchDocument as unknown as ReturnType<typeof vi.fn>)
        .mockResolvedValueOnce(mockDocuments[0])
        .mockResolvedValueOnce(mockDocuments[1]);

      const { result } = renderHook(() => useDocuments(['1', '2']), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.data).toEqual(mockDocuments);
      });

      expect(fetchDocument).toHaveBeenCalledTimes(2);
      expect(fetchDocument).toHaveBeenCalledWith('1');
      expect(fetchDocument).toHaveBeenCalledWith('2');
    });

    it('should not fetch when no document ids are provided', () => {
      renderHook(() => useDocuments([]), {
        wrapper: createWrapper()
      });

      expect(fetchDocument).not.toHaveBeenCalled();
    });
  });

  describe('getComplaintRequests', () => {
    it('should fetch complaints with given parameters', async () => {
      const mockComplaints = [{ id: '1', title: 'Complaint 1' }];
      (fetchComplaints as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ content: mockComplaints });

      // Use valid options that match the ApiQueryParams type
      const options: Partial<ApiQueryParams> = {
        pageNumber: 2,
        pageSize: 15
      };

      const result = await getComplaintRequests(2, 15, options);

      expect(result).toEqual(mockComplaints);
      expect(fetchComplaints).toHaveBeenCalledWith({
        pageNumber: 2,
        pageSize: 15,
        ...options
      });
    });
  });

  describe('useCreateComplaint', () => {
    it('should create complaint with proper payload conversion', async () => {
      const complaintData = {
        applicationDescription: 'Test complaint',
        applicationType: 'category1',
        applicationCategory: 'subcategory1',
        subscriptionId: '123',
        file: ['file1', 'file2']
      };

      const expectedPayload = {
        body: 'Test complaint',
        category: 'category1',
        subcategory: 'subcategory1',
        subscriptionId: '123',
        files: ['file1', 'file2']
      };

      (createComplaint as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ id: 'new-complaint' });

      const { result } = renderHook(() => useCreateComplaint(), {
        wrapper: createWrapper()
      });

      const mutatePromise = result.current.mutateAsync(complaintData);
      await expect(mutatePromise).resolves.toEqual({ id: 'new-complaint' });

      expect(createComplaint).toHaveBeenCalledWith(expectedPayload);
    });

    it('useCreateComplaint should work with single string file', async () => {
      const data = {
        applicationDescription: 'Body',
        applicationType: 'type',
        applicationCategory: 'sub',
        subscriptionId: 'sub123',
        file: 'f1'
      };
      const expected = { body: 'Body', category: 'type', subcategory: 'sub', subscriptionId: 'sub123', files: ['f1'] };
      (createComplaint as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ ok: true });

      const { result } = renderHook(() => useCreateComplaint(), { wrapper: createWrapper() });
      await result.current.mutateAsync(data as any);

      expect(createComplaint).toHaveBeenCalledWith(expected);
    });

    it('useCreateComplaint should handle undefined file', async () => {
      const data = {
        applicationDescription: 'Body',
        applicationType: 'type',
        applicationCategory: 'sub',
        subscriptionId: 'sub123',
      };
      const expected = { body: 'Body', category: 'type', subcategory: 'sub', subscriptionId: 'sub123', files: [] };
      (createComplaint as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ ok: true });

      const { result } = renderHook(() => useCreateComplaint(), { wrapper: createWrapper() });
      await result.current.mutateAsync(data as any);

      expect(createComplaint).toHaveBeenCalledWith(expected);
    });
  });

  describe('useSubscriptionById', () => {
    it('should fetch subscription by id', async () => {
      const mockSubscription = { id: '123', name: 'Test Subscription' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockSubscription)
      });

      const { result } = renderHook(() => useSubscriptionById('123'), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.data).toEqual(mockSubscription);
      });

      expect(mockFetch).toHaveBeenCalledWith('/api/subscription/123');
    });
  });

  describe('processComplaintData', () => {
    it('should properly format complaint data', () => {
      const input = {
        body: 'Test complaint',
        category: 'category1',
        subcategory: 'subcategory1',
        subscriptionId: '123',
        files: ['file1', 'file2']
      };

      const result = processComplaintData(input);

      expect(result).toEqual(input);
    });

    it('should add empty files array if not provided', () => {
      const input = {
        body: 'Test complaint',
        category: 'category1',
        subcategory: 'subcategory1',
        subscriptionId: '123'
      };

      const result = processComplaintData(input);

      expect(result).toEqual({
        ...input,
        files: []
      });
    });
  });

  describe('getComplaintCategories', () => {
    it('should fetch complaint categories', async () => {
      const mockCategories = {
        value: {
          category1: {
            name: 'Category 1',
            subcategories: {
              sub1: { name: 'Sub 1' }
            }
          }
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCategories)
      });

      const result = await getComplaintCategories();

      expect(result).toEqual(mockCategories.value);
      expect(mockFetch).toHaveBeenCalledWith('/api/setting/global/complaints.categories');
    });

    it('useComplaintCategories should return all categories when includeDeleted is true', async () => {
      const mockCategories = {
        value: {
          cat1: { name: 'Cat 1', deleted: 'false', subcategories: {} },
          cat2: { name: 'Cat 2', deleted: 'true',  subcategories: {} },
        }
      };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCategories)
      });

      const { result } = renderHook(() => useComplaintCategories(true), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current).toEqual(mockCategories.value);
      });
    });
  });

  describe('useUploadDocument', () => {
    it('should upload a document', async () => {
      const mockFile = new File(['dummy'], 'file.txt', { type: 'text/plain' });
      (uploadDocument as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ id: 'fileId' });

      const { result } = renderHook(() => useUploadDocument(), {
        wrapper: createWrapper()
      });

      const res = await result.current.mutateAsync(mockFile);
      expect(res).toEqual({ id: 'fileId' });
      expect(uploadDocument).toHaveBeenCalledWith(mockFile);
    });
  });

  describe('useMarkDocumentAsDone', () => {
    it('should mark document as done', async () => {
      (markDocumentAsDone as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ status: 'done' });

      const { result } = renderHook(() => useMarkDocumentAsDone(), {
        wrapper: createWrapper()
      });

      const res = await result.current.mutateAsync('doc123');
      expect(res).toEqual({ status: 'done' });
      expect(markDocumentAsDone).toHaveBeenCalledWith('doc123');
    });
  });

  describe('useCancelComplaint', () => {
    it('should cancel complaint and invalidate queries', async () => {
      // allow setTimeout assertions
      vi.useFakeTimers();
      (cancelComplaint as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ success: true });

      // custom QueryClient to spy on its methods
      const queryClient = new QueryClient();
      const invalidateSpy = vi.spyOn(queryClient, 'invalidateQueries');
      const refetchSpy = vi.spyOn(queryClient, 'refetchQueries');

      const Wrapper = ({ children }: { children: React.ReactNode }) =>
        React.createElement(QueryClientProvider, { client: queryClient, children });

      const { result } = renderHook(() => useCancelComplaint(), { wrapper: Wrapper });

      await result.current.mutateAsync('complaint123');

      // fast-forward timers so the setTimeout in onSuccess runs
      vi.runAllTimers();

      expect(cancelComplaint).toHaveBeenCalledWith('complaint123');
      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: ['complaints'] });
      expect(refetchSpy).toHaveBeenCalledWith({ queryKey: ['complaints'] });

      vi.useRealTimers();
    });
  });

  describe('useComplaintCategories', () => {
    it('should exclude deleted categories and subcategories when includeDeleted is false', async () => {
      const mockCategories = {
        value: {
          cat1: { name: 'Cat 1', deleted: 'false', subcategories: { sub1: { name: 'Sub 1', deleted: 'false' } } },
          cat2: { name: 'Cat 2', deleted: 'true', subcategories: { sub2: { name: 'Sub 2', deleted: 'false' } } },
          cat3: { name: 'Cat 3', deleted: 'false', subcategories: { sub3: { name: 'Sub 3', deleted: 'true' } } },
        }
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCategories)
      });

      const { result } = renderHook(() => useComplaintCategories(false), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current).toEqual({
          cat1: { name: 'Cat 1', deleted: 'false', subcategories: { sub1: { name: 'Sub 1', deleted: 'false' } } }
        });
      });
    });
  });

  describe('fetchComplaintDetails', () => {
    it('should throw when the response is not ok', async () => {
      mockFetch.mockResolvedValueOnce({ ok: false, status: 404, statusText: 'Not Found' });

      await expect(fetchComplaintDetails('unknown')).rejects.toMatchObject({ status: 404 });
      expect(mockFetch).toHaveBeenCalledWith('/api/complaint/unknown');
    });
  });

  // ---------------- additional branch-coverage tests ----------------
  describe('Extra branch coverage', () => {
    it('useComplaintCategories should return all categories when includeDeleted is true', async () => {
      const mockCategories = {
        value: {
          cat1: { name: 'Cat 1', deleted: 'false', subcategories: {} },
          cat2: { name: 'Cat 2', deleted: 'true',  subcategories: {} },
        }
      };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCategories)
      });

      const { result } = renderHook(() => useComplaintCategories(true), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current).toEqual(mockCategories.value);
      });
    });

    it('useCreateComplaint should work with single string file', async () => {
      const data = {
        applicationDescription: 'Body',
        applicationType: 'type',
        applicationCategory: 'sub',
        subscriptionId: 'sub123',
        file: 'f1'
      };
      const expected = { body: 'Body', category: 'type', subcategory: 'sub', subscriptionId: 'sub123', files: ['f1'] };
      (createComplaint as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ ok: true });

      const { result } = renderHook(() => useCreateComplaint(), { wrapper: createWrapper() });
      await result.current.mutateAsync(data as any);

      expect(createComplaint).toHaveBeenCalledWith(expected);
    });

    it('useCreateComplaint should handle undefined file', async () => {
      const data = {
        applicationDescription: 'Body',
        applicationType: 'type',
        applicationCategory: 'sub',
        subscriptionId: 'sub123',
      };
      const expected = { body: 'Body', category: 'type', subcategory: 'sub', subscriptionId: 'sub123', files: [] };
      (createComplaint as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ ok: true });

      const { result } = renderHook(() => useCreateComplaint(), { wrapper: createWrapper() });
      await result.current.mutateAsync(data as any);

      expect(createComplaint).toHaveBeenCalledWith(expected);
    });

    it('useComplaints should normalise negative page numbers to 1', async () => {
      (fetchComplaints as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ content: [] });
      renderHook(() => useComplaints(-5, 10), { wrapper: createWrapper() });

      expect(fetchComplaints).toHaveBeenCalledWith(expect.objectContaining({ pageNumber: 1 }));
    });

    it('getComplaintRequests should normalise negative page numbers to 1', async () => {
      (fetchComplaints as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ content: [] });
      await getComplaintRequests(-3, 10);

      expect(fetchComplaints).toHaveBeenCalledWith(expect.objectContaining({ pageNumber: 1 }));
    });
  });
  // ---------------- end extra tests ----------------
});
