/**
 * @vitest-environment jsdom
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import React from 'react';

import { useComplaintCategories, useComplaints } from './data/queries';
import { useSubscriptions } from '../subscriptions/data/queries';
import { useModal } from '@mass/shared/components/organisms/modal/provider';
import { useDisabled, useIsDisabled } from '@/utils/use-disabled';
import ComplaintsRequestsPage from '.';

vi.mock('./data/queries', () => ({
  useComplaints: vi.fn(),
  useComplaintCategories: vi.fn(),
}));

vi.mock('../subscriptions/data/queries', () => ({
  useSubscriptions: vi.fn(),
}));

vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: vi.fn(),
}));

vi.mock('@/utils/use-disabled', () => ({
  useDisabled: vi.fn(),
  useIsDisabled: vi.fn(),
}));

vi.mock('@mass/shared/components/organisms/layout/header', () => ({
  Header: ({ title, description, children }: { title: string, description: string, children?: React.ReactNode }) => (
    <div data-testid="header">
      <h1>{title}</h1>
      <p>{description}</p>
      {children}
    </div>
  ),
}));

vi.mock('@mass/shared/components/organisms/layout/main', () => ({
  Main: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="main">{children}</div>
  ),
}));

const mockSetSearchQuery = vi.fn();
vi.mock('@mass/shared/hooks/use-table-filter', () => ({
  useTableFilter: () => ({
    searchQuery: 'test query',
    setSearchQuery: mockSetSearchQuery
  })
}));

vi.mock('@mass/shared/hooks/use-debounce', () => ({
  useDebounce: (value: any) => value
}));

const mockNavigate = vi.fn();
vi.mock('@tanstack/react-router', () => ({
  useNavigate: () => mockNavigate,
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'page.title': 'Complaints & Requests',
        'page.description': 'Manage your complaints and requests',
        'tabs.active': 'Active',
        'tabs.old': 'Resolved',
        'filter': 'Filter',
        'empty.button': 'Create New Request',
        'empty.title': 'No Complaints Yet',
        'empty.description': 'Create your first complaint or request',
        'empty.past_description': 'No resolved requests yet',
        'common.clear_filters': 'Clear Filters',
        'no_complaints_description_no_filters': 'No matching complaints found',
        'create_modal.title': 'Create Complaint',
        'create_modal.description': 'Submit a new complaint or request',
        'search_placeholder': 'Search complaints'
      };
      return translations[key] || key;
    },
    i18n: {
      language: 'en',
      changeLanguage: vi.fn(),
    }
  }),
}));

vi.mock('@mass/shared/components/ui/tabs', () => ({
  Tabs: ({ children, value, onValueChange }: { children: React.ReactNode, value: string, onValueChange: (value: string) => void }) => (
    <div data-testid="tabs" data-value={value} onClick={() => onValueChange('old')}>
      {children}
    </div>
  ),
  TabsList: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tabs-list">{children}</div>
  ),
  TabsTrigger: ({ children, value, 'data-testid': testId }: { 
    children: React.ReactNode, 
    value: string,
    'data-testid'?: string,
    className?: string
  }) => (
    <button data-testid={testId || `${value}-tab`}>{children}</button>
  ),
}));

vi.mock('@mass/shared/components/molecules/table-transition', () => ({
  AnimatedTransition: ({ children }: { children: React.ReactNode }) => <>{children}</>
}));

vi.mock('@mass/shared/components/atoms/Iconify', () => ({
  default: ({ name }: { name: string }) => <span data-testid="icon">{name}</span>
}));

vi.mock('@mass/shared/components/atoms/empty', () => ({
  default: ({ title, description, children }: { title: string, description: string, children?: React.ReactNode }) => (
    <div data-testid="empty-state">
      <h3>{title}</h3>
      <p>{description}</p>
      {children}
    </div>
  )
}));

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, variant, 'data-testid': testId }: { 
    children: React.ReactNode, 
    onClick?: () => void, 
    variant?: string,
    'data-testid'?: string 
  }) => (
    <button 
      data-testid={testId || "button"} 
      data-variant={variant} 
      onClick={onClick}
    >
      {children}
    </button>
  ),
}));

interface MockDataItem {
  id: string;
  title: string;
  [key: string]: any;
}

vi.mock('@mass/shared/components/organisms/table/index', () => ({
  DataTable: ({ 
    data, 
    onRowClick, 
    empty 
  }: { 
    data: MockDataItem[]; 
    onRowClick?: (item: MockDataItem) => void; 
    empty: React.ReactNode 
  }) => (
    <div data-testid="data-table">
      {data && data.length > 0 ? (
        data.map(item => (
          <div key={item.id} onClick={() => onRowClick && onRowClick(item)}>
            {item.title}
          </div>
        ))
      ) : (
        empty
      )}
    </div>
  ),
}));

describe('ComplaintsRequestsPage', () => {
  const mockModalOpen = vi.fn();

  const mockComplaints = {
    content: [
      {
        id: '1',
        title: 'Test Complaint',
        body: 'This is a test complaint',
        status: 'PENDING',
        type: 'complaint',
        subtype: 'general',
        createdAt: '2023-01-01T00:00:00.000Z'
      }
    ],
    totalPages: 2,
    totalElements: 15,
    size: 10,
    number: 0,
    numberOfElements: 1,
    pageable: {},
    last: true,
    sort: {},
    first: true,
    empty: false
  };

  const mockSubscriptions = {
    pages: [
      {
        content: [{ id: '1', name: 'Test Subscription' }],
        totalElements: 1,
        pageable: {},
        last: true,
        sort: {},
        first: true,
        empty: false,
        totalPages: 1,
        size: 10,
        number: 0,
        numberOfElements: 1
      }
    ],
    pageParams: [null]
  };

  const mockCategories = [
    { id: '1', name: 'General', code: 'general' },
    { id: '2', name: 'Billing', code: 'billing' }
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(useComplaints).mockReturnValue({
      data: mockComplaints,
      isLoading: false,
      isError: false,
      error: null,
      isPending: false,
      isSuccess: true,
      refetch: vi.fn(),
      fetchStatus: 'idle',
      status: 'success',
    } as any);

    vi.mocked(useComplaintCategories).mockReturnValue({
      data: mockCategories,
      isLoading: false,
      isError: false,
      error: null,
      isPending: false,
      isSuccess: true,
      refetch: vi.fn(),
      fetchStatus: 'idle',
      status: 'success',
    } as any);

    vi.mocked(useSubscriptions).mockReturnValue({
      data: mockSubscriptions,
      isLoading: false,
      isError: false,
      error: null,
      isPending: false,
      isSuccess: true,
      fetchStatus: 'idle',
      status: 'success',
      fetchNextPage: vi.fn(),
      hasNextPage: false
    } as any);

    vi.mocked(useModal).mockReturnValue({
      open: mockModalOpen,
      modals: [],
      hide: vi.fn(),
      remove: vi.fn(),
      patch: vi.fn(),
      safeTryClose: vi.fn()
    } as any);

    vi.mocked(useDisabled).mockImplementation(() => {});
    vi.mocked(useIsDisabled).mockReturnValue(false);
  });

  it('renders title and description', () => {
    render(<ComplaintsRequestsPage />);
    expect(screen.getByText('Complaints & Requests')).toBeInTheDocument();
    expect(screen.getByText('Manage your complaints and requests')).toBeInTheDocument();
  });

  it('renders active and resolved tabs', () => {
    render(<ComplaintsRequestsPage />);
    expect(screen.getByTestId('active-tab')).toBeInTheDocument();
    expect(screen.getByTestId('resolved-tab')).toBeInTheDocument();
  });

  it('clicking "Create Complaint" opens modal', () => {
    render(<ComplaintsRequestsPage />);
    fireEvent.click(screen.getByTestId('create-complaint-button'));
    expect(mockModalOpen).toHaveBeenCalled();
  });

  it('displays empty state when there are no complaints', () => {
    vi.mocked(useComplaints).mockReturnValueOnce({
      data: {
        content: [],
        totalElements: 0,
        totalPages: 0,
        number: 0,
        empty: true,
        pageable: {},
        last: true,
        sort: {},
        first: true,
        size: 10,
        numberOfElements: 0
      },
      isLoading: false,
      isSuccess: true,
      isError: false,
      error: null,
      isPending: false,
      refetch: vi.fn(),
      fetchStatus: 'idle',
      status: 'success'
    } as any);
    
    render(<ComplaintsRequestsPage />);
    expect(screen.getByText('No Complaints Yet')).toBeInTheDocument();
  });

  it('respects permissions and hides "Create" button if disabled', () => {
    vi.mocked(useIsDisabled).mockReturnValue(true);
    render(<ComplaintsRequestsPage />);
    expect(screen.queryByTestId('create-complaint-button')).not.toBeInTheDocument();
  });

  it('navigates to complaint detail on row click if not disabled', async () => {
    render(<ComplaintsRequestsPage />);
    const row = screen.getByText('Test Complaint');
    fireEvent.click(row);
    expect(mockNavigate).toHaveBeenCalledWith({ to: '/complaints-requests/1' });
  });

  it('does not navigate when viewing is disabled', () => {
    vi.mocked(useIsDisabled).mockReturnValue(true);
    render(<ComplaintsRequestsPage />);
    const row = screen.getByText('Test Complaint');
    fireEvent.click(row);
    expect(mockNavigate).not.toHaveBeenCalled();
  });
  
  it('adjusts current page when page becomes out of bounds', async () => {
    const mockUseComplaintsMultiPage = vi.fn().mockReturnValue({
      data: {
        ...mockComplaints,
        totalPages: 2,
        number: 1 
      },
      isLoading: false,
      isError: false,
      error: null,
      isPending: false,
      isSuccess: true,
      refetch: vi.fn(),
      fetchStatus: 'idle',
      status: 'success'
    });
    
    vi.mocked(useComplaints).mockImplementation(mockUseComplaintsMultiPage);
    
    const { rerender } = render(<ComplaintsRequestsPage />);
    
    mockUseComplaintsMultiPage.mockReturnValue({
      data: {
        ...mockComplaints,
        content: [], 
        totalPages: 1,
        number: 1    
      },
      isLoading: false,
      isError: false,
      error: null,
      isPending: false,
      isSuccess: true,
      refetch: vi.fn(),
      fetchStatus: 'idle',
      status: 'success'
    });
    
  
    rerender(<ComplaintsRequestsPage />);
    
    await waitFor(() => {
      expect(useComplaints).toHaveBeenCalledWith(
        1, 
        10,
        expect.anything()
      );
    });
  });
  
  it('applies search filter correctly', async () => {
    const mockUseComplaints = vi.fn().mockReturnValue({
      data: mockComplaints,
      isLoading: false,
      isError: false,
      error: null,
      isPending: false,
      isSuccess: true,
      refetch: vi.fn(),
      fetchStatus: 'idle',
      status: 'success'
    });
    
    vi.mocked(useComplaints).mockImplementation(mockUseComplaints);
    
    render(<ComplaintsRequestsPage />);
    
    await waitFor(() => {
      expect(mockUseComplaints).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.objectContaining({
          'filter:ct': { body: 'test query' }
        })
      );
    });
  });
});
