import { sidebarData } from "@/constants/sidebar-data";
import { ApiQueryParams } from "@/services/types";
import Empty from "@mass/shared/components/atoms/empty";
import Iconify from "@mass/shared/components/atoms/Iconify";
import { AnimatedTransition } from "@mass/shared/components/molecules/table-transition";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { DataTable } from "@mass/shared/components/organisms/table/index";
import { Button } from "@mass/shared/components/ui/button";
import { useDebounce } from "@mass/shared/hooks/use-debounce";
import { useTableFilter } from "@mass/shared/hooks/use-table-filter";
import { SortingState } from "@tanstack/react-table";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSubscriptions } from "../subscriptions/data/queries";
import { columns } from "./components/columns";
import ComplaintApplicationModal from "./components/modals/complaint-application";
import { ComplaintsTableSkeleton } from "./components/skeleton-loader";
import StatusBadge from "./components/status-badge";
import { useComplaintCategories, useComplaints } from "./data/queries";
import { Tabs, TabsList, TabsTrigger } from "@mass/shared/components/ui/tabs";
import Filter from "./components/modals/filter";
import { useDisabled, useIsDisabled } from "@/utils/use-disabled";
import { useNavigate } from "@tanstack/react-router";

export default function ComplaintsRequestsPage() {
  useDisabled(["disabled.complaints.view"], "/settings");
  const isAddDisabled = useIsDisabled([
    "disabled.complaints.view",
    "disabled.complaints.add",
  ]);
  const navigate = useNavigate();
  const isDetailsDisabled = useIsDisabled([
    "disabled.complaints.view",
    "disabled.complaints.details",
  ]);

  const { t, i18n } = useTranslation("complaints");

  const searchInputRef = useRef<HTMLInputElement>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const { searchQuery, setSearchQuery } = useTableFilter();
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const isSearchActive =
    !!debouncedSearchQuery && debouncedSearchQuery.length > 0;

  const [pageSize] = useState(10);
  const { open } = useModal();
  const [sorting, setSorting] = useState<SortingState>([]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const { data: subscriptions } = useSubscriptions({ pageSize: 1 });
  const subscriptionCount = subscriptions?.pages?.[0]?.totalElements || 0;

  const [tab, setTab] = useState<"active" | "old">("active");

  const [filterStartDate, setFilterStartDate] = useState<Date | undefined>(
    undefined
  );
  const [filterEndDate, setFilterEndDate] = useState<Date | undefined>(
    undefined
  );
  const [filterType, setFilterType] = useState("all");
  const [filterSubtype, setFilterSubtype] = useState("all");

  const orderByParam =
    sorting.length > 0
      ? sorting
          .map((sort) => `${sort.id}:${sort.desc ? "desc" : "asc"}`)
          .join(",")
      : undefined;
  const queryParams: ApiQueryParams = {
    pageNumber: currentPage,
    pageSize,
    ...(orderByParam && { orderBy: orderByParam }),
    ...(isSearchActive && { "filter:ct": { body: debouncedSearchQuery } }),
    "filter:eq": {
      ...(filterType !== "all" ? { type: filterType } : {}),
      ...(filterSubtype !== "all" ? { subtype: filterSubtype } : {}),
      status: tab === "active" ? "PENDING" : ["RESOLVED", "CANCELED"],
    },
    ...(filterStartDate && filterEndDate
      ? {
          "filter:bw": {
            createdAt: [
              filterStartDate.toISOString(),
              filterEndDate.toISOString(),
            ],
          },
        }
      : {}),
  };

  const { data: paginatedComplaints, isLoading: loading } = useComplaints(
    currentPage,
    pageSize,
    queryParams
  );

  const complaintRequests = paginatedComplaints?.content || [];
  const totalPages = Math.max(paginatedComplaints?.totalPages || 1, 1);
  const hasActiveFilters =
    isSearchActive ||
    filterType !== "all" ||
    filterSubtype !== "all" ||
    filterStartDate ||
    filterEndDate;

  useEffect(() => {
    if (!loading && paginatedComplaints) {
      if (complaintRequests.length === 0 && totalPages > 0 && currentPage > 1) {
        const targetPage = Math.min(totalPages, currentPage - 1);
        setCurrentPage(targetPage);
      }
      
      else if (currentPage > totalPages && totalPages > 0) {
        setCurrentPage(totalPages);
      }
    }
  }, [
    paginatedComplaints,
    loading,
    totalPages,
    currentPage,
    complaintRequests.length,
  ]);

  const openCreate = () => {
    open(ComplaintApplicationModal, {
      name: "create-application-modal",
      header: {
        title: t("create_modal.title"),
        description: t("create_modal.description"),
      },
      viewMode: false,
    });
  };

  const openFilter = () => {
    // todo: any
    open(Filter as React.ComponentType<any>, {
      name: "filter-billing-modal",
      currentFilters: {
        type: filterType,
        subtype: filterSubtype,
        startDate: filterStartDate,
        endDate: filterEndDate,
      },
      // todo: any
      onConfirm: ({ type, subtype, startDate, endDate }: any) => {
        setFilterType(type);
        setFilterSubtype(subtype);
        setFilterStartDate(startDate);
        setFilterEndDate(endDate);
      },
      clearFilters,
    });
  };

  const clearFilters = () => {
    setSearchQuery("");
    setCurrentPage(1);
    setFilterType("all");
    setFilterSubtype("all");
    setFilterStartDate(undefined);
    setFilterEndDate(undefined);

    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(Math.max(newPage, 1));
  };

  const { data: categories } = useComplaintCategories();

  return (
    <>
      <Header
        title={t("page.title")}
        sidebarData={sidebarData as any}
        description={t("page.description")}
      />

      <Main>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between py-4 w-full gap-4 sm:gap-0">
          <Tabs
            value={tab}
            onValueChange={(value) => setTab(value as "active" | "old")}
            className="overflow-hidden rounded-lg border-2 border-muted w-full sm:w-auto"
          >
            <TabsList className="w-full sm:w-auto">
              <TabsTrigger value="active" className="w-full sm:flex-auto" data-testid="active-tab">{t("tabs.active")}</TabsTrigger>
              <TabsTrigger value="old" className="w-full sm:flex-auto" data-testid="resolved-tab">{t("tabs.old")}</TabsTrigger>
            </TabsList>
          </Tabs>
          <div className="gap-2 flex w-full sm:w-auto justify-end">
            {!!subscriptionCount && (
              <>
                <Button 
                  variant="outline" 
                  onClick={openFilter} 
                  className="flex-1 sm:flex-initial"
                  data-testid="filter-button"
                >
                  {t("filter")}
                  <Iconify name="untitled:filter-lines" className="size-5 ml-2" />
                </Button>
                {isAddDisabled ? null : (
                  <Button 
                    onClick={openCreate} 
                    className="flex-1 sm:flex-initial"
                    data-testid="create-complaint-button"
                  >
                    {t("empty.button")}
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
        <AnimatedTransition
          loading={loading}
          skeleton={<ComplaintsTableSkeleton />}
        >
          <DataTable
            columns={columns}
            data={complaintRequests}
            meta={{ t }}
            tableOptions={{
              state: {
                globalFilter: debouncedSearchQuery,
                pagination: {
                  pageIndex: Math.max(currentPage - 1, 0),
                  pageSize,
                },
                sorting,
              },
              manualPagination: true,
              manualSorting: true,
              pageCount: totalPages,
            }}
            isPaginated={!isSearchActive}
            onSortingChange={setSorting}
            onRowClick={(row) => {
              if (isDetailsDisabled) return;
              
              navigate({ to: `/complaints-requests/${row.id}` });
            }}
            // header={() => (
            //   <div className="flex items-center justify-between py-4 px-4">
            //     <div className="flex-1 w-full relative flex flex-col items-start justify-center gap-0.5 text-left text-lg text-gray-900 font-text-sm-regular">
            //       <div className="relative leading-[28px] font-semibold self-stretch flex flex-row items-center justify-start gap-2">
            //         {t("page.title")}
            //       </div>
            //       <div className="self-stretch relative text-sm leading-[20px] text-gray-600 overflow-hidden text-ellipsis whitespace-nowrap">
            //         {t("page.description")}
            //       </div>
            //     </div>
            //     <div className="flex gap-2">
            //       {/* <div className="flex-1 relative">
            //         <Iconify
            //           name="untitled:search-lg"
            //           className="size-4 absolute top-1/2 left-3 -translate-y-1/2 text-gray-400"
            //         />
            //         <Input
            //           placeholder={t("search_placeholder")}
            //           value={searchQuery}
            //           onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            //             setSearchQuery(e.target.value)
            //           }
            //           className="max-w-sm pl-10"
            //           ref={searchInputRef}
            //         />
            //       </div> */}
            //     </div>
            //   </div>
            // )}
            empty={
              <Empty
                title={t("empty.title")}
                description={t(
                  hasActiveFilters
                    ? "no_complaints_description_no_filters"
                    : tab === "old"
                      ? "empty.past_description"
                      : "empty.description"
                )}
              >
                {hasActiveFilters ? (
                  <div className="flex gap-4 mt-4">
                    <Button 
                      variant="outline" 
                      onClick={clearFilters}
                      data-testid="clear-filters-button"
                    >
                      <Iconify name="untitled:x" className="mr-2 size-4" />
                      {t("common.clear_filters", {
                        ns: "common",
                      })}
                    </Button>

                    {!!subscriptionCount &&
                      (isAddDisabled ? null : (
                        <Button onClick={openCreate}>
                          {t("empty.button")}
                        </Button>
                      ))}
                  </div>
                ) : (
                  !!subscriptionCount &&
                  (isAddDisabled ? null : (
                    <Button onClick={openCreate} className="mt-4">
                      {t("empty.button")}
                    </Button>
                  ))
                )}
              </Empty>
            }
            // className="w-full border rounded-2xl shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)] overflow-hidden"
            onPageChange={handlePageChange}
          />
        </AnimatedTransition>
      </Main>
    </>
  );
}
