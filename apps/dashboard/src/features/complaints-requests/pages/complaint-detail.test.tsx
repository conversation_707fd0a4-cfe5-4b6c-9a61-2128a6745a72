import { describe, expect, it, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import * as React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// --- mocks --------------------------------------------------------------
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (k: string) => k,
    i18n: { language: 'en' },
  }),
}));

vi.mock('@tanstack/react-router', () => ({
  // a very small stub – only what the component uses
  Link: ({ children }: { children: React.ReactNode }) => <a>{children}</a>,
  useParams: () => ({ complaintId: 'param-id' }),
}));

// regions hook
vi.mock('@/features/subscriptions/data/queries', () => ({
  useRegions: () => ({ data: [{ id: 'r1', name: 'Region-1' }] }),
}));

// ---- fix: mock subscriptions service safely --------------------------
vi.mock('@/services/api/subscriptions', () => {
  return {
    subscriptionService: { getById: vi.fn() }
  };
});

// after mocks we can import the mocked object
import { subscriptionService } from '@/services/api/subscriptions';

// --- safe hooks mock ----------------------------------------------------
vi.mock('../data/queries', () => ({
  __esModule: true,
  useComplaintDetails: vi.fn(),
  useComplaintCategories: vi.fn(),
  useDocuments: vi.fn(),
}));

// re-import the mocks created above to configure them in tests
import {
  useComplaintDetails,
  useComplaintCategories,
  useDocuments,
} from '../data/queries';

// component under test must be imported **after** mocks
import ComplaintDetailPage from './complaint-detail';

// helper wrapper with react-query provider
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={new QueryClient()}>{children}</QueryClientProvider>
);

// ---- additional safe mocks to avoid context errors --------------------
vi.mock('@mass/shared/components/organisms/layout/main', () => ({
  Main: ({ children, ...p }: any) => <main {...p}>{children}</main>,
}));
vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, ...p }: any) => <button {...p}>{children}</button>,
}));
vi.mock('@mass/shared/components/ui/skeleton', () => ({
  Skeleton: (p: any) => <div role="status" {...p} />,
}));
vi.mock('@mass/shared/components/ui/card', () => ({
  Card: ({ children, ...p }: any) => <section {...p}>{children}</section>,
  CardContent: ({ children, ...p }: any) => <div {...p}>{children}</div>,
}));
vi.mock('lucide-react', () => ({
  PaperclipIcon: (p: any) => <svg {...p} />,
}));
// --- adjust Header mock -------------------------------------------------
vi.mock('@mass/shared/components/organisms/layout/header', () => ({
  // include title prop in rendered output
  Header: ({ title, children, ...p }: any) => (
    <header {...p}>
      {/* expose title for tests */}
      {typeof title === 'string' ? title : title /* jsx element */ }
      {children}
    </header>
  ),
}));
// -----------------------------------------------------------------------

beforeEach(() => {
  vi.resetAllMocks();

  (useComplaintDetails as any).mockReturnValue({
    data: {
      body: 'B',
      type: 'T',
      subtype: 'ST',
      subscriptionId: 'sub-1',
      documents: ['doc-1'],
      status: 'OPEN',
      response: 'RSP',
    },
    isLoading: false,
    error: null,
  });

  // 👉 return shape with { data: … }
  (useComplaintCategories as any).mockReturnValue({
    data: {
      T: {
        label: { EN: 'Type-Label' },
        subcategories: { ST: { label: { EN: 'Sub-Label' } } },
      },
    },
  });

  (subscriptionService.getById as any).mockResolvedValue({
    name: 'Sub-Name',
    regionId: 'r1',
  });

  (useDocuments as any).mockReturnValue({
    data: [{ name: 'file.pdf', size: 2048, url: '/url' }],
  });
});

describe('ComplaintDetailPage', () => {
  it('renders skeletons while loading', () => {
    (useComplaintDetails as any).mockReturnValue({ data: undefined, isLoading: true, error: null });
    render(<ComplaintDetailPage complaintId="c1" />, { wrapper });

    // one of the skeleton placeholders
    expect(screen.getAllByRole('status').length).toBeGreaterThan(0);
  });

  it('renders error view when fetch fails', () => {
    (useComplaintDetails as any).mockReturnValue({ data: undefined, isLoading: false, error: new Error('fail') });
    render(<ComplaintDetailPage complaintId="c1" />, { wrapper });

    expect(screen.getByText('error.complaint_not_found')).toBeInTheDocument();
  });

  it('renders complaint details (happy path)', async () => {
    render(<ComplaintDetailPage complaintId="c1" />, { wrapper });

    await waitFor(() => expect(subscriptionService.getById).toHaveBeenCalled());

    // 🔄 match by regex to ignore internal span splits
    // ensure Type & Sub labels appear (can show multiple times)
    expect(screen.getAllByText(/Type-Label/).length).toBeGreaterThan(0);
    expect(screen.getAllByText(/Sub-Label/).length).toBeGreaterThan(0);
    expect(screen.getByText('B')).toBeInTheDocument();
    expect(screen.getByText('file.pdf')).toBeInTheDocument();
  });
});
