import { use<PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { sidebarData } from "@/constants/sidebar-data";
import { useRegions } from "@/features/subscriptions/data/queries";
import { subscriptionService } from "@/services/api/subscriptions";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { Button } from "@mass/shared/components/ui/button";
import { Skeleton } from "@mass/shared/components/ui/skeleton";
import { toast } from "sonner";
import StatusBadge from "../components/status-badge";
import { useComplaintCategories, useComplaintDetails, useDocuments } from "../data/queries";
import { PaperclipIcon } from "lucide-react";
import { Card, CardContent } from "@mass/shared/components/ui/card";
interface ComplaintDetail {
    id: string;
    body: string;
    type: string;
    subtype: string;
    subscriptionId: string;
    subscriptionName?: string;
    distributionCompany?: string;
    status?: string;
    createdAt?: string;
    updatedAt?: string;
    documents?: string[];
    response?: string;
}

interface ComplaintDetailPageProps {
    complaintId?: string;
}

export default function ComplaintDetailPage({ complaintId }: ComplaintDetailPageProps) {
    const { t, i18n } = useTranslation("complaints");
    const params = useParams({ strict: false });
    const id = complaintId || params.complaintId;

    const { data: complaintDetailsData, isLoading, error } = useComplaintDetails(id as string);
    const [complaintDetails, setComplaintDetails] = useState<ComplaintDetail | null>(null);
    const { data: regions } = useRegions();
    const { data: categories } = useComplaintCategories(false);

    useEffect(() => {
        if (complaintDetailsData && !complaintDetails) {
            const fetchSubscriptionDetails = async () => {
                try {
                    const foundSub = await subscriptionService.getById(
                        complaintDetailsData.subscriptionId
                    );

                    const regionName =
                        (regions ?? []).find((region) => region.id === foundSub?.regionId)
                            ?.name || t("unknown");

                    const transformedDetails: ComplaintDetail = {
                        id: complaintId as string,
                        body: complaintDetailsData.body || "",
                        type: complaintDetailsData.type || "",
                        subtype: complaintDetailsData.subtype || "",
                        subscriptionId: complaintDetailsData.subscriptionId || "",
                        subscriptionName: foundSub?.name || "",
                        documents: complaintDetailsData.documents || [],
                        response: complaintDetailsData.response || "",
                        status: complaintDetailsData.status,
                        distributionCompany: regionName,
                    };

                    setComplaintDetails(transformedDetails);
                } catch (error) {
                    toast.error(t("error.load_complaint_failed"));
                }
            };

            fetchSubscriptionDetails();
        }
    }, [complaintDetailsData, regions, complaintId, complaintDetails, t]);

    const documents = useDocuments(complaintDetails?.documents || []);

    const category = complaintDetails?.type ? categories?.[complaintDetails.type]?.label?.[i18n.language.toUpperCase()] : '';
    const subcategory = complaintDetails?.type && complaintDetails?.subtype ?
        categories?.[complaintDetails.type]?.subcategories?.[complaintDetails.subtype]?.label?.[i18n.language.toUpperCase()] : '';

    if (isLoading) {
        return (
            <>
                <Header
                    title={t("page.title")}
                    sidebarData={sidebarData as any}
                    description={t("page.description")}
                    breadcrumb={[
                        {
                            title: t("page.title"),
                            path: "/complaints-requests",
                            icon: "untitled:file-05"
                        },
                        {
                            title: `${category} — ${subcategory}`,
                        }
                    ]}
                />
                <Main>
                    <div className="w-full h-full flex flex-col space-y-4 p-4">
                        <Skeleton className="h-8 w-1/3" />
                        <Skeleton className="h-4 w-1/4" />
                        <Skeleton className="h-48 w-full" />
                        <Skeleton className="h-32 w-full" />
                    </div>
                </Main>
            </>
        );
    }

    if (error || !complaintDetails) {
        return (
            <>
                <Header
                    title={t("page.title")}
                    sidebarData={sidebarData as any}
                    description={t("page.description")}
                />
                <Main>
                    <div className="w-full h-full flex flex-col items-center justify-center space-y-4 p-4">
                        <h2 className="text-xl font-semibold">{t("error.complaint_not_found")}</h2>
                        <p>{t("error.load_complaint_failed")}</p>
                        <Button
                            onClick={() => window.history.back()}
                        >
                            {t("go_back", { ns: "common" })}
                        </Button>
                    </div>
                </Main>
            </>
        );
    }

    return (
        <>
            <Header
                title={
                    <>
                        {category} — {subcategory}
                        {complaintDetails.status && <StatusBadge status={complaintDetails.status as any} />}
                    </>
                }
                sidebarData={sidebarData as any}
                description={`${t("application_id")}: ${complaintDetails.id}`}
                breadcrumb={[
                    {
                        title: t("page.title"),
                        path: "/complaints-requests",
                        icon: "untitled:file-05"
                    },
                    {
                        title: `${category} — ${subcategory}`
                    }
                ]}
            />
            <Main fixed className="justify-start px-4 sm:px-6 mx-auto">
                <Card className="!px-0 py-6 w-full">
                    <CardContent className="space-y-6">
                        <dl className="divide-y divide-gray-100">
                            <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                <dt className="text-sm/6 font-medium text-gray-900">{t("subscription", { ns: "subscriptions" })}</dt>
                                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <Link
                                        to="/subscriptions/$subscriptionId"
                                        params={{ subscriptionId: complaintDetails.subscriptionId }}
                                        className="inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors bg-blue-50 border-blue-200 text-blue-500 hover:bg-blue-100 cursor-pointer"
                                    >
                                        {complaintDetails.subscriptionName}
                                    </Link>
                                </dd>
                            </div>
                            <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                <dt className="text-sm/6 font-medium text-gray-900">{t("application_category")}</dt>
                                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">{category}</dd>
                            </div>
                            <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                <dt className="text-sm/6 font-medium text-gray-900">{t("application_subcategory")}</dt>
                                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">{subcategory}</dd>
                            </div>
                            <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                <dt className="text-sm/6 font-medium text-gray-900">{t("distribution_company", { ns: "subscriptions" })}</dt>
                                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">{complaintDetails.distributionCompany}</dd>
                            </div>
                            <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                <dt className="text-sm/6 font-medium text-gray-900">{t("description")}</dt>
                                <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    {complaintDetails.body}
                                </dd>
                            </div>
                            {complaintDetails.response && (
                                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                    <dt className="text-sm/6 font-medium text-gray-900">{t("response")}</dt>
                                    <dd className="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                        {complaintDetails.response}
                                    </dd>
                                </div>
                            )}
                            {documents.data && documents.data.length > 0 && (
                                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                    <dt className="text-sm/6 font-medium text-gray-900">{t("files")}</dt>
                                    <dd className="mt-2 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <ul role="list" className="divide-y divide-gray-100 rounded-md border border-gray-200">
                                            {documents.data.map((document, index) => (
                                                <li key={index} className="flex items-center justify-between py-4 pr-5 pl-4 text-sm/6">
                                                    <div className="flex w-0 flex-1 items-center">
                                                        <PaperclipIcon className="size-5 shrink-0 text-gray-400" aria-hidden="true" />
                                                        <div className="ml-4 flex min-w-0 flex-1 gap-2">
                                                            <span className="truncate font-medium">{document.name}</span>
                                                            <span className="shrink-0 text-gray-400">{Math.round(document.size / 1024)}kb</span>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4 shrink-0">
                                                        <a href={document.url} target="_blank" rel="noopener noreferrer" className="font-medium text-primary hover:text-primary/80">
                                                            {t("download")}
                                                        </a>
                                                    </div>
                                                </li>
                                            ))}
                                        </ul>
                                    </dd>
                                </div>
                            )}
                        </dl>
                    </CardContent>
                </Card>
            </Main>
        </>
    );
}