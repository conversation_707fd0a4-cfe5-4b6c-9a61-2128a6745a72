/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { useNavigate, useRouter } from '@tanstack/react-router';
import ForbiddenError from './forbidden';

// Mock dependencies
vi.mock('@tanstack/react-router', () => ({
  useNavigate: vi.fn(),
  useRouter: vi.fn(),
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'forbidden.title': 'Access Forbidden',
        'forbidden.description': 'You do not have permission to access this resource.',
        'go_back': 'Go Back',
        'back_to_home': 'Back to Home',
      };
      return translations[key] || key;
    },
  }),
}));

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, variant, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} {...props}>
      {children}
    </button>
  ),
}));

const mockNavigate = vi.fn();
const mockHistoryGo = vi.fn();

describe('ForbiddenError', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useRouter as any).mockReturnValue({
      history: {
        go: mockHistoryGo,
      },
    });
  });

  it('should render forbidden error page correctly', () => {
    render(<ForbiddenError />);

    expect(screen.getByText('403')).toBeInTheDocument();
    expect(screen.getByText('Access Forbidden')).toBeInTheDocument();
    expect(screen.getByText('You do not have permission to access this resource.')).toBeInTheDocument();
    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByText('Back to Home')).toBeInTheDocument();
  });

  it('should handle go back button click', () => {
    render(<ForbiddenError />);

    const goBackButton = screen.getByText('Go Back');
    fireEvent.click(goBackButton);

    expect(mockHistoryGo).toHaveBeenCalledWith(-1);
  });

  it('should handle back to home button click', () => {
    render(<ForbiddenError />);

    const backToHomeButton = screen.getByText('Back to Home');
    fireEvent.click(backToHomeButton);

    expect(mockNavigate).toHaveBeenCalledWith({ to: '/' });
  });

  it('should apply correct button variants', () => {
    render(<ForbiddenError />);

    const goBackButton = screen.getByText('Go Back');
    const backToHomeButton = screen.getByText('Back to Home');

    expect(goBackButton).toHaveAttribute('data-variant', 'outline');
    expect(backToHomeButton).not.toHaveAttribute('data-variant');
  });

  it('should have correct layout structure', () => {
    const { container } = render(<ForbiddenError />);

    const mainContainer = container.querySelector('.h-svh');
    expect(mainContainer).toBeInTheDocument();

    const contentContainer = container.querySelector('.m-auto.flex.h-full.w-full.flex-col.items-center.justify-center.gap-2');
    expect(contentContainer).toBeInTheDocument();
  });

  it('should display 403 error code with correct styling', () => {
    render(<ForbiddenError />);

    const errorCode = screen.getByText('403');
    expect(errorCode).toBeInTheDocument();
    expect(errorCode.tagName).toBe('H1');
  });

  it('should render error description as paragraph', () => {
    render(<ForbiddenError />);

    const description = screen.getByText('You do not have permission to access this resource.');
    expect(description.tagName).toBe('P');
  });

  it('should have button container with correct gap', () => {
    const { container } = render(<ForbiddenError />);

    const buttonContainer = container.querySelector('.mt-6.flex.gap-4');
    expect(buttonContainer).toBeInTheDocument();
    expect(buttonContainer?.children).toHaveLength(2);
  });
});
