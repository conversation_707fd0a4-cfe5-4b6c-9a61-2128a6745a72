/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { useNavigate, useRouter } from '@tanstack/react-router';
import GeneralError from './general-error';

// Mock dependencies
vi.mock('@tanstack/react-router', () => ({
  useNavigate: vi.fn(),
  useRouter: vi.fn(),
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'general.title': 'Something went wrong',
        'general.description': 'An unexpected error occurred. Please try again later.',
        'go_back': 'Go Back',
        'back_to_home': 'Back to Home',
      };
      return translations[key] || key;
    },
  }),
}));

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, variant, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} {...props}>
      {children}
    </button>
  ),
}));

vi.mock('@mass/shared/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

const mockNavigate = vi.fn();
const mockHistoryGo = vi.fn();

describe('GeneralError', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useRouter as any).mockReturnValue({
      history: {
        go: mockHistoryGo,
      },
    });
  });

  it('should render general error page correctly', () => {
    render(<GeneralError />);

    expect(screen.getByText('500')).toBeInTheDocument();
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred. Please try again later.')).toBeInTheDocument();
    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByText('Back to Home')).toBeInTheDocument();
  });

  it('should render minimal version without error code and buttons', () => {
    render(<GeneralError minimal={true} />);

    expect(screen.queryByText('500')).not.toBeInTheDocument();
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred. Please try again later.')).toBeInTheDocument();
    expect(screen.queryByText('Go Back')).not.toBeInTheDocument();
    expect(screen.queryByText('Back to Home')).not.toBeInTheDocument();
  });

  it('should handle go back button click', () => {
    render(<GeneralError />);

    const goBackButton = screen.getByText('Go Back');
    fireEvent.click(goBackButton);

    expect(mockHistoryGo).toHaveBeenCalledWith(-1);
  });

  it('should handle back to home button click', () => {
    render(<GeneralError />);

    const backToHomeButton = screen.getByText('Back to Home');
    fireEvent.click(backToHomeButton);

    expect(mockNavigate).toHaveBeenCalledWith({ to: '/' });
  });

  it('should apply custom className', () => {
    const customClass = 'custom-error-class';
    const { container } = render(<GeneralError className={customClass} />);

    const mainContainer = container.firstChild as HTMLElement;
    expect(mainContainer).toHaveClass('h-svh', 'w-full', customClass);
  });

  it('should not render buttons in minimal mode', () => {
    render(<GeneralError minimal={true} />);

    const goBackButton = screen.queryByText('Go Back');
    const homeButton = screen.queryByText('Back to Home');
    
    expect(goBackButton).not.toBeInTheDocument();
    expect(homeButton).not.toBeInTheDocument();
  });

  it('should render buttons in non-minimal mode', () => {
    render(<GeneralError minimal={false} />);

    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByText('Back to Home')).toBeInTheDocument();
  });

  it('should apply correct button variants', () => {
    render(<GeneralError />);

    const goBackButton = screen.getByText('Go Back');
    const backToHomeButton = screen.getByText('Back to Home');

    expect(goBackButton).toHaveAttribute('data-variant', 'outline');
    expect(backToHomeButton).not.toHaveAttribute('data-variant');
  });

  it('should have correct layout structure for full mode', () => {
    const { container } = render(<GeneralError />);

    const mainContainer = container.querySelector('.h-svh.w-full');
    expect(mainContainer).toBeInTheDocument();

    const contentContainer = container.querySelector('.m-auto.flex.h-full.w-full.flex-col.items-center.justify-center.gap-2');
    expect(contentContainer).toBeInTheDocument();
  });

  it('should have correct layout structure for minimal mode', () => {
    const { container } = render(<GeneralError minimal={true} />);

    const mainContainer = container.querySelector('.h-svh.w-full');
    expect(mainContainer).toBeInTheDocument();

    // Should not have button container
    const buttonContainer = container.querySelector('.mt-6.flex.gap-4');
    expect(buttonContainer).not.toBeInTheDocument();
  });

  it('should display error code with correct styling when not minimal', () => {
    render(<GeneralError minimal={false} />);

    const errorCode = screen.getByText('500');
    expect(errorCode).toBeInTheDocument();
    expect(errorCode.tagName).toBe('H1');
  });

  it('should render error description as paragraph', () => {
    render(<GeneralError />);

    const description = screen.getByText('An unexpected error occurred. Please try again later.');
    expect(description.tagName).toBe('P');
  });

  it('should handle undefined className prop', () => {
    const { container } = render(<GeneralError />);

    const mainContainer = container.firstChild as HTMLElement;
    expect(mainContainer).toHaveClass('h-svh', 'w-full');
  });

  it('should render title as span element', () => {
    render(<GeneralError />);

    const title = screen.getByText('Something went wrong');
    expect(title.tagName).toBe('SPAN');
  });
});
