import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { useTranslation } from 'react-i18next'
import MaintenanceError from './maintenance-error'

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}))

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, variant, ...props }: any) => (
    <button data-variant={variant} {...props}>
      {children}
    </button>
  ),
}))

describe('MaintenanceError', () => {
  const mockT = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    vi.mocked(useTranslation).mockReturnValue({
      t: mockT,
    } as any)

    // Setup translation mock responses
    mockT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'maintenance.title': 'Under Maintenance',
        'maintenance.description': 'We are currently performing scheduled maintenance. Please try again later.',
        'learn_more': 'Learn More',
      }
      return translations[key] || key
    })
  })

  it('should render maintenance error page correctly', () => {
    render(<MaintenanceError />)

    expect(screen.getByText('503')).toBeInTheDocument()
    expect(screen.getByText('Under Maintenance')).toBeInTheDocument()
    expect(screen.getByText('We are currently performing scheduled maintenance. Please try again later.')).toBeInTheDocument()
    expect(screen.getByText('Learn More')).toBeInTheDocument()
  })

  it('should use correct translation namespace', () => {
    render(<MaintenanceError />)

    expect(useTranslation).toHaveBeenCalledWith('errors')
  })

  it('should display translated text correctly', () => {
    render(<MaintenanceError />)

    expect(mockT).toHaveBeenCalledWith('maintenance.title')
    expect(mockT).toHaveBeenCalledWith('maintenance.description')
    expect(mockT).toHaveBeenCalledWith('learn_more')
  })

  it('should render learn more button with outline variant', () => {
    render(<MaintenanceError />)

    const learnMoreButton = screen.getByText('Learn More')
    expect(learnMoreButton).toHaveAttribute('data-variant', 'outline')
  })

  it('should have proper container structure and styling', () => {
    const { container } = render(<MaintenanceError />)

    const mainContainer = container.querySelector('.h-svh')
    expect(mainContainer).toBeInTheDocument()

    const contentContainer = container.querySelector('.m-auto.flex.h-full.w-full.flex-col.items-center.justify-center.gap-2')
    expect(contentContainer).toBeInTheDocument()
  })

  it('should render 503 heading with correct styling', () => {
    render(<MaintenanceError />)

    const heading = screen.getByText('503')
    expect(heading.tagName).toBe('H1')
    expect(heading).toHaveClass('text-[7rem]', 'font-bold', 'leading-tight')
  })

  it('should render description with muted foreground styling', () => {
    const { container } = render(<MaintenanceError />)

    const description = container.querySelector('.text-muted-foreground')
    expect(description).toBeInTheDocument()
    expect(description).toHaveTextContent('We are currently performing scheduled maintenance. Please try again later.')
  })

  it('should handle missing translations gracefully', () => {
    mockT.mockImplementation((key: string) => key)
    
    render(<MaintenanceError />)

    expect(screen.getByText('maintenance.title')).toBeInTheDocument()
    expect(screen.getByText('maintenance.description')).toBeInTheDocument()
    expect(screen.getByText('learn_more')).toBeInTheDocument()
  })

  it('should render title with medium font weight', () => {
    render(<MaintenanceError />)

    const title = screen.getByText('Under Maintenance')
    expect(title.tagName).toBe('SPAN')
    expect(title).toHaveClass('font-medium')
  })

  it('should center align description text', () => {
    const { container } = render(<MaintenanceError />)

    const description = container.querySelector('p')
    expect(description).toHaveClass('text-center')
  })
})
