import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { useNavigate, useRouter } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import NotFoundError from './not-found-error'

// Mock dependencies
vi.mock('@tanstack/react-router', () => ({
  useNavigate: vi.fn(),
  useRouter: vi.fn(),
}))

vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}))

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, variant, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} {...props}>
      {children}
    </button>
  ),
}))

describe('NotFoundError', () => {
  const mockNavigate = vi.fn()
  const mockHistory = { go: vi.fn() }
  const mockT = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    vi.mocked(useNavigate).mockReturnValue(mockNavigate)
    vi.mocked(useRouter).mockReturnValue({ 
      history: mockHistory 
    } as any)
    vi.mocked(useTranslation).mockReturnValue({
      t: mockT,
    } as any)

    // Setup translation mock responses
    mockT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'not_found.title': 'Page Not Found',
        'not_found.description': 'The page you are looking for does not exist.',
        'go_back': 'Go Back',
        'back_to_home': 'Back to Home',
      }
      return translations[key] || key
    })
  })

  it('should render 404 error page correctly', () => {
    render(<NotFoundError />)

    expect(screen.getByText('404')).toBeInTheDocument()
    expect(screen.getByText('Page Not Found')).toBeInTheDocument()
    expect(screen.getByText('The page you are looking for does not exist.')).toBeInTheDocument()
    expect(screen.getByText('Go Back')).toBeInTheDocument()
    expect(screen.getByText('Back to Home')).toBeInTheDocument()
  })

  it('should use correct translation namespace', () => {
    render(<NotFoundError />)

    expect(useTranslation).toHaveBeenCalledWith('errors')
  })

  it('should display translated text correctly', () => {
    render(<NotFoundError />)

    expect(mockT).toHaveBeenCalledWith('not_found.title')
    expect(mockT).toHaveBeenCalledWith('not_found.description')
    expect(mockT).toHaveBeenCalledWith('go_back')
    expect(mockT).toHaveBeenCalledWith('back_to_home')
  })

  it('should call history.go(-1) when go back button is clicked', () => {
    render(<NotFoundError />)

    const goBackButton = screen.getByText('Go Back')
    fireEvent.click(goBackButton)

    expect(mockHistory.go).toHaveBeenCalledWith(-1)
  })

  it('should navigate to home when back to home button is clicked', () => {
    render(<NotFoundError />)

    const backToHomeButton = screen.getByText('Back to Home')
    fireEvent.click(backToHomeButton)

    expect(mockNavigate).toHaveBeenCalledWith({ to: '/' })
  })

  it('should render buttons with correct variants', () => {
    render(<NotFoundError />)

    const goBackButton = screen.getByText('Go Back')
    const backToHomeButton = screen.getByText('Back to Home')

    expect(goBackButton).toHaveAttribute('data-variant', 'outline')
    expect(backToHomeButton).not.toHaveAttribute('data-variant')
  })

  it('should have proper container structure and styling', () => {
    const { container } = render(<NotFoundError />)

    const mainContainer = container.querySelector('.h-svh')
    expect(mainContainer).toBeInTheDocument()

    const contentContainer = container.querySelector('.m-auto.flex.h-full.w-full.flex-col.items-center.justify-center.gap-2')
    expect(contentContainer).toBeInTheDocument()
  })

  it('should render 404 heading with correct styling', () => {
    render(<NotFoundError />)

    const heading = screen.getByText('404')
    expect(heading.tagName).toBe('H1')
    expect(heading).toHaveClass('text-[7rem]', 'font-bold', 'leading-tight')
  })

  it('should render description with muted foreground styling', () => {
    const { container } = render(<NotFoundError />)

    const description = container.querySelector('.text-muted-foreground')
    expect(description).toBeInTheDocument()
    expect(description).toHaveTextContent('The page you are looking for does not exist.')
  })

  it('should handle missing translations gracefully', () => {
    mockT.mockImplementation((key: string) => key)
    
    render(<NotFoundError />)

    expect(screen.getByText('not_found.title')).toBeInTheDocument()
    expect(screen.getByText('not_found.description')).toBeInTheDocument()
  })
})
