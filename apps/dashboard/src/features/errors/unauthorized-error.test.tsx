import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { useNavigate, useRouter } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import UnauthorisedError from './unauthorized-error'

// Mock dependencies
vi.mock('@tanstack/react-router', () => ({
  useNavigate: vi.fn(),
  useRouter: vi.fn(),
}))

vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}))

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, variant, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} {...props}>
      {children}
    </button>
  ),
}))

describe('UnauthorisedError', () => {
  const mockNavigate = vi.fn()
  const mockHistory = { go: vi.fn() }
  const mockT = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    vi.mocked(useNavigate).mockReturnValue(mockNavigate)
    vi.mocked(useRouter).mockReturnValue({ 
      history: mockHistory 
    } as any)
    vi.mocked(useTranslation).mockReturnValue({
      t: mockT,
    } as any)

    // Setup translation mock responses
    mockT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'unauthorized.title': 'Unauthorized Access',
        'unauthorized.description': 'You do not have permission to access this resource.',
        'go_back': 'Go Back',
        'back_to_home': 'Back to Home',
      }
      return translations[key] || key
    })
  })

  it('should render 401 unauthorized error page correctly', () => {
    render(<UnauthorisedError />)

    expect(screen.getByText('401')).toBeInTheDocument()
    expect(screen.getByText('Unauthorized Access')).toBeInTheDocument()
    expect(screen.getByText('You do not have permission to access this resource.')).toBeInTheDocument()
    expect(screen.getByText('Go Back')).toBeInTheDocument()
    expect(screen.getByText('Back to Home')).toBeInTheDocument()
  })

  it('should use correct translation namespace', () => {
    render(<UnauthorisedError />)

    expect(useTranslation).toHaveBeenCalledWith('errors')
  })

  it('should display translated text correctly', () => {
    render(<UnauthorisedError />)

    expect(mockT).toHaveBeenCalledWith('unauthorized.title')
    expect(mockT).toHaveBeenCalledWith('unauthorized.description')
    expect(mockT).toHaveBeenCalledWith('go_back')
    expect(mockT).toHaveBeenCalledWith('back_to_home')
  })

  it('should call history.go(-1) when go back button is clicked', () => {
    render(<UnauthorisedError />)

    const goBackButton = screen.getByText('Go Back')
    fireEvent.click(goBackButton)

    expect(mockHistory.go).toHaveBeenCalledWith(-1)
  })

  it('should navigate to home when back to home button is clicked', () => {
    render(<UnauthorisedError />)

    const backToHomeButton = screen.getByText('Back to Home')
    fireEvent.click(backToHomeButton)

    expect(mockNavigate).toHaveBeenCalledWith({ to: '/' })
  })

  it('should render buttons with correct variants', () => {
    render(<UnauthorisedError />)

    const goBackButton = screen.getByText('Go Back')
    const backToHomeButton = screen.getByText('Back to Home')

    expect(goBackButton).toHaveAttribute('data-variant', 'outline')
    expect(backToHomeButton).not.toHaveAttribute('data-variant')
  })

  it('should have proper container structure and styling', () => {
    const { container } = render(<UnauthorisedError />)

    const mainContainer = container.querySelector('.h-svh')
    expect(mainContainer).toBeInTheDocument()

    const contentContainer = container.querySelector('.m-auto.flex.h-full.w-full.flex-col.items-center.justify-center.gap-2')
    expect(contentContainer).toBeInTheDocument()
  })

  it('should render 401 heading with correct styling', () => {
    render(<UnauthorisedError />)

    const heading = screen.getByText('401')
    expect(heading.tagName).toBe('H1')
    expect(heading).toHaveClass('text-[7rem]', 'font-bold', 'leading-tight')
  })

  it('should render description with muted foreground styling', () => {
    const { container } = render(<UnauthorisedError />)

    const description = container.querySelector('.text-muted-foreground')
    expect(description).toBeInTheDocument()
    expect(description).toHaveTextContent('You do not have permission to access this resource.')
  })

  it('should render title with medium font weight', () => {
    render(<UnauthorisedError />)

    const title = screen.getByText('Unauthorized Access')
    expect(title.tagName).toBe('SPAN')
    expect(title).toHaveClass('font-medium')
  })

  it('should center align description text', () => {
    const { container } = render(<UnauthorisedError />)

    const description = container.querySelector('p')
    expect(description).toHaveClass('text-center')
  })

  it('should handle missing translations gracefully', () => {
    mockT.mockImplementation((key: string) => key)
    
    render(<UnauthorisedError />)

    expect(screen.getByText('unauthorized.title')).toBeInTheDocument()
    expect(screen.getByText('unauthorized.description')).toBeInTheDocument()
  })
})
