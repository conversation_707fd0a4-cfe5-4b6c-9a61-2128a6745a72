import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { useMatches, useNavigate } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import HelpCenterLayout from './index'

// Mock dependencies
vi.mock('@/constants/sidebar-data', () => ({
  sidebarData: [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Help Center', href: '/help-center' },
  ],
}))

vi.mock('@tanstack/react-router', () => ({
  useMatches: vi.fn(),
  useNavigate: vi.fn(),
}))

vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}))

vi.mock('@mass/shared/components/organisms/layout/header', () => ({
  Header: ({ sidebarData, title, description }: any) => (
    <header data-testid="header">
      <div data-testid="title">{title}</div>
      <div data-testid="description">{description}</div>
    </header>
  ),
}))

vi.mock('@mass/shared/components/organisms/layout/main', () => ({
  Main: ({ children, className }: any) => (
    <main data-testid="main" className={className}>
      {children}
    </main>
  ),
}))

describe('HelpCenterLayout', () => {
  const mockNavigate = vi.fn()
  const mockUseMatches = vi.mocked(useMatches)
  const mockUseNavigate = vi.mocked(useNavigate)
  const mockUseTranslation = vi.mocked(useTranslation)

  beforeEach(() => {
    vi.clearAllMocks()

    mockUseNavigate.mockReturnValue(mockNavigate)
    mockUseTranslation.mockReturnValue({
      t: vi.fn((key: string) => {
        const translations: Record<string, string> = {
          'help_center.use-cases': 'Use Cases',
          'help_center.faq': 'FAQ',
          'help_center.about': 'About',
        }
        return translations[key] || key
      }),
    } as any)
  })

  it('should render layout with children', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/use-cases' },
    ] as any)

    render(
      <HelpCenterLayout>
        <div data-testid="child-content">Test Content</div>
      </HelpCenterLayout>
    )

    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('main')).toBeInTheDocument()
    expect(screen.getByTestId('child-content')).toBeInTheDocument()
  })

  it('should extract active tab from URL path - use-cases', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/use-cases' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(screen.getByTestId('title')).toHaveTextContent('Use Cases')
  })

  it('should extract active tab from URL path - faq', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/faq' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(screen.getByTestId('title')).toHaveTextContent('FAQ')
  })

  it('should extract active tab from URL path - about', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/about' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(screen.getByTestId('title')).toHaveTextContent('About')
  })

  it('should default to use-cases when no path segment', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center' },
    ] as any)

    render(<HelpCenterLayout />)

    // When path is '/help-center', the last segment is 'help-center' which should be transformed or defaulted
    expect(screen.getByTestId('title')).toHaveTextContent('help_center.help-center')
  })

  it('should navigate to use-cases for invalid tab', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/invalid-tab' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(mockNavigate).toHaveBeenCalledWith({
      to: '/help-center/$page',
      params: { page: 'use-cases' },
    })
  })

  it('should not navigate for valid tabs', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/faq' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(mockNavigate).not.toHaveBeenCalled()
  })

  it('should handle empty matches array', () => {
    mockUseMatches.mockReturnValue([])

    render(<HelpCenterLayout />)

    expect(screen.getByTestId('title')).toHaveTextContent('Use Cases')
  })

  it('should render header with correct props', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/about' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(screen.getByTestId('title')).toHaveTextContent('About')
    expect(screen.getByTestId('description')).toHaveTextContent(
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Maecenas convallis posuere volutpat'
    )
  })

  it('should render main with correct className', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/use-cases' },
    ] as any)

    render(<HelpCenterLayout />)

    const main = screen.getByTestId('main')
    expect(main).toHaveClass('justify-start')
  })

  it('should render content container with correct styling', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/use-cases' },
    ] as any)

    const { container } = render(
      <HelpCenterLayout>
        <div>Test</div>
      </HelpCenterLayout>
    )

    const contentContainer = container.querySelector('.flex.flex-col.items-start.mr-auto.gap-5.w-full')
    expect(contentContainer).toBeInTheDocument()
  })

  it('should handle complex URL paths', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/dashboard' },
      { pathname: '/help-center/faq' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(screen.getByTestId('title')).toHaveTextContent('FAQ')
  })

  it('should handle URL with trailing slash', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/about' },
    ] as any)

    render(<HelpCenterLayout />)

    // When path is '/help-center/about', the last segment is 'about'
    expect(screen.getByTestId('title')).toHaveTextContent('About')
  })

  it('should call translation with correct key', () => {
    const mockT = vi.fn(key => key)
    mockUseTranslation.mockReturnValue({ t: mockT } as any)
    
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/faq' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(mockT).toHaveBeenCalledWith('help_center.faq')
  })

  it('should handle navigation dependency in useEffect', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/invalid' },
    ] as any)

    const { rerender } = render(<HelpCenterLayout />)

    // Re-render with same props to test useEffect dependency
    rerender(<HelpCenterLayout />)

    expect(mockNavigate).toHaveBeenCalledWith({
      to: '/help-center/$page',
      params: { page: 'use-cases' },
    })
  })

  it('should render without children', () => {
    mockUseMatches.mockReturnValue([
      { pathname: '/help-center/use-cases' },
    ] as any)

    render(<HelpCenterLayout />)

    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('main')).toBeInTheDocument()
  })
})
