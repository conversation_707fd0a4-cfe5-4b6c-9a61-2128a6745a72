import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import About from './about'

// Mock the MDX import
vi.mock('../content/example1.mdx', () => ({
  default: () => <div data-testid="example-mdx">Example MDX Content</div>,
}))

describe('About', () => {
  it('should render the about page correctly', () => {
    render(<About />)

    expect(screen.getByTestId('example-mdx')).toBeInTheDocument()
  })

  it('should have correct article styling', () => {
    const { container } = render(<About />)

    const article = container.querySelector('article')
    expect(article).toBeInTheDocument()
    expect(article).toHaveClass('prose', 'prose-xs', 'text-left', 'w-full')
  })

  it('should render MDX content inside article', () => {
    const { container } = render(<About />)

    const article = container.querySelector('article')
    expect(article).toContainElement(screen.getByTestId('example-mdx'))
  })
})
