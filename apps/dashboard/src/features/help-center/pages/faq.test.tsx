import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { useTranslation } from 'react-i18next'
import Faq from './faq'

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}))

const mockUseGlobalSettings = vi.fn()
vi.mock('@mass/shared/hooks/use-global-settings', () => ({
  useGlobalSettings: () => mockUseGlobalSettings(),
  globalSettingsKeys: {
    documents: {
      faq: vi.fn(() => 'faq-key'),
    },
  },
}))

vi.mock('@/services/api', () => ({
  default: vi.fn(),
}))

vi.mock('@mass/shared/components/ui/accordion', () => ({
  Accordion: ({ children, type, collapsible, ...props }: any) => (
    <div 
      data-testid="accordion" 
      type={type}
      collapsible={collapsible ? 'true' : undefined}
      {...props}
    >
      {children}
    </div>
  ),
  AccordionContent: ({ children }: any) => (
    <div data-testid="accordion-content">{children}</div>
  ),
  AccordionItem: ({ children, value }: any) => (
    <div data-testid="accordion-item" data-value={value}>{children}</div>
  ),
  AccordionTrigger: ({ children, className }: any) => (
    <button data-testid="accordion-trigger" className={className}>{children}</button>
  ),
}))

vi.mock('@mass/shared/components/ui/skeleton', () => ({
  Skeleton: ({ className }: any) => (
    <div data-testid="skeleton" className={className}></div>
  ),
}))

describe('Faq', () => {
  const mockUseTranslation = vi.mocked(useTranslation)

  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseTranslation.mockReturnValue({
      i18n: { language: 'tr' },
    } as any)

    mockUseGlobalSettings.mockReturnValue({
      isLoading: false,
      data: {
        faqs: [
          {
            id: 1,
            question: 'Test Question',
            answer: 'Test Answer',
            category: 'general'
          }
        ]
      }
    })

    // Mock console.log to avoid output in tests
    vi.spyOn(console, 'log').mockImplementation(() => {})
  })

  it('should render loading state with skeletons', () => {
    mockUseGlobalSettings.mockReturnValue({
      data: null,
      isLoading: true,
    })

    render(<Faq />)

    expect(screen.getByRole('article')).toBeInTheDocument()
    expect(screen.getAllByTestId('skeleton')).toHaveLength(9) // 3 items * 3 skeletons each
  })

  it('should render FAQ items when data is loaded', () => {
    const mockFaqData = {
      value: [
        {
          question: {
            TR: 'Soru 1 TR',
            EN: 'Question 1 EN',
          },
          answer: {
            TR: 'Cevap 1 TR',
            EN: 'Answer 1 EN',
          },
        },
        {
          question: {
            TR: 'Soru 2 TR',
            EN: 'Question 2 EN',
          },
          answer: {
            TR: 'Cevap 2 TR',
            EN: 'Answer 2 EN',
          },
        },
      ],
    }

    mockUseGlobalSettings.mockReturnValue({
      data: mockFaqData,
      isLoading: false,
    })

    render(<Faq />)

    expect(screen.getByTestId('accordion')).toBeInTheDocument()
    expect(screen.getAllByTestId('accordion-item')).toHaveLength(2)
    expect(screen.getByText('Soru 2 TR')).toBeInTheDocument() // Items are reversed
    expect(screen.getByText('Soru 1 TR')).toBeInTheDocument()
  })

  it('should display content in Turkish when language is TR', () => {
    const mockFaqData = {
      value: [
        {
          question: {
            TR: 'Türkçe Soru',
            EN: 'English Question',
          },
          answer: {
            TR: 'Türkçe Cevap',
            EN: 'English Answer',
          },
        },
      ],
    }

    mockUseTranslation.mockReturnValue({
      i18n: { language: 'tr' },
    } as any)

    mockUseGlobalSettings.mockReturnValue({
      data: mockFaqData,
      isLoading: false,
    })

    render(<Faq />)

    expect(screen.getByText('Türkçe Soru')).toBeInTheDocument()
    expect(screen.getByText('Türkçe Cevap')).toBeInTheDocument()
    expect(screen.queryByText('English Question')).not.toBeInTheDocument()
    expect(screen.queryByText('English Answer')).not.toBeInTheDocument()
  })

  it('should display content in English when language is EN', () => {
    const mockFaqData = {
      value: [
        {
          question: {
            TR: 'Türkçe Soru',
            EN: 'English Question',
          },
          answer: {
            TR: 'Türkçe Cevap',
            EN: 'English Answer',
          },
        },
      ],
    }

    mockUseTranslation.mockReturnValue({
      i18n: { language: 'en' },
    } as any)

    mockUseGlobalSettings.mockReturnValue({
      data: mockFaqData,
      isLoading: false,
    })

    render(<Faq />)

    expect(screen.getByText('English Question')).toBeInTheDocument()
    expect(screen.getByText('English Answer')).toBeInTheDocument()
    expect(screen.queryByText('Türkçe Soru')).not.toBeInTheDocument()
    expect(screen.queryByText('Türkçe Cevap')).not.toBeInTheDocument()
  })

  it('should render empty accordion when no FAQ data', () => {
    mockUseGlobalSettings.mockReturnValue({
      data: { value: [] },
      isLoading: false,
    })

    render(<Faq />)

    expect(screen.getByTestId('accordion')).toBeInTheDocument()
    expect(screen.queryAllByTestId('accordion-item')).toHaveLength(0)
  })

  it('should render empty accordion when data is null', () => {
    mockUseGlobalSettings.mockReturnValue({
      data: null,
      isLoading: false,
    })

    render(<Faq />)

    expect(screen.getByTestId('accordion')).toBeInTheDocument()
    expect(screen.queryAllByTestId('accordion-item')).toHaveLength(0)
  })

  it('should have correct article styling', () => {
    mockUseGlobalSettings.mockReturnValue({
      data: null,
      isLoading: false,
    })

    render(<Faq />)

    const article = screen.getByRole('article')
    expect(article).toHaveClass('prose', 'prose-xs', 'text-left', 'w-full', '-mt-6')
  })

  it('should have correct loading article styling', () => {
    mockUseGlobalSettings.mockReturnValue({
      data: null,
      isLoading: true,
    })

    render(<Faq />)

    const article = screen.getByRole('article')
    expect(article).toHaveClass('prose', 'prose-xs', 'text-left', 'w-full', 'space-y-4')
  })

  it('should reverse FAQ items order', () => {
    const mockFaqData = {
      value: [
        {
          question: { TR: 'First Question', EN: 'First Question' },
          answer: { TR: 'First Answer', EN: 'First Answer' },
        },
        {
          question: { TR: 'Second Question', EN: 'Second Question' },
          answer: { TR: 'Second Answer', EN: 'Second Answer' },
        },
        {
          question: { TR: 'Third Question', EN: 'Third Question' },
          answer: { TR: 'Third Answer', EN: 'Third Answer' },
        },
      ],
    }

    mockUseGlobalSettings.mockReturnValue({
      data: mockFaqData,
      isLoading: false,
    })

    render(<Faq />)

    const accordionItems = screen.getAllByTestId('accordion-item')
    expect(accordionItems[0]).toHaveAttribute('data-value', 'item-0') // First item should be the last from original array
  })

  it('should configure accordion correctly', () => {
    mockUseGlobalSettings.mockReturnValue({
      data: { value: [] },
      isLoading: false,
    })

    render(<Faq />)

    const accordion = screen.getByTestId('accordion')
    expect(accordion).toHaveAttribute('type', 'single') 
    expect(accordion).toHaveAttribute('collapsible', 'true')
  })

  it('should apply correct trigger styling', () => {
    const mockFaqData = {
      value: [
        {
          question: { TR: 'Test Question', EN: 'Test Question' },
          answer: { TR: 'Test Answer', EN: 'Test Answer' },
        },
      ],
    }

    mockUseGlobalSettings.mockReturnValue({
      data: mockFaqData,
      isLoading: false,
    })

    render(<Faq />)

    const trigger = screen.getByTestId('accordion-trigger')
    expect(trigger).toHaveClass('text-lg')
  })

  it('should handle case insensitive language matching', () => {
    const mockFaqData = {
      value: [
        {
          question: {
            TR: 'Türkçe Soru',
            EN: 'English Question',
          },
          answer: {
            TR: 'Türkçe Cevap',
            EN: 'English Answer',
          },
        },
      ],
    }

    mockUseTranslation.mockReturnValue({
      i18n: { language: 'tr' }, // lowercase
    } as any)

    mockUseGlobalSettings.mockReturnValue({
      data: mockFaqData,
      isLoading: false,
    })

    render(<Faq />)

    // Should convert to uppercase and match TR
    expect(screen.getByText('Türkçe Soru')).toBeInTheDocument()
    expect(screen.getByText('Türkçe Cevap')).toBeInTheDocument()
  })
})
