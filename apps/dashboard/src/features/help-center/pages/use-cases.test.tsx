import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import UseCases from './use-cases'

// Mock the MDX import
vi.mock('../content/example.mdx', () => ({
  default: () => <div data-testid="example-mdx">Use Cases MDX Content</div>,
}))

describe('UseCases', () => {
  it('should render the use cases page correctly', () => {
    render(<UseCases />)

    expect(screen.getByTestId('example-mdx')).toBeInTheDocument()
  })

  it('should have correct article styling', () => {
    const { container } = render(<UseCases />)

    const article = container.querySelector('article')
    expect(article).toBeInTheDocument()
    expect(article).toHaveClass('prose', 'prose-xs', 'text-left', 'w-full')
  })

  it('should render MDX content inside article', () => {
    const { container } = render(<UseCases />)

    const article = container.querySelector('article')
    expect(article).toContainElement(screen.getByTestId('example-mdx'))
  })
})
