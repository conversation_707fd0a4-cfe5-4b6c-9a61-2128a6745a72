/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { columns } from './columns'
import type { AppNotification } from './columns'

// Mock the dependencies
vi.mock('@/features/notifications/data/queries', () => ({
  useMarkNotificationAsRead: vi.fn(() => ({
    mutate: vi.fn(),
  })),
}))

vi.mock('@mass/shared/components/atoms/Iconify', () => ({
  default: ({ name }: { name: string }) => <span data-testid="iconify" data-name={name} />,
}))

vi.mock('@mass/shared/components/organisms/table/action', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="table-action">{children}</div>,
}))

vi.mock('@mass/shared/components/organisms/table/SortHeader', () => ({
  SortHeader: ({ title }: { title: string }) => <div data-testid="sort-header">{title}</div>,
}))

vi.mock('@mass/shared/components/ui/dropdown-menu', () => ({
  DropdownMenuItem: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
    <div data-testid="dropdown-menu-item" onClick={onClick}>
      {children}
    </div>
  ),
}))

vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(() => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        column_notification_info: 'Notification Info',
        column_subscription_info: 'Subscription Info', 
        column_notification_date: 'Date',
        column_send_date: 'Send Date',
        column_notification_type: 'Type',
        subscription: 'Subscription Info',
        mark_as_read: 'Mark as read',
        mark_as_unread: 'Mark as unread',
        archive: 'Archive',
        unarchive: 'Unarchive',
      }
      return translations[key] || key
    },
    i18n: { language: 'en' },
  })),
}))

vi.mock('date-fns', () => ({
  format: (date: Date | string, formatStr: string) => {
    if (typeof date === 'string') {
      date = new Date(date)
    }
    return '1/1/2024'
  },
}))

vi.mock('date-fns/locale', async (importOriginal) => {
  const actual = await importOriginal() as any
  return {
    ...actual,
    tr: { code: 'tr' },
    enUS: { code: 'en' },
    en: { code: 'en' }, // Add the missing 'en' export
  }
})

const mockNotification: AppNotification = {
  id: '1',
  createdAt: '2024-01-01T10:00:00Z',
  textContent: {
    TR: 'Bu bir test bildirimi içeriğidir',
    EN: 'This is a test notification content',
  },
  title: {
    TR: 'Test Bildirimi',
    EN: 'Test Notification',
  },
  read: false,
  regionId: 'region-1',
  subscriptionId: 'sub-1',
  subscription: {
    id: 'sub-1',
    name: 'Test Subscription',
    region: {
      id: 'region-1',
      name: 'Test Region',
    },
  } as any,
  type: 'ANNOUNCEMENT',
  subtype: 'notification',
  status: 'UNREAD',
}

// Helper function to render column headers
const renderColumnHeader = (columnIndex: number) => {
  const column = columns[columnIndex]
  if (typeof column.header === 'function') {
    const HeaderComponent = column.header as any
    const mockTable = {
      options: {
        meta: {
          t: (key: string) => key,
        },
      },
    }
    return render(<HeaderComponent table={mockTable} />)
  }
  return null
}

// Helper function to render column cells
const renderColumnCell = (columnIndex: number, notification = mockNotification) => {
  const column = columns[columnIndex]
  if (typeof column.cell === 'function') {
    const CellComponent = column.cell as any
    const mockRow = {
      original: notification,
      getValue: (key: string) => (notification as any)[key],
    }
    const mockTable = {
      options: {
        meta: {
          t: (key: string) => key,
          regions: [{ id: 'region-1', name: 'Test Region' }],
        },
      },
    }
    return render(<CellComponent row={mockRow} table={mockTable} />)
  }
  return null
}

describe('Notifications Columns', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should have correct number of columns', () => {
    expect(columns).toHaveLength(5)
  })

  describe('Notification Info Column', () => {
    it('should render header correctly', () => {
      renderColumnHeader(0)
      expect(screen.getByText('Notification Info')).toBeInTheDocument()
    })

    it('should render notification info cell with title', () => {
      renderColumnCell(0)
      
      expect(screen.getByText('Test Notification')).toBeInTheDocument()
    })

    it('should show unread indicator for unread notifications', () => {
      renderColumnCell(0)
      
      // Check for the unread indicator (pulsing dot)
      const container = screen.getByText('Test Notification').closest('div')
      const unreadDot = container?.querySelector('.animate-pulse')
      expect(unreadDot).toBeInTheDocument()
    })
  })

  describe('Subscription Info Column', () => {
    it('should render header correctly', () => {
      renderColumnHeader(1)
      expect(screen.getByText('Subscription Info')).toBeInTheDocument()
    })

    it('should render subscription name', () => {
      renderColumnCell(1)
      
      expect(screen.getByText('Test Subscription')).toBeInTheDocument()
    })
  })

  describe('Type Column', () => {
    it('should render header correctly', () => {
      renderColumnHeader(2)
      expect(screen.getByText('column_notification_type')).toBeInTheDocument()
    })
  })

  describe('Date Column', () => {
    it('should render header correctly', () => {
      renderColumnHeader(3)
      expect(screen.getByText('column_send_date')).toBeInTheDocument()
    })

    it('should render formatted date', () => {
      renderColumnCell(3)
      
      expect(screen.getByText('1/1/2024')).toBeInTheDocument()
    })
  })

  describe('Actions Column', () => {
    it('should render actions for notifications', () => {
      renderColumnCell(4)
      
      expect(screen.getByTestId('table-action')).toBeInTheDocument()
    })
  })
})