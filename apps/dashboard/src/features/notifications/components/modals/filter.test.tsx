// @vitest-environment jsdom
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, cleanup, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import Filter from './filter';

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
    },
  }),
}));

vi.mock('@/services/api/subscriptions', () => ({
  subscriptionService: {
    getAll: vi.fn().mockResolvedValue({
      content: [
        { id: '123', name: 'Test Subscription 1' },
        { id: '456', name: 'Test Subscription 2' },
      ],
      totalPages: 1,
    }),
    getById: vi.fn().mockResolvedValue({ name: 'Test Subscription' }),
  },
}));

vi.mock('@mass/shared/components/atoms/featured-icon', () => ({
  default: ({ name, className }: { name: string, className: string }) => 
    <div data-testid={`featured-icon-${name}`} className={className}>Icon: {name}</div>
}));

vi.mock('@mass/shared/components/ui/searchable-paginated-select', () => ({
  default: ({ value, setValue, placeholder }: any) => (
    <div data-testid="searchable-select">
      <button data-testid="subscription-selector" onClick={() => setValue('123')}>
        {placeholder}
      </button>
      <span>Current: {value || 'None'}</span>
    </div>
  )
}));

vi.mock('../use-notification-types', () => ({
  useNotificationTypes: () => ({
    'type1': { label: { 'EN': 'Type 1', 'TR': 'Tip 1' } },
    'type2': { label: { 'EN': 'Type 2', 'TR': 'Tip 2' } }
  }),
}));

describe('Filter Component', () => {
  const onHideMock = vi.fn();
  const onConfirmMock = vi.fn();
  const clearFiltersMock = vi.fn();
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.clearAllMocks();
  });

  afterEach(() => {
    cleanup();
  });

  it('renders filter modal correctly', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <Filter 
          onHide={onHideMock}
          onConfirm={onConfirmMock}
          clearFilters={clearFiltersMock}
        />
      </QueryClientProvider>
    );

    expect(screen.getByTestId('featured-icon-untitled:filter-lines')).toBeInTheDocument();
    expect(screen.getByText('filter', { selector: '.self-stretch' })).toBeInTheDocument();
    expect(screen.getByTestId('subscription-selector')).toBeInTheDocument();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
    expect(screen.getByText('common.clear_filters')).toBeInTheDocument();
  });

  it('handles subscription selection', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <Filter 
          onHide={onHideMock}
          onConfirm={onConfirmMock}
          clearFilters={clearFiltersMock}
        />
      </QueryClientProvider>
    );

    const subscriptionSelector = screen.getByTestId('subscription-selector');
    fireEvent.click(subscriptionSelector);

    expect(screen.getByText('Current: 123')).toBeInTheDocument();
  });

  it('handles notification type selection', async () => {
    render(
      <QueryClientProvider client={queryClient}>
        <Filter 
          onHide={onHideMock}
          onConfirm={onConfirmMock}
          clearFilters={clearFiltersMock}
        />
      </QueryClientProvider>
    );

    const typeSelector = screen.getByRole('combobox');
    fireEvent.click(typeSelector);
    
    // Due to mocking, we need to ensure all elements render before continuing
    await waitFor(() => {
      const typeOptions = screen.getAllByRole('option');
      expect(typeOptions.length).toBeGreaterThan(0);
      fireEvent.click(typeOptions[0]);
    });
  });

  it('clears filters when clear button is clicked', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <Filter 
          onHide={onHideMock}
          onConfirm={onConfirmMock}
          clearFilters={clearFiltersMock}
        />
      </QueryClientProvider>
    );

    const clearButton = screen.getByText('common.clear_filters');
    fireEvent.click(clearButton);

    expect(clearFiltersMock).toHaveBeenCalled();
    expect(onHideMock).toHaveBeenCalled();
  });

  it('applies filters when apply button is clicked', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <Filter 
          onHide={onHideMock}
          onConfirm={onConfirmMock}
          clearFilters={clearFiltersMock}
        />
      </QueryClientProvider>
    );

    // First select a subscription
    const subscriptionSelector = screen.getByTestId('subscription-selector');
    fireEvent.click(subscriptionSelector);

    // Click apply (filter)
    const applyButton = screen.getByRole('button', { name: 'filter' });
    fireEvent.click(applyButton);

    expect(onConfirmMock).toHaveBeenCalled();
    expect(onHideMock).toHaveBeenCalled();
  });

  it('handles date range correctly', async () => {
    // Mock dates for testing
    const startDate = new Date('2023-01-01');
    const endDate = new Date('2023-12-31');

    render(
      <QueryClientProvider client={queryClient}>
        <Filter 
          onHide={onHideMock}
          onConfirm={onConfirmMock}
          clearFilters={clearFiltersMock}
          currentFilters={{ startDate, endDate }}
        />
      </QueryClientProvider>
    );

    // Apply filters with the date range
    const applyButton = screen.getByRole('button', { name: 'filter' });
    fireEvent.click(applyButton);

    await waitFor(() => {
      expect(onConfirmMock).toHaveBeenCalledWith(expect.objectContaining({
        startDate,
        endDate,
      }));
    });
  });

  it('swaps dates if start date is after end date', async () => {
    // Mock dates in wrong order
    const startDate = new Date('2023-12-31');
    const endDate = new Date('2023-01-01');

    render(
      <QueryClientProvider client={queryClient}>
        <Filter 
          onHide={onHideMock}
          onConfirm={onConfirmMock}
          clearFilters={clearFiltersMock}
          currentFilters={{ startDate, endDate }}
        />
      </QueryClientProvider>
    );

    // Apply filters with the date range
    const applyButton = screen.getByRole('button', { name: 'filter' });
    fireEvent.click(applyButton);

    await waitFor(() => {
      expect(onConfirmMock).toHaveBeenCalledWith(
        expect.objectContaining({
          // Dates should be swapped in the confirm call
          startDate: endDate,
          endDate: startDate,
        })
      );
    });
  });
});
