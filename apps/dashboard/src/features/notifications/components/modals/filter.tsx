import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import FeaturedIcon from "@mass/shared/components/atoms/featured-icon";
import { Button } from "@mass/shared/components/ui/button";
import { useTranslation } from "react-i18next";
import { Label } from "@mass/shared/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mass/shared/components/ui/select";
import SearchablePaginatedSelect from "@mass/shared/components/ui/searchable-paginated-select";
import { useState } from "react";
import { subscriptionService } from "@/services/api/subscriptions";
import { useNotificationTypes } from "../use-notification-types";
import { useQueryClient } from "@tanstack/react-query";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm?: () => void | Promise<void>;
}

export default function Filter({
  onHide,
  onConfirm,
  currentFilters,
  clearFilters,
  // todo: any
}: Props & {
  title?: string;
  description?: string;
  currentFilters?: any;
  clearFilters: () => void;
}) {
  const { t, i18n } = useTranslation("subscriptions");
  const currentLang = i18n.language.toUpperCase() as "EN" | "TR";
  const { t: tNotifs } = useTranslation("notifications");
  const notifTypes = useNotificationTypes();
  const queryClient = useQueryClient();

  const [subscription, setSubscription] = useState<string | null>(
    currentFilters?.subscription ?? null
  );
  const [type, setType] = useState(currentFilters?.type ?? "all");

  const [startDate, setStartDate] = useState<Date | undefined>(
    currentFilters?.startDate ?? undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    currentFilters?.endDate ?? undefined
  );

  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-lg text-gray-900 font-text-sm-regular">
        <FeaturedIcon name="untitled:filter-lines" className="-mt-16" />
        <div className="self-stretch relative leading-[28px] font-semibold">
          {t("filter")}
        </div>
      </div>
      {/* <div className="">
        <Label>{tNotifs("filter_start_date")}</Label>
        <DatePicker date={startDate} setDate={setStartDate} />
      </div>
      <div className="">
        <Label>{tNotifs("filter_end_date")}</Label>
        <DatePicker date={endDate} setDate={setEndDate} />
      </div> */}
      <div className="">
        <Label>{t("subscription")}</Label>
        <SearchablePaginatedSelect
          value={subscription}
          setValue={setSubscription}
          fetcher={async (page: number, search: string) => {
            const response = await subscriptionService.getAll({
              "filter:ct": {
                name: search,
              },
              pageSize: 10,
              pageNumber: page,
            });
            return {
              items: response.content.map((sub) => ({
                label: sub.name,
                value: sub.id,
              })),
              nextPage: page + 1 > response.totalPages ? undefined : page + 1,
            };
          }}
          labeler={async (value: string | null) => {
            if (!value) return null;
            const response = await queryClient.fetchQuery({
              queryKey: ["subscription", value],
              queryFn: () => subscriptionService.getById(value),
              staleTime: 5 * 60 * 1000,
              gcTime: 30 * 60 * 1000,
            });
            return response.name;
          }}
          placeholder={t("subscription")}
          searchPlaceholder={t("search_subscription")}
          emptyText={t("no_subscription_found")}
        />
      </div>
      <div className="">
        <Label>{tNotifs("notification_type")}</Label>
        <Select value={type} onValueChange={setType}>
          <SelectTrigger>
            <SelectValue placeholder={tNotifs("notification_type")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("all")}</SelectItem>
            {Object.entries(notifTypes ?? {}).map(([value, data]) => (
              <SelectItem key={value} value={value}>
                {data.label[currentLang]}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="w-full flex gap-2 items-center">
        <Button
          variant="outline"
          className="w-full"
          onClick={() => {
            clearFilters();
            onHide();
          }}
        >
          {t("common.clear_filters", {
            ns: "common",
          })}
        </Button>
        <Button
          className="w-full"
          onClick={() => {
            let _startDate = startDate;
            let _endDate = endDate;

            if (!_startDate && endDate) {
              _startDate = new Date(0);
            } else if (!_endDate && startDate) {
              _endDate = new Date();
            }

            if (_startDate && _endDate) {
              if (_startDate.valueOf() > _endDate.valueOf()) {
                const tmp = _startDate;
                _startDate = _endDate;
                _endDate = tmp;
              }
            }

            (onConfirm as any)?.({
              type,
              subscription,
              startDate: _startDate,
              endDate: _endDate,
            }); // todo: any
            onHide();
          }}
        >
          {t("filter")}
        </Button>
      </div>
    </div>
  );
}
