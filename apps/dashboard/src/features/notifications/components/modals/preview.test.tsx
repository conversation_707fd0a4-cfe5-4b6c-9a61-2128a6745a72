import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import Preview from './preview'

// Mock useTranslation
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        close: 'Close',
      }
      return translations[key] || key
    },
  }),
}))

// Mock Button component
vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, variant, className, ...props }: any) => (
    <button
      onClick={onClick}
      data-variant={variant}
      className={className}
      {...props}
    >
      {children}
    </button>
  ),
}))

describe('Preview Modal', () => {
  const mockOnHide = vi.fn()
  const defaultProps = {
    onHide: mockOnHide,
    activeTab: 'subscriptions' as const,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render with title and description', () => {
    render(
      <Preview
        {...defaultProps}
        title="Test Notification Title"
        description="Test notification description"
      />
    )

    expect(screen.getByText('Test Notification Title')).toBeInTheDocument()
    expect(screen.getByText('Test notification description')).toBeInTheDocument()
  })

  it('should render without title and description', () => {
    const { container } = render(<Preview {...defaultProps} />)
    
    // Check the structure is still there
    expect(container.querySelector('.pb-6.px-6')).toBeInTheDocument()
  })

  it('should render close button with correct text', () => {
    render(<Preview {...defaultProps} />)

    const closeButton = screen.getByRole('button', { name: 'Close' })
    expect(closeButton).toBeInTheDocument()
    expect(closeButton).toHaveAttribute('data-variant', 'outline')
    expect(closeButton).toHaveClass('w-full')
  })

  it('should call onHide when close button is clicked', () => {
    render(<Preview {...defaultProps} />)

    const closeButton = screen.getByRole('button', { name: 'Close' })
    fireEvent.click(closeButton)

    expect(mockOnHide).toHaveBeenCalledTimes(1)
  })

  it('should render with correct structure and classes', () => {
    const { container } = render(
      <Preview
        {...defaultProps}
        title="Test Title"
        description="Test Description"
      />
    )

    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('pb-6 px-6 flex flex-col gap-5')

    const contentContainer = container.querySelector('.w-full.relative.flex.flex-col')
    expect(contentContainer).toBeInTheDocument()
    expect(contentContainer).toHaveClass('items-start justify-start gap-2 text-left text-gray-900 font-text-sm-regular')

    const buttonContainer = container.querySelector('.w-full.flex.gap-2.items-center')
    expect(buttonContainer).toBeInTheDocument()
  })

  it('should handle empty strings for title and description', () => {
    render(
      <Preview
        {...defaultProps}
        title=""
        description=""
      />
    )

    // Should render empty title and description without errors
    const titleElement = screen.getByText((content, element) => {
      return element?.className.includes('font-semibold') || false
    })
    const descriptionElement = screen.getByText((content, element) => {
      return element?.className.includes('text-gray-600') || false
    })

    expect(titleElement).toBeInTheDocument()
    expect(descriptionElement).toBeInTheDocument()
  })

  it('should handle long title and description', () => {
    const longTitle = 'This is a very long notification title that might wrap to multiple lines in the modal'
    const longDescription = 'This is a very long notification description that contains a lot of text and should be displayed properly in the modal without breaking the layout'

    render(
      <Preview
        {...defaultProps}
        title={longTitle}
        description={longDescription}
      />
    )

    expect(screen.getByText(longTitle)).toBeInTheDocument()
    expect(screen.getByText(longDescription)).toBeInTheDocument()
  })

  it('should work with different activeTab values', () => {
    // Test with facilities tab
    render(
      <Preview
        {...defaultProps}
        activeTab="facilities"
        title="Facilities Title"
        description="Facilities Description"
      />
    )

    expect(screen.getByText('Facilities Title')).toBeInTheDocument()
    expect(screen.getByText('Facilities Description')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Close' })).toBeInTheDocument()
  })
})
