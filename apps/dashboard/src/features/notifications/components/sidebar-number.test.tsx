// @vitest-environment jsdom
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SidebarNumber } from './sidebar-number'
import * as React from 'react'

// Unmock the actual component to prevent using the mock in __mocks__
vi.unmock('./sidebar-number')

// Create mock functions
const mockUseSidebar = vi.fn()
const mockUseNotificationCount = vi.fn()

// Mock modules before importing component
vi.mock('@mass/shared/components/ui/sidebar', () => ({
  useSidebar: () => mockUseSidebar(),
}))

vi.mock('../data/queries', () => ({
  useNotificationCount: () => mockUseNotificationCount(),
}))

// Create actual React component mocks with proper JSX
vi.mock('@mass/shared/components/ui/badge', () => ({
  Badge: ({ children, className, variant, ...props }: { 
    children?: React.ReactNode; 
    className?: string; 
    variant?: string;
    [key: string]: any;
  }) => (
    <div data-testid="badge" className={className} data-variant={variant} {...props}>
      {children}
    </div>
  ),
}))

describe('SidebarNumber', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return null when count is 0', () => {
    mockUseNotificationCount.mockReturnValue({
      data: 0,
    })
    mockUseSidebar.mockReturnValue({
      state: 'expanded',
    })

    const { container } = render(<SidebarNumber />)
    expect(container.firstChild).toBeNull()
  })

  it('should return null when count is undefined', () => {
    mockUseNotificationCount.mockReturnValue({
      data: undefined,
    })
    mockUseSidebar.mockReturnValue({
      state: 'expanded',
    })

    const { container } = render(<SidebarNumber />)
    expect(container.firstChild).toBeNull()
  })

  it('should render badge when sidebar is collapsed and count > 0', () => {
    // Set mocks with correct return values
    mockUseNotificationCount.mockReturnValue({
      data: 5,
    })
    mockUseSidebar.mockReturnValue({
      state: 'collapsed',
    })

    // Render the component
    const { container, debug } = render(<SidebarNumber />)
    
    // Debug the rendered output
    console.log('HTML:', container.innerHTML)
    debug()
    
    // Check if the badge is rendered correctly
    const badge = screen.getByTestId('badge')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveTextContent('5')
    expect(badge).toHaveClass('bg-primary/10 text-primary border-none text-xs h-4 w-4 p-2.5 flex items-center justify-center')
    expect(badge).toHaveAttribute('data-variant', 'outline')
  })

  it('should render count number when sidebar is expanded and count > 0', () => {
    mockUseNotificationCount.mockReturnValue({
      data: 10,
    })
    mockUseSidebar.mockReturnValue({
      state: 'expanded',
    })

    const { debug } = render(<SidebarNumber />)
    debug()
    const countDisplay = screen.getByTestId('count-display')
    expect(countDisplay).toBeInTheDocument()
    expect(countDisplay).toHaveTextContent('10')
  })

  it('should render badge with correct container styling when collapsed', () => {
    mockUseNotificationCount.mockReturnValue({
      data: 3,
    })
    mockUseSidebar.mockReturnValue({
      state: 'collapsed',
    })

    const { debug } = render(<SidebarNumber />)
    debug()
    const badgeContainer = screen.getByTestId('badge-container')
    expect(badgeContainer).toHaveClass('absolute top-1/2 -translate-y-1/2 right-0 -mr-2')
  })

  it('should handle different count values', () => {
    // Test with larger count
    mockUseNotificationCount.mockReturnValue({
      data: 99,
    })
    mockUseSidebar.mockReturnValue({
      state: 'collapsed',
    })

    const { container, debug } = render(<SidebarNumber />)
    console.log('Different count HTML:', container.innerHTML)
    debug()
    
    const badge = screen.getByTestId('badge')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveTextContent('99')
  })

  it('should handle state transitions correctly', () => {
    mockUseNotificationCount.mockReturnValue({
      data: 7,
    })

    // Test expanded state
    mockUseSidebar.mockReturnValue({
      state: 'expanded',
    })

    const { rerender, container, debug } = render(<SidebarNumber />)
    console.log('Expanded state HTML:', container.innerHTML)
    debug()
    expect(screen.queryByTestId('badge')).not.toBeInTheDocument()
    expect(screen.getByTestId('count-display')).toBeInTheDocument()

    // Test collapsed state
    mockUseSidebar.mockReturnValue({
      state: 'collapsed',
    })

    rerender(<SidebarNumber />)
    debug()
    console.log('Collapsed state HTML:', container.innerHTML)
    expect(screen.getByTestId('badge')).toBeInTheDocument()
  })
})
