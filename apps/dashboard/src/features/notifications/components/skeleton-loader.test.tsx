// @vitest-environment jsdom
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { NotificationsTableSkeleton } from './skeleton-loader'

// Mock dependencies
vi.mock('@mass/shared/components/ui/skeleton', () => ({
  Skeleton: ({ className, ...props }: any) => (
    <div data-testid="skeleton" className={className} {...props} />
  ),
}))

describe('NotificationsTableSkeleton', () => {
  it('should render skeleton loader correctly', () => {
    render(<NotificationsTableSkeleton />)

    const skeletons = screen.getAllByTestId('skeleton')
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it('should render main container with correct classes', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    const mainContainer = container.querySelector('.w-full.space-y-3.h-full')
    expect(mainContainer).toBeInTheDocument()
  })

  it('should render header section with skeletons', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    const headerSection = container.querySelector('.flex.items-center.justify-between.py-4')
    expect(headerSection).toBeInTheDocument()

    // Should have title skeleton
    const titleSkeleton = container.querySelector('.h-8.w-64')
    expect(titleSkeleton).toBeInTheDocument()

    // Should have action buttons skeletons
    const buttonSkeletons = container.querySelectorAll('.h-10')
    expect(buttonSkeletons.length).toBeGreaterThanOrEqual(2)
  })

  it('should render table container with border', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    const tableContainer = container.querySelector('.rounded-xl.border.border-gray-100')
    expect(tableContainer).toBeInTheDocument()
  })

  it('should render table header row', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    const headerRow = container.querySelector('.border-b.border-gray-100.px-4.py-2.grid.grid-cols-6')
    expect(headerRow).toBeInTheDocument()
  })

  it('should render 5 table body rows', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    const bodyRows = container.querySelectorAll('.px-4.py-3.w-full.grid.grid-cols-6.items-center.gap-4.border-b.border-gray-100')
    expect(bodyRows).toHaveLength(5)
  })

  it('should render skeletons in each body row', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    // Each row should have multiple skeletons
    const bodyRows = container.querySelectorAll('.px-4.py-3.w-full.grid.grid-cols-6')
    
    bodyRows.forEach(row => {
      const skeletonsInRow = row.querySelectorAll('[data-testid="skeleton"]')
      expect(skeletonsInRow.length).toBeGreaterThan(3)
    })
  })

  it('should have correct grid layout', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    // Header should have grid-cols-6
    const headerRow = container.querySelector('.grid.grid-cols-6')
    expect(headerRow).toBeInTheDocument()

    // Body rows should also have grid-cols-6
    const bodyRows = container.querySelectorAll('.grid.grid-cols-6')
    expect(bodyRows.length).toBeGreaterThanOrEqual(6) // 1 header + 5 body rows
  })

  it('should render rounded badge skeleton in rows', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    const roundedSkeletons = container.querySelectorAll('.rounded-full')
    expect(roundedSkeletons.length).toBeGreaterThan(0)
  })

  it('should render action button skeletons', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    const actionSkeletons = container.querySelectorAll('.h-8.w-8.rounded-md.ml-auto')
    expect(actionSkeletons).toHaveLength(5) // One in each body row
  })

  it('should have last row without bottom border', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    const bodyRows = container.querySelectorAll('.px-4.py-3.w-full.grid.grid-cols-6.items-center.gap-4.border-b.border-gray-100.last\\:border-b-0')
    expect(bodyRows).toHaveLength(5)
  })

  it('should render nested column structure correctly', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    // Should have col-span-2 elements for first column
    const colSpan2Elements = container.querySelectorAll('.col-span-2')
    expect(colSpan2Elements.length).toBeGreaterThan(0)
  })

  it('should render different skeleton sizes', () => {
    const { container } = render(<NotificationsTableSkeleton />)

    // Should have various sizes
    expect(container.querySelector('.h-8.w-64')).toBeInTheDocument() // Title
    expect(container.querySelector('.h-10.w-40')).toBeInTheDocument() // Button
    expect(container.querySelector('.h-5.w-32')).toBeInTheDocument() // Text
    expect(container.querySelector('.h-4.w-48')).toBeInTheDocument() // Description
  })
})
