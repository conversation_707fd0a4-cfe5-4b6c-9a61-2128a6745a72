// @vitest-environment jsdom
import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom/vitest";
import { describe, it, expect, vi } from "vitest";
import TypeBadge from "./type-badge";

// replace jest.mock ➜ vi.mock
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe("TypeBadge", () => {
  it("renders the correct translation key for a normal type", () => {
    render(<TypeBadge type="info" subType="alert" />);

    // We expect the key string itself because of our mock.
    const el = screen.getByText("notifications.types.alert.type");
    expect(el).toBeInTheDocument();
    expect(el).toHaveClass("border"); // default class for non-warning
  });

  it("applies warning classes when type contains 'warning'", () => {
    render(<TypeBadge type="warning-high" subType="battery" />);

    const el = screen.getByText("notifications.types.battery.type");
    expect(el).toHaveClass("bg-red-50", "border-red-200", "text-red-800");
  });
});
