import { renderHook } from "@testing-library/react";
import "@testing-library/jest-dom/vitest";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { useNotificationTypes } from "./use-notification-types";

const mockUseNotificationCategories = vi.fn();

vi.mock("../data/queries", () => ({
  useNotificationCategories: () => mockUseNotificationCategories(),
}));

describe("useNotificationTypes", () => {
  beforeEach(() => {
    mockUseNotificationCategories.mockReset();
  });

  it("filters out deleted categories and subcategories", () => {
    mockUseNotificationCategories.mockReturnValue({
      data: {
        cat1: {
          deleted: "false",
          subcategories: {
            sub1: { deleted: "false" },
            sub2: { deleted: "true" },
          },
        },
        cat2: {
          deleted: "true",
          subcategories: {
            sub3: { deleted: "false" },
          },
        },
      },
    });

    const { result } = renderHook(() => useNotificationTypes());

    expect(result.current).toEqual({
      cat1: {
        deleted: "false",
        subcategories: { sub1: { deleted: "false" } },
      },
    });
  });

  it("returns an empty object when categories are undefined", () => {
    mockUseNotificationCategories.mockReturnValue({ data: undefined });
    const { result } = renderHook(() => useNotificationTypes());
    expect(result.current).toEqual({});
  });
});
