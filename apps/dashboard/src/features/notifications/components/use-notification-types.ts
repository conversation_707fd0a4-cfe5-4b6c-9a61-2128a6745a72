import { useNotificationCategories } from "../data/queries";

export const useNotificationTypes = () => {
  const { data: categories } = useNotificationCategories();

  const withoutDeletedCategories = Object.fromEntries(
    Object.entries(categories ?? {}).filter(
      ([_, data]) => data.deleted !== "true"
    )
  );

  const withoutDeletedSubcategories = Object.fromEntries(
    Object.entries(withoutDeletedCategories).map(([key, data]) => [
      key,
      {
        ...data,
        subcategories: Object.fromEntries(
          Object.entries(data.subcategories ?? {}).filter(
            ([_, subData]) => subData.deleted !== "true"
          )
        ),
      },
    ])
  );

  return withoutDeletedSubcategories;
};
