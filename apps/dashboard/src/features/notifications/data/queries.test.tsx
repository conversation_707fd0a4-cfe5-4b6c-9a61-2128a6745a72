import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor, act } from '@testing-library/react';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  useMarkNotificationAsRead,
  useNotificationCategories,
  useNotificationCount,
  useNotificationDetail,
  useNotifications,
} from './queries';

// Mocks need to be declared before imports due to hoisting
vi.mock('@/services/api/notifications', () => {
  const getAll = vi.fn();
  const getById = vi.fn();
  const count = vi.fn();
  const markAsRead = vi.fn();
  const getCategories = vi.fn();
  return {
    notificationService: {
      getAll,
      getById,
      count,
      markAsRead,
      getCategories,
    },
  };
});

import { notificationService } from '@/services/api/notifications';

// Setup the mock data
const mockGetAllResponse = {
  content: [
    { id: '1', title: 'Notification 1', read: false, archived: false },
    { id: '2', title: 'Notification 2', read: true, archived: false },
  ],
  number: 1,
  totalPages: 2,
  empty: false,
  first: true,
  last: false,
  size: 10,
  totalElements: 2,
  pageable: {
    offset: 0,
    pageNumber: 0,
    pageSize: 10,
  },
};

const mockNotificationDetail = {
  id: '1',
  title: 'Notification 1',
  message: 'This is notification 1',
  read: false,
  archived: false,
};

const mockCategories = {
  category1: {
    label: { TR: 'Kategori 1', EN: 'Category 1' },
    subcategories: {
      sub1: { label: { TR: 'Alt Kategori 1', EN: 'Subcategory 1' } },
    },
  },
};

// Create a wrapper for testing hooks that use React Query
function createTestWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 1000,
        refetchOnMount: true,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}

describe('Notification Query Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    const mockedNotificationService = vi.mocked(notificationService);

    mockedNotificationService.getAll.mockResolvedValue(mockGetAllResponse);
    mockedNotificationService.getById.mockResolvedValue(mockNotificationDetail);
    mockedNotificationService.count.mockResolvedValue(5);
    mockedNotificationService.markAsRead.mockResolvedValue({ success: true });
    mockedNotificationService.getCategories.mockResolvedValue(mockCategories);
  });

  it('useNotifications should fetch notifications correctly', async () => {
    const params = {
      id: '',
      orderBy: 'createdAt:desc',
      pageNumber: 1,
      createdAt: '',
      textContent: {},
      read: false,
      regionId: '',
      subscriptionId: null,
      subscription: null,
      type: '',
      subtype: '',
      pageSize: 10,
      archived: false,
    };

    const { result } = renderHook(() => useNotifications(params as any), {
      wrapper: createTestWrapper(),
    });

    await waitFor(() => {
      expect(notificationService.getAll).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data?.pages[0]).toBeDefined();
    expect(result.current.data?.pages[0].content).toHaveLength(2);
    expect(result.current.data?.pages[0].totalPages).toBe(2);
  });

  it('useNotificationDetail should fetch notification details', async () => {
    const { result } = renderHook(() => useNotificationDetail('1'), {
      wrapper: createTestWrapper(),
    });

    await waitFor(() => {
      expect(notificationService.getById).toHaveBeenCalledWith('1');
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(
      expect.objectContaining({
        id: '1',
        title: 'Notification 1',
      })
    );
  });

  it('useNotificationDetail should not fetch when id is empty', async () => {
    const { result } = renderHook(() => useNotificationDetail(''), {
      wrapper: createTestWrapper(),
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.status).toBe('pending'); // React Query v5: 'idle' => 'pending'
    expect(notificationService.getById).not.toHaveBeenCalled();
  });

  it('useNotificationCount should fetch count of notifications', async () => {
    const { result } = renderHook(() => useNotificationCount(false, false), {
      wrapper: createTestWrapper(),
    });

    await waitFor(() => {
      expect(notificationService.count).toHaveBeenCalledWith(false, false);
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toBe(5);
  });

  it('useMarkNotificationAsRead should mark notification as read', async () => {
    const { result } = renderHook(() => useMarkNotificationAsRead(), {
      wrapper: createTestWrapper(),
    });

    act(() => {
      result.current.mutate({ id: '1' });
    });

    await waitFor(() => {
      expect(notificationService.markAsRead).toHaveBeenCalledWith('1', undefined);
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual({ success: true });
  });

  it('useNotificationCategories should fetch notification categories', async () => {
    const { result } = renderHook(() => useNotificationCategories(), {
      wrapper: createTestWrapper(),
    });

    await waitFor(() => {
      expect(notificationService.getCategories).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(
      expect.objectContaining({
        category1: expect.objectContaining({
          label: expect.objectContaining({ EN: 'Category 1' }),
        }),
      })
    );
  });
});
