import { notificationService } from "@/services/api/notifications";
import {
  useMutation,
  useInfiniteQuery,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import type { Notification } from "@/services/api/notifications";
import type { PagedResponse } from "@/services/types";

export const notificationKeys = {
  all: ["notifications"] as const,
  lists: () => [...notificationKeys.all, "list"] as const,
  list: (params: Notification) =>
    [...notificationKeys.lists(), params] as const,
  details: () => [...notificationKeys.all, "detail"] as const,
  categories: () => [...notificationKeys.details(), "categories"] as const,
  detail: (id: string) => [...notificationKeys.details(), id] as const,
  count: (read: boolean, archived: boolean) =>
    [
      "notifications",
      "count",
      read ? "read:true" : "read:false",
      archived ? "archived:true" : "archived:false",
    ] as const,
};

function logQueryError(error: unknown) {
  if (error instanceof Error) {
    console.error("API Error:", error.message);
    return error.message;
  }
  console.error("Unexpected error:", error);
  return "An unexpected error occurred";
}

export function useNotifications(params: Notification) {
  const adjustedParams = {
    ...params,
    pageNumber: Math.max(params.pageNumber || 1, 1),
    orderBy: params.orderBy || "createdAt:desc",
  };

  return useInfiniteQuery<PagedResponse<Notification>, unknown>({
    initialPageParam: adjustedParams.pageNumber,
    queryKey: notificationKeys.list(adjustedParams),
    queryFn: async (ctx) => {
      const page =
        typeof ctx.pageParam === "number"
          ? ctx.pageParam
          : adjustedParams.pageNumber;
      return notificationService.getAll({
        ...adjustedParams,
        pageNumber: page,
      });
    },
    getNextPageParam: (lastPage: PagedResponse<Notification>) =>
      lastPage.number < lastPage.totalPages ? lastPage.number + 1 : undefined,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

export function useNotificationDetail(id: string) {
  return useQuery({
    queryKey: notificationKeys.detail(id),
    queryFn: () => notificationService.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

export function useNotificationCount(read: boolean, archived: boolean) {
  return useQuery({
    queryKey: notificationKeys.count(read, archived),
    queryFn: () => notificationService.count(read, archived),
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
}

// Mutations
export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, tag }: { id: string; tag?: string }) =>
      notificationService.markAsRead(id, tag),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: notificationKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: notificationKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: notificationKeys.count(false, false),
      });
      queryClient.invalidateQueries({
        queryKey: notificationKeys.count(false, true),
      });
      queryClient.invalidateQueries({
        queryKey: notificationKeys.count(true, true),
      });
      queryClient.invalidateQueries({
        queryKey: notificationKeys.count(true, false),
      });
    },
    onError: (error) => logQueryError(error),
  });
}

export function useNotificationCategories() {
  return useQuery<
    Record<
      string,
      {
        label: { TR: string; EN: string };
        subcategories: Record<
          string,
          { label: { TR: string; EN: string }; deleted?: string }
        >;
        deleted?: string;
      }
    >
  >({
    queryKey: notificationKeys.categories(),
    queryFn: () => notificationService.getCategories(),
  });
}
