import { describe, it, expect, vi, beforeEach, beforeAll } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import * as React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// ---------- global stubs ------------------------------------------------
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (k: string) => k,
    i18n: { language: 'en' },
  }),
}));

vi.mock('@mass/shared/components/organisms/layout/header', () => ({
  Header: (p: any) => <header>{p.title}</header>,
}));
vi.mock('@mass/shared/components/organisms/layout/main', () => ({
  Main: ({ children }: any) => <main>{children}</main>,
}));
vi.mock('@mass/shared/components/atoms/Iconify', () => ({
  default: () => <span data-testid="icon" />,
}));
vi.mock('@mass/shared/components/ui/button', () => ({
  Button: (p: any) => <button {...p} />,
}));
vi.mock('@mass/shared/components/ui/tabs', () => ({
  Tabs: (p: any) => <div>{p.children}</div>,
  TabsList: (p: any) => <div>{p.children}</div>,
  TabsTrigger: (p: any) => <button {...p} />,
}));
vi.mock('@mass/shared/components/atoms/empty', () => ({
  default: (p: any) => <div data-testid="empty">{p.children}</div>,
}));

// 1⃣ Enhanced AnimatedTransition mock
vi.mock('@mass/shared/components/molecules/table-transition', () => ({
  AnimatedTransition: ({ loading, skeleton, children }: any) =>
    loading ? <div data-testid="skeleton">{skeleton}</div> : <>{children}</>,
}));

// DataTable is heavy → expose props for inspection
// 🔁  Replace any previous DataTable mock with this single version
vi.mock('@mass/shared/components/organisms/table/index', () => ({
  DataTable: (p: any) => {
    // spy for assertions
    (global as any).__dataTableSpy?.(p);
    return (
      <table data-testid="table">
        {p.data.map((r: any) => (
          <tr key={r.id} onClick={() => p.onRowClick?.(r)}>
            <td>{r.title.EN}</td>
          </tr>
        ))}
      </table>
    );
  },
}));
(global as any).__dataTableSpy = vi.fn();
const dataTableSpy = (global as any).__dataTableSpy;

// Modal provider
const openModal = vi.fn();
vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: () => ({ open: openModal }),
}));

// Utils ‑ disabled hooks
vi.mock('@/utils/use-disabled', () => ({
  useDisabled: () => {},
  useIsDisabled: () => false,
}));

// Regions hook
vi.mock('../subscriptions/data/queries', () => ({
  useRegions: () => ({ data: [{ id: 'r1', name: 'Region-1' }] }),
}));

// ---------------- hooks factory mock (fix hoisting) --------------------
vi.mock('./data/queries', () => {
  const hooks = {
    useNotifications: vi.fn(),
    useMarkNotificationAsRead: vi.fn(),
  };
  return { __esModule: true, ...hooks };
});

// import mocked hooks (after vi.mock factories)
import {
  useNotifications,
  useMarkNotificationAsRead,
} from './data/queries';
// -----------------------------------------------------------------------

// component under test (must be imported after mocks)
import Notifications from './index';

// -----------------------------------------------------------------------

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={new QueryClient()}>{children}</QueryClientProvider>
);

const sampleNotif = {
  id: 'n1',
  title: { EN: 'Title-1' },
  textContent: { EN: 'Body' },
  status: 'UNREAD',
  read: false,
  subscription: { name: 'Sub-1' },
  regionId: 'r1',
  createdAt: '',
  type: 'INFO',
};

// ➤ 2) simple matchMedia polyfill (needed by some libs)
beforeAll(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),            // deprecated
      removeListener: vi.fn(),         // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
});

beforeEach(() => {
  vi.resetAllMocks();

  // 👇 cast to any to access mock helpers without TS errors
  (useMarkNotificationAsRead as any).mockReturnValue({ mutate: vi.fn() });
  (useNotifications as any).mockReturnValue({
    isLoading: true,
    data: undefined,
    error: null,
  });
});

describe('Notifications page', () => {
  it('shows skeleton while loading', () => {
    (useNotifications as any).mockReturnValue({
      isLoading: true,
      data: undefined,
      error: null,
    });

    render(<Notifications />, { wrapper });

    expect(screen.getByTestId('icon')).toBeInTheDocument();
    expect(screen.getByTestId('skeleton')).toBeInTheDocument();
    expect(screen.queryByTestId('table')).toBeNull();
  });

  it('handles error state gracefully', () => {
    const consoleErr = vi.spyOn(console, 'error').mockImplementation(() => {});
    (useNotifications as any).mockReturnValue({
      isLoading: false,
      data: undefined,
      error: new Error('boom'),
    });

    render(<Notifications />, { wrapper });

    expect(consoleErr).toHaveBeenCalled(); // component logged the error
    consoleErr.mockRestore();
  });

  it('renders notifications and opens preview on row click', async () => {
    (useNotifications as any).mockReturnValue({
      isLoading: false,
      error: null,
      data: { pages: [{ content: [sampleNotif], totalPages: 1 }] },
    });

    render(<Notifications />, { wrapper });

    // row rendered
    expect(screen.getByText('Title-1')).toBeInTheDocument();

    // click row → should call open modal
    fireEvent.click(screen.getByText('Title-1'));
    await waitFor(() => expect(openModal).toHaveBeenCalled());

    // DataTable received correct meta
    expect(dataTableSpy).toHaveBeenCalledWith(expect.objectContaining({ data: [sampleNotif] }));
  });
});
