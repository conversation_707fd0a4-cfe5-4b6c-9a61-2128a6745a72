import { sidebarData } from "@/constants/sidebar-data";
import { ApiQueryParams } from "@/services/types";
import Empty from "@mass/shared/components/atoms/empty";
import Iconify from "@mass/shared/components/atoms/Iconify";
import { AnimatedTransition } from "@mass/shared/components/molecules/table-transition";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { DataTable } from "@mass/shared/components/organisms/table/index";
import { Button } from "@mass/shared/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@mass/shared/components/ui/tabs";
import { SortingState } from "@tanstack/react-table";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useRegions } from "../subscriptions/data/queries";
import type { AppNotification as CustomNotificationItem } from "./components/columns";
import { columns } from "./components/columns";
import Filter from "./components/modals/filter";
import Preview from "./components/modals/preview";
import { NotificationsTableSkeleton } from "./components/skeleton-loader";
import { useMarkNotificationAsRead, useNotifications } from "./data/queries";
import { useDisabled, useIsDisabled } from "@/utils/use-disabled";

interface NotificationsProps {
  isArchived?: boolean;
}

export default function Notifications({}: NotificationsProps) {
  useDisabled(["disabled.notifications.view"], "/complaints-requests");

  const isDetailsDisabled = useIsDisabled([
    "disabled.notifications.view",
    "disabled.notifications.details",
  ]);

  const { t, i18n } = useTranslation("notifications");
  const currentLang = i18n.language.toUpperCase() as "EN" | "TR";

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "createdAt",
      desc: true,
    },
  ]);
  const [tab, setTab] = useState<"active" | "archives">("active");

  const [filterType, setFilterType] = useState("all");
  const [filterSubscription, setFilterSubscription] = useState<string | null>(
    null
  );
  const [filterStartDate, setFilterStartDate] = useState<Date | undefined>(
    undefined
  );
  const [filterEndDate, setFilterEndDate] = useState<Date | undefined>(
    undefined
  );

  const clearFilters = () => {
    setFilterType("all");
    setFilterSubscription(null);
    setFilterStartDate(undefined);
    setFilterEndDate(undefined);
  };

  const orderByParam =
    sorting.length > 0
      ? sorting.map((s) => `${s.id}:${s.desc ? "desc" : "asc"}`).join(",")
      : undefined;

  const filtersActive =
    filterType !== "all" ||
    filterSubscription ||
    filterStartDate ||
    filterEndDate;

  const queryParams: ApiQueryParams = {
    pageNumber: currentPage,
    pageSize,
    ...(orderByParam && { orderBy: orderByParam }),
    "filter:eq": {
      status: tab === "active" ? ["READ", "UNREAD"] : "ARCHIVED",
      ...(filterSubscription ? { subscriptionId: filterSubscription } : {}),
      ...(filterType !== "all" ? { type: filterType } : {}),
    },
    ...(filterStartDate && filterEndDate
      ? {
          "filter:bw": {
            createdAt: [
              filterStartDate.toISOString(),
              filterEndDate.toISOString(),
            ],
          },
        }
      : {}),
  };

  const {
    data: notificationsData,
    isLoading: loading,
    error,
  } = useNotifications(queryParams as any);

  const notificationsFirstPage = notificationsData?.pages?.[0];
  const data = (notificationsFirstPage?.content ??
    []) as unknown as CustomNotificationItem[];

  const totalPages = Math.max(notificationsFirstPage?.totalPages || 1, 1);

  if (error) {
    console.error("Error fetching notifications:", error);
  }

  useEffect(() => {
    if (!loading && notificationsFirstPage) {
      if (data.length === 0 && totalPages > 0 && currentPage > 1) {
        setCurrentPage(totalPages);
      }
    }
  }, [notificationsData, loading, totalPages, currentPage, data.length]);

  const { open } = useModal();

  const regions = useRegions();
  const { mutate: markAsRead } = useMarkNotificationAsRead();

  const handleRowClick = (notification: CustomNotificationItem) => {
    if (isDetailsDisabled) return;

    const potentialRegion = notification.regionId
      ? (regions.data ?? []).find(
          (region) => region.id === notification.regionId
        )
      : null;

    if (
      !notification.read &&
      tab === "active" &&
      notification.status === "UNREAD"
    ) {
      markAsRead({ id: notification.id });
    }

    open(Preview, {
      name: "preview-modal",
      title: notification.subscription
        ? (notification.subscription?.name ?? "") +
          " - " +
          (potentialRegion?.name ?? "")
        : t("epias_notification"),
      description: notification.textContent[currentLang],
      header: {
        title: notification.title[currentLang],
        description: "",
      },
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(page, 1));
  };

  const openFilter = () => {
    open(Filter as React.ComponentType<any>, {
      name: "filter-billing-modal",
      currentFilters: {
        type: filterType,
        subscription: filterSubscription,
        startDate: filterStartDate,
        endDate: filterEndDate,
      },
      // todo: any
      onConfirm: ({ type, subscription, startDate, endDate }: any) => {
        setFilterType(type);
        setFilterSubscription(subscription);
        setFilterStartDate(startDate);
        setFilterEndDate(endDate);
      },
      clearFilters,
    });
  };

  return (
    <>
      <Header
        sidebarData={sidebarData as any}
        title={t("title")}
        description={t("description")}
      />

      <Main>
        <div className="flex items-center justify-between py-4 w-full">
          <Tabs
            value={tab}
            onValueChange={(value) => setTab(value as "active" | "archives")}
            className="overflow-hidden rounded-lg border-2 border-muted"
          >
            <TabsList>
              <TabsTrigger value="active">{t("tabs.active")}</TabsTrigger>
              <TabsTrigger value="archives">{t("tabs.archives")}</TabsTrigger>
            </TabsList>
          </Tabs>
          <Button variant="outline" onClick={openFilter}>
            {t("common.filter", {
              ns: "common",
            })}
            <Iconify name="untitled:filter-lines" className="size-5" />
          </Button>
        </div>
        <AnimatedTransition
          loading={loading}
          skeleton={<NotificationsTableSkeleton />}
        >
          <DataTable
            columns={columns}
            data={data}
            meta={{
              t,
              regions: regions.data ?? [],
            }}
            onRowClick={handleRowClick}
            tableOptions={{
              getRowId: (row) => row.id,
              enableRowSelection: false,
              state: {
                pagination: {
                  pageIndex: Math.max(currentPage - 1, 0),
                  pageSize,
                },
                sorting,
              },
              manualPagination: true,
              manualSorting: true,
              pageCount: totalPages,
            }}
            onSortingChange={setSorting}
            empty={
              <Empty
                title={t("no_notifications_title")}
                description={
                  !filtersActive
                    ? t("no_notifications_description")
                    : t("no_notifications_description_no_filters")
                }
              >
                {filtersActive ? (
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="mt-4"
                  >
                    {t("common.clear_filters", {
                      ns: "common",
                    })}
                  </Button>
                ) : null}
              </Empty>
            }
            className="w-full h-full"
            onPageChange={handlePageChange}
          />
        </AnimatedTransition>
      </Main>
    </>
  );
}
