/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { DeleteAccountModal } from './delete-account-modal';

// Mock translations
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'common:common.cancel': 'Cancel',
        'common.cancel': 'Cancel',
        'delete_account': 'Delete Account',
        'confirmation.delete.title': 'Confirm Deletion',
        'confirmation.delete.description': 'This action cannot be undone. Your account and all associated data will be permanently deleted.',
      };
      return translations[key] || key;
    },
  }),
}));

// Fix the mock to generate correct testId for Cancel button
vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, variant, className }: any) => (
    <button 
      onClick={onClick}
      data-variant={variant}
      className={className || ''}
      data-testid={`button-${variant}-cancel`}
    >
      {children}
    </button>
  ),
}));

// Mock FeaturedIcon to include testId
vi.mock('@mass/shared/components/atoms/featured-icon', () => ({
  __esModule: true,
  default: ({ name, className }: any) => (
    <div className={className} data-testid="featured-icon">
      {name}
    </div>
  )
}));

describe('DeleteAccountModal', () => {
  const onHideMock = vi.fn();
  const onConfirmMock = vi.fn();

  it('renders correctly with all elements', () => {
    render(<DeleteAccountModal onHide={onHideMock} onConfirm={onConfirmMock} />);
    
    // Check for title and description
    expect(screen.getByText('Confirm Deletion')).toBeInTheDocument();
    expect(screen.getByText('This action cannot be undone. Your account and all associated data will be permanently deleted.')).toBeInTheDocument();
    
    // Use the correct testId for the cancel button
    expect(screen.getByTestId('button-outline-cancel')).toBeInTheDocument();
    expect(screen.getByText('Delete Account')).toBeInTheDocument();
    
    // Check for icon and pattern
    expect(screen.getByTestId('featured-icon')).toBeInTheDocument();
    const patternImg = screen.getByAltText('pattern');
    expect(patternImg).toBeInTheDocument();
  });

  it('calls onHide when cancel button is clicked', () => {
    render(<DeleteAccountModal onHide={onHideMock} onConfirm={onConfirmMock} />);
    
    const cancelButton = screen.getByTestId('button-outline-cancel');
    fireEvent.click(cancelButton);
    
    expect(onHideMock).toHaveBeenCalledTimes(1);
    expect(onConfirmMock).not.toHaveBeenCalled();
  });

  it('applies correct styling and layout', () => {
    render(<DeleteAccountModal onHide={onHideMock} onConfirm={onConfirmMock} />);
    
    // Get featured icon element with correct testId
    const iconElement = screen.getByTestId('featured-icon');
    const container = iconElement.parentElement;
    expect(container).toHaveClass('w-full', 'relative');
    
    // Check for proper button container styling
    const buttonContainer = screen.getByTestId('button-outline-cancel').closest('div');
    expect(buttonContainer).toHaveClass('w-full', 'flex', 'gap-2');
    
    // Check for featured icon styling
    expect(iconElement).toHaveClass('-mt-16');
  });
});