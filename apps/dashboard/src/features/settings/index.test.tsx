/**
 * @vitest-environment jsdom
 */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import * as React from 'react';

// -------- stubs -------------------------------------------------
vi.mock('react-i18next', () => ({
  __esModule: true,
  useTranslation: () => ({ t: (k: string) => k }),
  /* 👇 add missing export required by utils/i18n.ts */
  initReactI18next: { type: '3rdParty' },
}));
vi.mock('@mass/shared/components/organisms/layout/header', () => ({
  Header: (p: any) => <header>{p.title}{p.subContent}</header>,
}));
vi.mock('@mass/shared/components/organisms/layout/main', () => ({
  Main: ({ children }: any) => <main>{children}</main>,
}));
vi.mock('@mass/shared/components/ui/*', () => ({
  Button: (p: any) => <button {...p} />,
  Input: (p: any) => <input {...p} />,
  Separator: (p: any) => <hr {...p} />,
  Switch: (p: any) => <input type="checkbox" {...p} />,
}));
vi.mock('@mass/shared/components/molecules/language-switcher', () => ({
  LanguageSwitcher: () => <div>lang</div>,
}));
const openModal = vi.fn();
vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: () => ({ open: openModal }),
}));

// router helpers
const navigateSpy = vi.fn();
vi.mock('@tanstack/react-router', () => ({
  useNavigate: () => navigateSpy,
  useRouter: () => ({ state: { location: { pathname: '/settings' } } }),
}));

// auth store
vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({ auth: { user: { firstName: 'N', lastName: 'S', tckn: '1' } } }),
}));

// disabled utils
vi.mock('@/utils/use-disabled', () => ({
  useDisabled: vi.fn(),
  useIsDisabled: vi.fn(),
}));
import { useDisabled, useIsDisabled } from '@/utils/use-disabled';

// data hooks
vi.mock('./data/queries', () => ({
  useDeleteAccount: () => ({ mutate: vi.fn(), isPending: false }),
  useNotificationSettings: () => ({ data: {}, isLoading: false }),
  useSetNotificationSettings: () => ({ mutate: vi.fn() }),
}));

// component after mocks
import Settings from './index';

// ---------------------------------------------------------------
const renderPage = () => render(<Settings />);

beforeEach(() => {
  vi.resetAllMocks();
  // default: nothing disabled
  (useDisabled as any).mockReturnValue(undefined);
  (useIsDisabled as any).mockReturnValue(false);
});

describe('Settings page', () => {
  it('redirects when every tab disabled', () => {
    (useIsDisabled as any).mockReturnValue(true);
    renderPage();
    expect(navigateSpy).toHaveBeenCalled(); // redirect happened
  });

  it('renders overview tab and opens delete account modal', () => {
    renderPage();

    expect(screen.getByText('personal_info')).toBeInTheDocument();
    // select the *button* (avoids duplicate heading text)
    fireEvent.click(
      screen.getByRole('button', { name: 'delete_account' })
    );
    expect(openModal).toHaveBeenCalled();
  });

  it('renders notifications tab when overview disabled', () => {
    // overview disabled, notifications allowed
    (useIsDisabled as any).mockImplementation((keys: string[]) =>
      keys.includes('disabled.settings.user')
    );

    renderPage();
    fireEvent.click(screen.getByText('notifications'));

    // ensure navigation happens
    expect(navigateSpy).toHaveBeenCalledWith(
      expect.objectContaining({ to: '/settings/notification' })
    );
  });
});
