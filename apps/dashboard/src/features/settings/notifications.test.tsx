import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import * as React from 'react';

// global stubs (reuse small versions)
vi.mock('react-i18next', () => ({
  __esModule: true,
  useTranslation: () => ({ t: (k: string) => k }),
  initReactI18next: { type: '3rdParty' },
}));
vi.mock('@mass/shared/components/organisms/layout/header', () => ({ Header: (p: any) => <header>{p.title}</header> }));
vi.mock('@mass/shared/components/organisms/layout/main', () => ({ Main: ({ children }: any) => <main>{children}</main> }));
vi.mock('@mass/shared/components/ui/*', () => ({ Button: (p: any) => <button {...p} />, Separator: () => <hr/>, Switch: (p: any) => <input type="checkbox" {...p} /> }));
vi.mock('@tanstack/react-router', () => ({ Link: ({ children }: any) => <a>{children}</a> }));

import SettingsNotifications from './notifications';

describe('SettingsNotifications page', () => {
  it('renders titles and toggles', () => {
    render(<SettingsNotifications />);
    expect(screen.getByText('notification_options')).toBeInTheDocument();
    // ✅ role is "switch" for the Radix UI component
    expect(screen.getAllByRole('switch').length).toBeGreaterThan(0);
  });

  it('activate all button exists and clickable', () => {
    render(<SettingsNotifications />);
    const btn = screen.getByText('activate_all');
    expect(btn).toBeInTheDocument();
    fireEvent.click(btn); // simply ensure no crash
  });
});
