import { sidebarData } from "@/constants/sidebar-data";
import { useAuthStore } from "@/stores/auth";
import { Head<PERSON> } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { Button } from "@mass/shared/components/ui/button";
import { Separator } from "@mass/shared/components/ui/separator";
import { Switch } from "@mass/shared/components/ui/switch";
import { Tabs, TabsList, TabsTrigger } from "@mass/shared/components/ui/tabs";
import { Link } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";

export default function SettingsNotifications() {
  const { t } = useTranslation("settings");
  const user = useAuthStore((state) => state.auth.user);

  return (
    <>
      <Header
        title={t("settings")}
        sidebarData={sidebarData}
        description="Manage and customize your notification settings."
        breadcrumb={[
          {
            title: t("settings"),
            path: "/settings",
            icon: "untitled:settings-02"
          },
          {
            title: t("notifications")
          }
        ]}
        subContent={
          <Tabs
            defaultValue="notifications"
            className="mt-2 w-full"
          >
            <TabsList className="border-b border-border/50 !bg-transparent w-full overflow-x-auto">
              <TabsTrigger
                value="overview"
                className="rounded-none border-b pb-3 border-border/50 !shadow-none !bg-transparent data-[state=active]:border-primary data-[state=active]:border-b-2 data-[state=active]:text-primary flex-1 sm:flex-initial whitespace-nowrap"
                asChild
              >
                <Link to="/settings">{t("settings")}</Link>
              </TabsTrigger>
              <TabsTrigger
                value="notifications"
                className="rounded-none border-b pb-3 border-border/50 !shadow-none !bg-transparent data-[state=active]:border-primary data-[state=active]:border-b-2 data-[state=active]:text-primary flex-1 sm:flex-initial whitespace-nowrap"
                asChild
              >
                <Link to="/settings/notification">{t("notifications")}</Link>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        }
      />
      <Main fixed className="justify-start">
        <div className="w-full py-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-0">
          <h2 className="text-lg font-medium">
            {t("notification_options")}
          </h2>
          <Button className="w-full sm:w-auto">{t("activate_all")}</Button>
        </div>
        <Separator />
        <div className="w-full py-4 flex flex-col sm:flex-row items-start gap-4 sm:gap-0">
          <div className="font-normal text-sm flex-1">
            <div className="font-medium">{t("outage_notifs_title")}</div>
            <div className="">{t("outage_notifs_desc")}</div>
          </div>
          <div className="flex gap-2 flex-1 flex-col text-sm">
            <label className="flex items-center gap-2">
              <Switch /> {t("planned_outage_notifs")}
            </label>
            <label className="flex items-center gap-2">
              <Switch /> {t("unplanned_outage_notifs")}
            </label>
          </div>
        </div>
        <Separator />
        <div className="w-full py-4 flex flex-col sm:flex-row items-start gap-4 sm:gap-0">
          <div className="font-normal text-sm flex-1">
            <div className="font-medium">{t("warnings_title")}</div>
            <div className="">{t("warnings_desc")}</div>
          </div>
          <div className="flex gap-2 flex-1 flex-col text-sm">
            <label className="flex items-center gap-2">
              <Switch /> {t("unexpected_usage_warning")}
            </label>
            <label className="flex items-center gap-2">
              <Switch /> {t("closed_term_warning")}
            </label>
            <label className="flex items-center gap-2">
              <Switch /> {t("user_limit_warning")}
            </label>
          </div>
        </div>
        <div className="w-full flex justify-end">
          <Button>{t("save_changes")}</Button>
        </div>
      </Main>
    </>
  );
}