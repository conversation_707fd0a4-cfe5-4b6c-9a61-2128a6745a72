import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, cleanup } from '@testing-library/react';
import * as React from 'react';

// Stub Radix dropdown-menu primitives to avoid provider errors
vi.mock('@mass/shared/components/ui/dropdown-menu', () => ({
  __esModule: true,
  DropdownMenu: ({ children }: any) => <div>{children}</div>,
  DropdownMenuTrigger: ({ children, ...p }: any) => <div {...p}>{children}</div>,
  DropdownMenuContent: ({ children }: any) => <div>{children}</div>,
  DropdownMenuItem: ({ children, onClick }: any) => (
    <div data-testid="menu-item" onClick={onClick}>
      {children}
    </div>
  ),
  DropdownMenuSeparator: () => <div />,
}));

// ---- stubs ------------------------------------------------------------------
vi.mock('react-i18next', () => ({
  __esModule: true,
  useTranslation: () => ({
    t: (k: string) => k,
    i18n: { language: 'en' },
  }),
}));
vi.mock('@mass/shared/components/atoms/Iconify', () => ({
  default: () => <span data-testid="icon" />,
}));
vi.mock('@mass/shared/components/organisms/table/SortHeader', () => ({
  SortHeader: ({ title }: { title: string }) => <span>{title}</span>,
}));
vi.mock('@mass/shared/components/organisms/table/action', () => ({
  __esModule: true,
  default: ({ action, children }: any) => (
    <div>
      <div data-testid="action" onClick={() => {}}>{action}</div>
      {children}
    </div>
  ),
}));
const openSpy = vi.fn();
vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: () => ({ open: openSpy }),
}));
const deleteSpy = vi.fn();
vi.mock('../data/queries', () => ({
  useDeleteSubscription: () => ({ mutate: deleteSpy }),
}));

// Import columns after mocks
import { columns } from './columns';

// Helper to build minimal context for cell renderers
const buildCtx = (sample: any, meta: any = {}) => {
  const row = {
    id: 'r1',
    original: sample,
    getValue: (key: string) => (sample as any)[key],
    getIsSelected: () => false,
    toggleSelected: () => {},
  };
  const table = {
    options: { meta },
    getIsAllRowsSelected: () => false,
    toggleAllRowsSelected: () => {},
  } as any;
  const column = { id: 'dummy', toggleSorting: () => {} } as any;
  return { row, table, column };
};

beforeEach(() => {
  vi.resetAllMocks();
});
afterEach(() => cleanup());

describe('subscriptions table columns', () => {
  it('shows production badge for production type', () => {
    const sample = { name: 'Sub-Name', type: 'solar-production' };
    const { row } = buildCtx(sample);
    render(<>{(columns[0].cell as any)!({ row } as any)}</>);
    expect(screen.getByText('production')).toBeInTheDocument();
    expect(screen.getByText('Sub-Name')).toBeInTheDocument();
  });

  it('hides production badge for consumption type', () => {
    const sample2 = { name: 'C-Name', type: 'grid-consumption' };
    const { row: row2 } = buildCtx(sample2);
    render(<>{(columns[0].cell as any)!({ row: row2 } as any)}</>);
    expect(screen.queryByText('production')).toBeNull();
    expect(screen.getByText('C-Name')).toBeInTheDocument();
  });

  it('maps regionId to region name via table meta', () => {
    const sample = { regionId: 'r1' };
    const meta = { regions: [{ id: 'r1', name: 'Region-1' }] };
    const { row, table } = buildCtx(sample, meta);
    render(<>{(columns[1].cell as any)!({ row, table } as any)}</>);
    expect(screen.getByText('Region-1')).toBeInTheDocument();
  });

  it('action column opens edit modal', () => {
    const sample = {
      id: 's1',
      name: 'Sub',
      regionId: 'r1',
      type: 'grid-consumption',
      createdAt: '',
      individual: false,
      installationId: '',
      key: '',
      personIdentifier: '',
      startDate: '',
      userId: '',
    };
    const meta = { activeTab: 'subscriptions' };
    const { row, table } = buildCtx(sample, meta);
    
    // Wrap cell render in a component so hooks run in proper context
    const CellWrapper = () => <>{(columns[3].cell as any)!({ row, table } as any)}</>;
    render(<CellWrapper />);
    
    // Trigger menu actions
    fireEvent.click(screen.getByTestId('action'));
    // Simulate click on DropdownMenuItem (edit)
    fireEvent.click(screen.getByText('common.edit', { exact: false }));
    expect(openSpy).toHaveBeenCalled();
  });
});
