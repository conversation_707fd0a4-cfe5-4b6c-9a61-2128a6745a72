import Iconify from "@mass/shared/components/atoms/Iconify";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Action from "@mass/shared/components/organisms/table/action";
import { SortHeader } from "@mass/shared/components/organisms/table/SortHeader";
import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@mass/shared/components/ui/dropdown-menu";
import type { ColumnDef, TableMeta } from "@tanstack/react-table";
import { format } from "date-fns";
import * as Locale from "date-fns/locale";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDeleteSubscription } from "../data/queries";
import Delete from "./modals/delete";
import Edit from "./modals/edit";
import type { Region } from "@/services/api/subscriptions";

export interface Subscription {
  id: string;
  createdAt: string;
  individual: boolean;
  installationId: string;
  key: string;
  name: string;
  personIdentifier: string;
  regionId: string;
  startDate: string;
  type: string;
  userId: string;
}

interface SubscriptionsTableMeta extends TableMeta<Subscription> {
  activeTab: string;
}

export const columns: ColumnDef<Subscription, unknown>[] = [
  {
    enableSorting: true,
    accessorKey: "name",
    header: ({ table, column }) => {
      const { t } = useTranslation("subscriptions");
      const meta = table.options.meta as SubscriptionsTableMeta;

      return (
        <div className="flex items-center gap-3 pl-2">
          {/* <div onClick={(e) => e.stopPropagation()}>
            <Checkbox
              checked={table.getIsAllRowsSelected()}
              onCheckedChange={(checked) => {
                table.toggleAllRowsSelected(!!checked);
              }}
              aria-label={t("table.select_all", { ns: "common" })}
            />
          </div> */}
          <SortHeader
            column={column}
            title={t(
              meta.activeTab === "subscriptions"
                ? "subscription_name"
                : "facility_name"
            )}
          />
        </div>
      );
    },
    cell: ({ row }) => {
      const { t } = useTranslation("subscriptions");

      return (
        <div className="flex items-center gap-3 pl-2">
          {/* <div onClick={(e) => e.stopPropagation()}>
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(checked) => {
                row.toggleSelected(!!checked);
              }}
              aria-label={t("table.select_row", { row: row.id, ns: "common" })}
            />
          </div> */}
          <span className="font-medium text-gray-700 flex items-center justify-center gap-2">
            {row.original.type.endsWith("-consumption") ? null : (
              <span className="px-2 py-1 rounded-full bg-green-50 border border-green-200 text-green-800 flex items-center justify-center gap-1 text-xs">
                <span className="px-1 py-1 bg-success-600 rounded-full"></span>
                {t("production")}
              </span>
            )}
            {row.getValue("name")}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: "regionId",
    header: ({ column }) => {
      const { t } = useTranslation("subscriptions");
      return (
        <div className="text-right">
          <SortHeader column={column} title={t("distribution_company")} />
        </div>
      );
    },
    cell: ({ row, table }) => {
      const regions = (table.options.meta as any).regions as Region[];
      const region = regions.find(r => r.id === row.original.regionId);
      return <div className="text-sm text-right">{region?.name ?? "-"}</div>;
    },
  },
  {
    accessorKey: "startDate",
    enableSorting: true,
    header: ({ column }) => {
      const { t } = useTranslation("subscriptions");
      return (
        <div className="text-right">
          <SortHeader column={column} title={t("application_date")} />
        </div>
      );
    },
    cell: ({ row }) => {
      const { i18n } = useTranslation();
      const locale = Locale[i18n.language as "tr"];

      return (
        <div>
          <div className="text-sm text-right">
            {format(new Date(row.getValue("startDate")), "d MMM, yyyy", {
              locale,
            })}
          </div>
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row, table }) => {
      const { t } = useTranslation("subscriptions");
      const { open } = useModal();
      const [ellipsisOpen, setEllipsisOpen] = useState(false);
      const meta = table.options.meta as SubscriptionsTableMeta;
      const subscription = row.original;

      const handleEdit = (e: React.MouseEvent) => {
        e.stopPropagation();
        setEllipsisOpen(false);

        open(Edit, {
          name: "edit-billing-modal",
          subscription,
          activeTab: meta.activeTab,
          header: {
            title: t("edit_subscription"),
            description: t("edit_description"),
          },
        });
      };

      const deleteMutation = useDeleteSubscription();

      const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation();
        setEllipsisOpen(false);

        open(Delete, {
          name: "delete-billing-modal",
          onConfirm: () => {
            return new Promise<void>((resolve, reject) =>
              deleteMutation.mutate(subscription.id, {
                onSuccess: () => {
                  resolve();
                },
                onError: (error) => {
                  console.error("Error deleting subscription:", error);
                  reject(error);
                },
              })
            );
          },
          activeTab: meta.activeTab,
        });
      };

      return (
        <div className="flex w-full justify-end items-end">
          <Action
            action={
              <div
                className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 cursor-pointer"
                onClick={(e) => e.stopPropagation()}
              >
                <Iconify name="untitled:dots-vertical" className="size-4" />
              </div>
            }
            open={ellipsisOpen}
            onOpenChange={setEllipsisOpen}
          >
            <DropdownMenuItem
              className="flex text-xs font-medium items-center justify-between"
              onClick={handleEdit}
            >
              {t("common.edit", {
                ns: "common",
              })}
              <Iconify name="untitled:edit-05" className="size-2" />
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              className="flex text-xs font-medium text-error-500 hover:!text-error-600 items-center justify-between"
              onClick={handleDelete}
            >
              {t("delete_action", {
                ns: "common",
              })}
              <Iconify name="untitled:trash-01" className="size-2" />
            </DropdownMenuItem>
          </Action>
        </div>
      );
    },
  },
];
