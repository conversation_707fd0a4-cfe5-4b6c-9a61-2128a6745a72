import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import Delete from './delete';

// Mock useTranslation
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock assets
vi.mock('@mass/shared/assets/pattern_decorative.svg', () => ({
  default: { default: 'pattern.svg' }
}));

// Mock delete mutation hook
const mockMutate = vi.fn((_, options) => {
  if (options && options.onSuccess) {
    options.onSuccess();
  }
});

const mockDeleteSubscription = {
  isPending: false,
  mutate: mockMutate
};

vi.mock('../../data/queries', () => ({
  useDeleteSubscription: () => mockDeleteSubscription,
}));

describe('Delete Modal', () => {
  const onHideMock = vi.fn();
  const onConfirmMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the delete confirmation modal', () => {
    render(
      <Delete 
        onHide={onHideMock} 
        onConfirm={onConfirmMock} 
        activeTab="subscriptions"
      />
    );
    
    expect(screen.getByText('delete_confirmation')).toBeInTheDocument();
    expect(screen.getByText('delete_description')).toBeInTheDocument();
    expect(screen.getByText('common.cancel')).toBeInTheDocument();
    expect(screen.getByText('common.delete')).toBeInTheDocument();
  });

  it('calls onHide when cancel button is clicked', () => {
    render(
      <Delete 
        onHide={onHideMock} 
        onConfirm={onConfirmMock} 
        activeTab="subscriptions"
      />
    );
    
    fireEvent.click(screen.getByText('common.cancel'));
    expect(onHideMock).toHaveBeenCalledTimes(1);
  });

  it('calls onConfirm and onHide when delete button is clicked', async () => {
    render(
      <Delete 
        onHide={onHideMock} 
        onConfirm={onConfirmMock} 
        activeTab="subscriptions"
      />
    );
    
    fireEvent.click(screen.getByText('common.delete'));
    
    expect(onConfirmMock).toHaveBeenCalledTimes(1);
    expect(onHideMock).toHaveBeenCalledTimes(1);
  });

  it('disables delete button when mutation is pending', () => {
    // Save original state
    const originalIsPending = mockDeleteSubscription.isPending;
    
    // Override the mock for this specific test
    mockDeleteSubscription.isPending = true;

    render(
      <Delete 
        onHide={onHideMock} 
        onConfirm={onConfirmMock} 
        activeTab="subscriptions"
      />
    );
    
    const deleteButton = screen.getByText('common.delete');
    expect(deleteButton).toBeDisabled();
    
    // Reset the mock for other tests
    mockDeleteSubscription.isPending = originalIsPending;
    // No need to restore mock since we're directly modifying the mock object
  });
});
