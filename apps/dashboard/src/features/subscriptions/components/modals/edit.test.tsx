import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import Edit from './edit';
import { Subscription } from '../columns';

// Mock useTranslation
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock update mutation hook
const mockMutate = vi.fn((data, options) => {
  options.onSuccess();
});

const mockUpdateSubscription = {
  isPending: false,
  mutate: mockMutate
};

vi.mock('../../data/queries', () => ({
  useUpdateSubscription: () => mockUpdateSubscription,
}));

// Mock AutoForm component
vi.mock('@mass/shared/components/ui/auto-form/index', () => ({
  default: ({ children, onSubmit, onParsedValuesChange }: { children: React.ReactNode, onSubmit: (data: any) => void, onParsedValuesChange: () => void }) => {
    return (
      <div data-testid="auto-form">
        <button 
          data-testid="trigger-value-change" 
          onClick={() => onParsedValuesChange()}
        >
          Trigger Value Change
        </button>
        <button 
          data-testid="submit-form" 
          onClick={() => onSubmit({ name: 'Updated Name' })}
        >
          Submit Form
        </button>
        {children}
      </div>
    );
  },
}));

describe('Edit Modal', () => {
  const onHideMock = vi.fn();
  const mockSubscription: Subscription = {
    id: '123',
    name: 'Test Subscription',
    createdAt: '2023-01-01T00:00:00Z',
    individual: true,
    installationId: 'install123',
    key: 'key123',
    personIdentifier: 'person123',
    regionId: 'region1',
    startDate: '2023-01-01T00:00:00Z',
    type: 'individual',
    userId: 'user123',
  };
  
  let queryClient: QueryClient;
  
  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.clearAllMocks();
  });
  
  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <Edit 
          onHide={onHideMock} 
          activeTab="subscriptions"
          subscription={mockSubscription}
          subscriptionId={mockSubscription.id}
        />
      </QueryClientProvider>
    );
  };

  it('renders the edit form', () => {
    renderComponent();
    
    expect(screen.getByTestId('auto-form')).toBeInTheDocument();
    expect(screen.getByText('common.save')).toBeInTheDocument();
  });

  it('disables submit button by default', () => {
    renderComponent();
    
    expect(screen.getByText('common.save')).toBeDisabled();
  });

  it('enables submit button when values change', () => {
    renderComponent();
    
    fireEvent.click(screen.getByTestId('trigger-value-change'));
    expect(screen.getByText('common.save')).not.toBeDisabled();
  });

  it('submits form and calls onHide on success', async () => {
    renderComponent();
    
    // Enable the button by changing values
    fireEvent.click(screen.getByTestId('trigger-value-change'));
    
    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));
    
    await waitFor(() => {
      expect(onHideMock).toHaveBeenCalled();
    });
  });

  it('displays error message when update fails', async () => {
    // Save the original function
    const originalMutate = mockUpdateSubscription.mutate;
    
    // Override the mock to simulate an error
    mockUpdateSubscription.mutate = vi.fn((_, options) => {
      options.onError({ message: 'update_failed' });
    });

    renderComponent();
    
    // Enable the button by changing values
    fireEvent.click(screen.getByTestId('trigger-value-change'));
    
    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));
    
    await waitFor(() => {
      expect(screen.getByText('error.update_failed')).toBeInTheDocument();
    });
    
    // Reset the mock for other tests
    mockUpdateSubscription.mutate = originalMutate;
  });

  it('disables submit button when mutation is pending', async () => {
    // Save the original isPending value
    const originalIsPending = mockUpdateSubscription.isPending;
    
    // Override the mock to simulate pending state
    mockUpdateSubscription.isPending = true;

    renderComponent();
    
    // Even if values change, button should be disabled when pending
    fireEvent.click(screen.getByTestId('trigger-value-change'));
    expect(screen.getByText('common.save')).toBeDisabled();
    
    // Reset the mock for other tests
    mockUpdateSubscription.isPending = originalIsPending;
  });
});
