// @vitest-environment jsdom
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Export from './export';
import { toast } from 'sonner';

vi.mock('sonner', () => ({
  toast: {
    promise: vi.fn(),
  },
}));

const mockMutateAsync = vi.fn();

vi.mock('../../data/queries', () => ({
  useExportSubscriptionUsageMutation: vi.fn(() => ({
    mutateAsync: mockMutateAsync,
    isPending: false,
  })),
}));

const renderWithClient = (ui: React.ReactElement) => {
  const queryClient = new QueryClient();
  return render(<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>);
};

describe('Export Component', () => {
  const onHide = vi.fn();
  const onConfirm = vi.fn();
  const defaultProps = {
    title: "Export Data",
    description: "Export your subscription data",
    subscriptionId: "sub123",
    startDate: new Date("2023-01-01"),
    endDate: new Date("2023-01-31"),
    granularity: "day",
    compareTo: [] as string[],
    onHide,
    onConfirm,
  };

  beforeEach(() => {
    vi.resetAllMocks();
    mockMutateAsync.mockResolvedValue({
      files: [{ url: 'https://example.com/file.csv', fileName: 'export.csv' }],
    });
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      blob: async () => new Blob(["file content"], { type: "text/csv" }),
    });

    global.URL.createObjectURL = vi.fn().mockReturnValue('blob:http://example.com/file.csv');
    global.URL.revokeObjectURL = vi.fn();
  });

  afterEach(() => cleanup());

  it('renders component with file type and hourly selection when compareTo is empty', () => {
    renderWithClient(<Export {...defaultProps} />);
  
    expect(screen.getByText("Export Data")).toBeInTheDocument();
    expect(screen.getByText("usage.export.filetype")).toBeInTheDocument();
    expect(screen.getByText("usage.export.hourly.label")).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /common.cancel/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /common.export/i })).toBeInTheDocument();
  });

  it('does not render hourly selection when compareTo is not empty', () => {
    renderWithClient(<Export {...defaultProps} compareTo={["other"]} />);
  
    expect(screen.queryByText("usage.export.hourly.label")).toBeNull();
  });

  it('calls onHide when cancel button is clicked', () => {
    renderWithClient(<Export {...defaultProps} />);
    fireEvent.click(screen.getByRole('button', { name: /common.cancel/i }));
    expect(onHide).toHaveBeenCalled();
  });

  it('starts export process on export button click and calls onHide and onConfirm', async () => {
    renderWithClient(<Export {...defaultProps} />);
  
    fireEvent.click(screen.getByRole('button', { name: /common.export/i }));
  
    await waitFor(() => {
      expect(mockMutateAsync).toHaveBeenCalledWith("csv");
      expect(toast.promise).toHaveBeenCalled();
      expect(onHide).toHaveBeenCalled();
      expect(onConfirm).toHaveBeenCalled();
    });
  });
});