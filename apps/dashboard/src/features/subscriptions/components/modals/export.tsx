import FeaturedIcon from "@mass/shared/components/atoms/featured-icon";
import type { ModalProps } from "@mass/shared/components/organisms/modal/provider";
import { Button } from "@mass/shared/components/ui/button";
import { Label } from "@mass/shared/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mass/shared/components/ui/select";
import { useTranslation } from "react-i18next";
import { useExportSubscriptionUsageMutation } from "../../data/queries";
import { useState } from "react";
import { toast } from "sonner";

interface Props extends ModalProps {
  onHide: () => void;
  onConfirm?: () => void | Promise<void>;
}

export default function Export({
  onHide,
  onConfirm,
  title,

  subscriptionId,
  startDate,
  endDate,
  granularity,
  compareTo,
}: Props & {
  title: string;
  description: string;
  subscriptionId: string;
  startDate: Date;
  endDate: Date;
  granularity: string;
  compareTo: string[];
}) {
  const { t } = useTranslation("subscriptions");
  const [fileType, setFileType] = useState("pdf");
  const [hourlyBreakdown, setHourlyBreakdown] = useState("disabled");
  const { mutateAsync, isPending } = useExportSubscriptionUsageMutation(
    subscriptionId,
    startDate,
    endDate,
    (hourlyBreakdown === "enabled" ? "hour" : granularity) as any, // todo
    compareTo
  );

  console.log("compareTo", compareTo);

  return (
    <div className="p-6 pt-4 flex flex-col gap-5">
      <div className="w-full relative flex flex-col items-start justify-start gap-2 text-left text-lg text-gray-900 font-text-sm-regular">
        <FeaturedIcon name="untitled:download-01" className="-mt-16" />
        <div className="self-stretch relative leading-[28px] font-semibold">
          {title}
        </div>
      </div>
      <div className="w-full gap-2 flex flex-col">
        <div className="flex flex-col gap-2">
          <Label>{t("usage.export.filetype")}</Label>
          <Select value={fileType} onValueChange={setFileType}>
            <SelectTrigger>
              <SelectValue placeholder={t("usage.export.filetype")} />
            </SelectTrigger>
            <SelectContent>
              {["pdf", "csv", "xlsx"].map((option) => (
                <SelectItem key={option} value={option}>
                  {option.toUpperCase()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {compareTo.length === 0 ? (
          <div className="flex flex-col gap-2">
            <Label>{t("usage.export.hourly.label")}</Label>
            <Select value={hourlyBreakdown} onValueChange={setHourlyBreakdown}>
              <SelectTrigger>
                <SelectValue placeholder={t("usage.export.hourly.label")} />
              </SelectTrigger>
              <SelectContent>
                {["disabled", "enabled"].map((option) => (
                  <SelectItem key={option} value={option}>
                    {t("usage.export.hourly." + option)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ) : null}
      </div>

      <div className="w-full flex gap-2 items-center">
        <Button variant="outline" onClick={onHide} className="w-full">
          {t("common.cancel", {
            ns: "common",
          })}
        </Button>
        <Button
          className="w-full"
          onClick={async () => {
            const download = async () => {
              const data = await mutateAsync(fileType);

              for (const file of data.files) {
                const { url, fileName } = file;

                const outsideRes = await fetch(url);

                const blob = await outsideRes.blob();
                const urlObject = URL.createObjectURL(blob);

                const link = document.createElement("a");
                link.href = urlObject;
                link.download = fileName;
                link.click();

                URL.revokeObjectURL(url);
                URL.revokeObjectURL(urlObject);
              }
            };

            const downloadPromise = download();

            toast.promise(downloadPromise, {
              loading: t("download_loading"),
              success: t("download_successful"),
              error: t("download_failed"),
            });

            await downloadPromise;

            onHide();
            onConfirm?.();
          }}
          disabled={isPending}
        >
          {t("common.export", {
            ns: "common",
          })}
        </Button>
      </div>
    </div>
  );
}
