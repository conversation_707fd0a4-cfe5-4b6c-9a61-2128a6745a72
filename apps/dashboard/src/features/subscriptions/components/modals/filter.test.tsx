import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import React from 'react';
import Filter from './filter';
import { subscriptionService } from '@/services/api/subscriptions';

// Mock useTranslation
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock featured icon
vi.mock('@mass/shared/components/atoms/featured-icon', () => ({
  default: ({ name, className }: { name: string; className: string }) => (
    <div data-testid={`featured-icon-${name}`} className={className}>
      Icon: {name}
    </div>
  ),
}));

// Mock subscription service
vi.mock('@/services/api/subscriptions', () => ({
  subscriptionService: {
    getRegionsPaginated: vi.fn().mockResolvedValue({
      items: [
        { id: 'region1', name: 'Region 1' },
        { id: 'region2', name: 'Region 2' }
      ],
      hasNextPage: false
    }),
    getRegionById: vi.fn().mockImplementation(async (id) => {
      if (id === 'region1') return { id: 'region1', name: 'Region 1' };
      if (id === 'region2') return { id: 'region2', name: 'Region 2' };
      return null;
    })
  },
}));

// Mock AutoFormSearchEnum component
vi.mock('@mass/shared/components/ui/auto-form/fields/search-enum', () => ({
  default: ({ label, field }: { label: string; field: any }) => (
    <div data-testid="auto-form-search-enum">
      <label>{label}</label>
      <input 
        type="text" 
        value={field.value || ''} 
        onChange={(e) => field.onChange(e.target.value)}
        data-testid="distribution-company-input"
      />
    </div>
  ),
}));

describe('Filter Modal', () => {
  const onHideMock = vi.fn();
  const onConfirmMock = vi.fn();
  const clearFiltersMock = vi.fn();
  
  const currentFilters = {
    type: 'all',
    facilityType: 'all',
    distributionCompany: []
  };
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the filter modal with correct elements', () => {
    render(
      <Filter 
        onHide={onHideMock} 
        onConfirm={onConfirmMock}
        activeTab="subscriptions"
        currentFilters={currentFilters}
        clearFilters={clearFiltersMock}
      />
    );
    
    expect(screen.getByRole('button', { name: 'common.filter' })).toBeInTheDocument();
    expect(screen.getByText('subscription_type')).toBeInTheDocument();
    expect(screen.getByText('facility_type')).toBeInTheDocument();
    expect(screen.getByText('distribution_company')).toBeInTheDocument();
    expect(screen.getByText('common.clear_filters')).toBeInTheDocument();
    expect(screen.getByTestId('featured-icon-untitled:filter-lines')).toBeInTheDocument();
  });

  it('initializes with current filter values', () => {
    const specificFilters = {
      type: 'individual',
      facilityType: 'electricity-production',
      distributionCompany: ['region1']
    };
    
    render(
      <Filter 
        onHide={onHideMock} 
        onConfirm={onConfirmMock}
        activeTab="subscriptions"
        currentFilters={specificFilters}
        clearFilters={clearFiltersMock}
      />
    );
    
    // Could verify select values if we had better mocks for selects
    // But we'll test functionality with the next tests
  });

  it('allows changing subscription type filter', async () => {
    render(
      <Filter 
        onHide={onHideMock} 
        onConfirm={onConfirmMock}
        activeTab="subscriptions"
        currentFilters={currentFilters}
        clearFilters={clearFiltersMock}
      />
    );
    
    // Open subscription type dropdown
    const typeSelects = screen.getAllByRole('combobox');
    fireEvent.click(typeSelects[0]);
    
    // Select individual type
    const individualOption = screen.getByText('subscription_type_individual');
    fireEvent.click(individualOption);
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: 'common.filter' }));
    
    // Verify onConfirm called with updated values
    expect(onConfirmMock).toHaveBeenCalledWith(expect.objectContaining({
      type: 'individual'
    }));
  });

  it('allows changing facility type filter', async () => {
    render(
      <Filter 
        onHide={onHideMock} 
        onConfirm={onConfirmMock}
        activeTab="subscriptions"
        currentFilters={currentFilters}
        clearFilters={clearFiltersMock}
      />
    );
    
    // Open facility type dropdown
    const facilityTypeSelects = screen.getAllByRole('combobox');
    fireEvent.click(facilityTypeSelects[1]);
    
    // Select electricity production type
    const productionOption = screen.getByText('electricity_production');
    fireEvent.click(productionOption);
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: 'common.filter' }));
    
    // Verify onConfirm called with updated values
    expect(onConfirmMock).toHaveBeenCalledWith(expect.objectContaining({
      facilityType: 'electricity-production'
    }));
  });

  it('allows setting distribution company filter', async () => {
    render(
      <Filter 
        onHide={onHideMock} 
        onConfirm={onConfirmMock}
        activeTab="subscriptions"
        currentFilters={currentFilters}
        clearFilters={clearFiltersMock}
      />
    );
    
    // Set distribution company
    const distributionInput = screen.getByTestId('distribution-company-input');
    fireEvent.change(distributionInput, { target: { value: 'region1' } });
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: 'common.filter' }));
    
    // Verify onConfirm called with updated values
    expect(onConfirmMock).toHaveBeenCalledWith(expect.objectContaining({
      distributionCompany: ['region1']
    }));
  });

  it('clears filters when clear filters button is clicked', () => {
    render(
      <Filter 
        onHide={onHideMock} 
        onConfirm={onConfirmMock}
        activeTab="subscriptions"
        currentFilters={currentFilters}
        clearFilters={clearFiltersMock}
      />
    );
    
    fireEvent.click(screen.getByText('common.clear_filters'));
    
    expect(clearFiltersMock).toHaveBeenCalled();
    expect(onHideMock).toHaveBeenCalled();
  });
});
