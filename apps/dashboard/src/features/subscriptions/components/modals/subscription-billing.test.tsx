import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import SubscriptionBillingModal from './subscription-billing';

vi.mock('@/features/auth', () => ({
  useMe: () => ({
    data: { 
      id: 'user1', 
      tckn: '12345678901',
      name: 'Test User'
    },
    isLoading: false,
  })
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

const mockMutate = vi.fn((_data, options) => {
  if (options && options.onSuccess) {
    options.onSuccess();
  }
});

const mockCreateSubscription = {
  isPending: false,
  mutate: mockMutate
};

vi.mock('../../data/queries', () => ({
  useCreateSubscription: () => mockCreateSubscription,
}));

// Mock AutoForm component
vi.mock('@mass/shared/components/ui/auto-form/index', () => ({
  default: ({ children, onSubmit, onParsedValuesChange, onValidChange }: { 
    children: React.ReactNode, 
    onSubmit: (data: any) => void,
    onParsedValuesChange: () => void,
    onValidChange: (valid: boolean) => void
  }) => {
    return (
      <div data-testid="auto-form">
        <button 
          data-testid="trigger-value-change" 
          onClick={() => onParsedValuesChange()}
        >
          Trigger Value Change
        </button>
        <button 
          data-testid="trigger-valid-change" 
          onClick={() => onValidChange(true)}
        >
          Trigger Valid Change
        </button>
        <button 
          data-testid="submit-form" 
          onClick={() => onSubmit({
            name: 'New Subscription',
            type: 'individual',
            distributionCompany: 'region1',
            installationId: 'inst123'
          })}
        >
          Submit Form
        </button>
        {children}
      </div>
    );
  },
}));

// Mock skeleton loader
vi.mock('../skeleton-loader', () => ({
  BillingFormSkeleton: () => <div data-testid="billing-form-skeleton">Loading...</div>
}));

describe('SubscriptionBillingModal', () => {
  const onHideMock = vi.fn();
  
  let queryClient: QueryClient;
  
  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <SubscriptionBillingModal 
          onHide={onHideMock} 
          activeTab="subscriptions"
        />
      </QueryClientProvider>
    );
  };

  it('renders the subscription billing form', () => {
    renderComponent();
    
    expect(screen.getByTestId('auto-form')).toBeInTheDocument();
    expect(screen.getByText('common.save')).toBeInTheDocument();
  });

  it('disables submit button by default', () => {
    renderComponent();
    
    expect(screen.getByText('common.save')).toBeDisabled();
  });

  it('enables submit button when values change and form is valid', () => {
    renderComponent();
    
    // Trigger value change
    fireEvent.click(screen.getByTestId('trigger-value-change'));
    
    // Form should still be disabled because it's not valid yet
    expect(screen.getByText('common.save')).toBeDisabled();
    
    // Trigger valid change
    fireEvent.click(screen.getByTestId('trigger-valid-change'));
    
    expect(screen.getByText('common.save')).not.toBeDisabled();
  });

  it('submits form and calls onHide on success', async () => {
    renderComponent();
    
    // Make form valid and changed
    fireEvent.click(screen.getByTestId('trigger-value-change'));
    fireEvent.click(screen.getByTestId('trigger-valid-change'));
    
    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));
    
    await waitFor(() => {
      expect(onHideMock).toHaveBeenCalled();
    });
  });

  it('displays error message when creation fails', async () => {
    // Save original mutate function
    const originalMutate = mockCreateSubscription.mutate;
    
    // Override the mock to simulate an error
    mockCreateSubscription.mutate = vi.fn((_, options) => {
      if (options && options.onError) {
        options.onError({ message: 'creation_failed' });
      }
    });

    renderComponent();
    
    // Make form valid and changed
    fireEvent.click(screen.getByTestId('trigger-value-change'));
    fireEvent.click(screen.getByTestId('trigger-valid-change'));
    
    // Submit the form
    fireEvent.click(screen.getByTestId('submit-form'));
    
    await waitFor(() => {
      expect(screen.getByText('error.creation_failed')).toBeInTheDocument();
    });
    
    // Reset the mock for other tests
    mockCreateSubscription.mutate = originalMutate;
  });

  it('disables submit button when mutation is pending', async () => {
    // Save original isPending state
    const originalIsPending = mockCreateSubscription.isPending;
    
    // Override the mock to simulate pending state
    mockCreateSubscription.isPending = true;

    renderComponent();
    
    // Even if values change and form is valid, button should be disabled when pending
    fireEvent.click(screen.getByTestId('trigger-value-change'));
    fireEvent.click(screen.getByTestId('trigger-valid-change'));
    
    expect(screen.getByText('common.save')).toBeDisabled();
    
    // Reset the mock for other tests
    mockCreateSubscription.isPending = originalIsPending;
  });

  // This test is skipped because we can't easily mock React.useState to force 
  // the isLoading state to be true in a reliable way.
  // Usually for testing loading states, the component should accept a prop
  // or we should test the actual loading scenario
  it.todo('shows skeleton loader when isLoading is true');
});
