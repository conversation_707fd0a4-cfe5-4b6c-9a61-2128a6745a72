import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { 
  SubscriptionsTableSkeleton, 
  BillingFormSkeleton, 
  SubscriptionDetailSkeleton 
} from './skeleton-loader'

// Mock Card components
vi.mock('@mass/shared/components/ui/card', () => ({
  Card: ({ children, className }: any) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
  CardContent: ({ children, className }: any) => (
    <div data-testid="card-content" className={className}>
      {children}
    </div>
  ),
}))

// Mock Skeleton component
vi.mock('@mass/shared/components/ui/skeleton', () => ({
  Skeleton: ({ className }: any) => (
    <div data-testid="skeleton" className={className}>
      Loading...
    </div>
  ),
}))

describe('Subscription Skeleton Loaders', () => {
  describe('SubscriptionsTableSkeleton', () => {
    it('should render table skeleton structure', () => {
      render(<SubscriptionsTableSkeleton />)

      const skeletons = screen.getAllByTestId('skeleton')
      expect(skeletons.length).toBeGreaterThan(10) // Header + multiple rows

      // Check main container classes
      const container = screen.getAllByTestId('skeleton')[0].closest('.w-full.space-y-3.h-full')
      expect(container).toBeInTheDocument()
    })

    it('should render header row skeleton', () => {
      render(<SubscriptionsTableSkeleton />)

      // Check for header elements
      const skeletons = screen.getAllByTestId('skeleton')
      expect(skeletons[0]).toHaveClass('h-8 w-64') // Search/title skeleton
      expect(skeletons[1]).toHaveClass('h-8 w-32') // Button skeleton
    })

    it('should render 5 table rows', () => {
      render(<SubscriptionsTableSkeleton />)

      // Count skeletons in rows (each row has 5 columns = 5 skeletons)
      const rowSkeletons = screen.getAllByTestId('skeleton').filter(skeleton => 
        skeleton.className.includes('h-5 w-40') || 
        skeleton.className.includes('h-5 w-24') ||
        skeleton.className.includes('h-5 w-28') ||
        skeleton.className.includes('h-8 w-8')
      )
      expect(rowSkeletons.length).toBeGreaterThan(20) // 5 rows × multiple columns
    })

    it('should have correct grid structure', () => {
      const { container } = render(<SubscriptionsTableSkeleton />)

      const gridElements = container.querySelectorAll('.grid-cols-5')
      expect(gridElements.length).toBeGreaterThan(0)
    })
  })

  describe('BillingFormSkeleton', () => {
    it('should render billing form skeleton structure', () => {
      render(<BillingFormSkeleton />)

      const skeletons = screen.getAllByTestId('skeleton')
      expect(skeletons.length).toBe(11) // 5 labels + 5 inputs + 1 button
    })

    it('should render 5 form fields', () => {
      render(<BillingFormSkeleton />)

      // Each field has a label (h-4 w-24) and input (h-10 w-full)
      const labelSkeletons = screen.getAllByTestId('skeleton').filter(skeleton =>
        skeleton.className.includes('h-4 w-24')
      )
      const inputSkeletons = screen.getAllByTestId('skeleton').filter(skeleton =>
        skeleton.className.includes('h-10 w-full')
      )

      expect(labelSkeletons).toHaveLength(5)
      expect(inputSkeletons).toHaveLength(6) // 5 inputs + 1 button
    })

    it('should have correct container structure', () => {
      const { container } = render(<BillingFormSkeleton />)

      const spaceContainer = container.querySelector('.space-y-3')
      expect(spaceContainer).toBeInTheDocument()
    })
  })

  describe('SubscriptionDetailSkeleton', () => {
    it('should render subscription detail skeleton structure', () => {
      render(<SubscriptionDetailSkeleton />)

      expect(screen.getByTestId('card')).toBeInTheDocument()
      expect(screen.getByTestId('card-content')).toBeInTheDocument()

      const skeletons = screen.getAllByTestId('skeleton')
      expect(skeletons.length).toBeGreaterThan(15) // Multiple sections with various skeletons
    })

    it('should render card with correct classes', () => {
      render(<SubscriptionDetailSkeleton />)

      const card = screen.getByTestId('card')
      expect(card).toHaveClass('!px-0 rounded-none shadow-none w-full')

      const cardContent = screen.getByTestId('card-content')
      expect(cardContent).toHaveClass('space-y-6')
    })

    it('should render invoice/contract information section', () => {
      const { container } = render(<SubscriptionDetailSkeleton />)

      const infoSection = container.querySelector('.border-b.border-gray-200.py-4')
      expect(infoSection).toBeInTheDocument()
      expect(infoSection).toHaveClass('w-full flex flex-col sm:flex-row sm:justify-between gap-2')
    })

    it('should render grid sections', () => {
      const { container } = render(<SubscriptionDetailSkeleton />)

      const gridSection = container.querySelector('.grid.gap-2.md\\:grid-cols-2')
      expect(gridSection).toBeInTheDocument()
    })

    it('should render table-like structure', () => {
      const { container } = render(<SubscriptionDetailSkeleton />)

      const tableHeader = container.querySelector('.border-b.border-gray-100')
      expect(tableHeader).toBeInTheDocument()

      const tableRow = container.querySelector('.py-4')
      expect(tableRow).toBeInTheDocument()
    })

    it('should have multiple sections', () => {
      const { container } = render(<SubscriptionDetailSkeleton />)

      const sections = container.querySelectorAll('.space-y-6 > div')
      expect(sections.length).toBeGreaterThan(2) // Multiple content sections
    })
  })
})
