/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';
import { DataTab } from './data';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useSubscriptionUsageData } from './data/queries';
import { UseMutationResult } from '@tanstack/react-query';

// Define DateRange type since it's not exported
interface DateRange {
  from: Date | null;
  to: Date | null;
}

// Mock the dependencies
vi.mock('@mass/shared/components/charts/bar', () => ({
  BarDataChart: ({ data, granularity, unit, threeTime }: { 
    data: any[]; 
    granularity: string; 
    unit: string; 
    threeTime: boolean 
  }) => (
    <div data-testid="bar-chart">
      <div>Granularity: {granularity}</div>
      <div>Unit: {unit}</div>
      <div>3-Time: {threeTime ? 'true' : 'false'}</div>
      <div>Data Points: {data ? data.length : 0}</div>
    </div>
  )
}));

vi.mock('@mass/shared/components/charts/table', () => ({
  TableChart: ({ data, granularity, unit }: { 
    data: any[]; 
    granularity: string; 
    unit: string 
  }) => (
    <div data-testid="table-chart">
      <div>Granularity: {granularity}</div>
      <div>Unit: {unit}</div>
      <div>Data Points: {data ? data.length : 0}</div>
    </div>
  )
}));

vi.mock('@mass/shared/components/charts/utils', () => ({
  getDateFunctionsForGranularity: () => ({
    start: (date: Date) => date,
    add: (date: Date, amount: number) => {
      const newDate = new Date(date);
      newDate.setDate(newDate.getDate() + amount);
      return newDate;
    },
    label: (date: Date) => date.toISOString().split('T')[0]
  })
}));

vi.mock('@mass/shared/hooks/use-date-locale', () => ({
  useDateLocale: () => 'en-US'
}));

vi.mock('@mass/shared/components/ui/date-picker', () => ({
  DatePicker: ({ date, setDate }: { date: Date | undefined; setDate: (date: Date) => void }) => (
    <div data-testid="date-picker">
      <input 
        type="date"
        value={date ? date.toISOString().split('T')[0] : ''}
        onChange={(e) => setDate(new Date(e.target.value))}
        data-testid="date-input"
      />
      <span>Selected: {date ? date.toISOString().split('T')[0] : 'None'}</span>
    </div>
  )
}));

vi.mock('@mass/shared/hooks/use-global-settings', () => ({
  useGlobalSettings: () => ({
    data: {
      value: {
        dayPrev: '10',
        dayMax: '730',
        monthPrev: '6',
        monthMax: '24',
        yearMax: '2'
      }
    }
  })
}));

// Mock the modal hook
const mockOpenModal = vi.fn();
vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: () => ({
    open: mockOpenModal
  })
}));

// Mock the data query hook
vi.mock('./data/queries', () => ({
  useSubscriptionUsageData: vi.fn()
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'usage.data_title': 'Usage Data',
        'usage.range_placeholder': 'Select Range',
        'usage.three_time_placeholder': 'Select Time Format',
        'usage.single_time': 'Single Time',
        'usage.three_time': 'Three Time',
        'usage.chart_type_bar': 'Bar Chart',
        'usage.chart_type_table': 'Table Chart',
        'usage.query_clear_button': 'Clear',
        'usage.query_export_button': 'Export',
        'usage.query_title': 'Usage Query',
        'usage.query_apply_button': 'Apply',
        'usage.daterange_title': 'Date Range',
        'usage.daterange_desc': 'Select the date range for usage data',
        'usage.daterange_label': 'Period',
        'usage.range_day': 'Daily',
        'usage.range_month': 'Monthly',
        'usage.range_year': 'Yearly',
        'usage.pick_date': 'Select Date',
        'usage.range_month_placeholder': 'Select Month',
        'usage.compare_title': 'Compare',
        'usage.compare_desc': 'Compare with other data',
        'usage.compare.query.day': 'Previous 10 days',
        'usage.compare.query.month': 'Previous 6 months',
        'usage.compare.average': 'Average',
        'usage.compare.similar-consumer-city': 'Similar City Consumers',
        'usage.compare.similar-consumer-district': 'Similar District Consumers',
        'usage.query_invalid_date_error': 'Invalid date',
        'usage.export.title': 'Export Usage Data'
      };
      return translations[key] || key;
    }
  })
}));

vi.mock('@/services/api', () => ({
  __esModule: true,
  default: vi.fn()
}));

// Create a wrapper for testing components that use React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    }
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('DataTab Component', () => {
  // Helper function to create a complete mock for UseMutationResult
  const createMutationResultMock = (data: any) => ({
    data,
    mutate: vi.fn(),
    reset: vi.fn(),
    error: null,
    variables: { startDate: undefined, endDate: undefined, granularity: 'month', compareTo: [] },
    isError: false,
    isIdle: false,
    isLoading: false,
    isPending: false,
    isSuccess: true,
    status: 'success',
    failureCount: 0,
    failureReason: null,
    isPaused: false,
    context: undefined,
    submittedAt: 0
  } as unknown as UseMutationResult<any, Error, { 
    startDate: Date | undefined; 
    endDate: Date | undefined; 
    granularity: "hour" | "day" | "month" | "year"; 
    compareTo: string[]; 
  }, unknown>);
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the usage data when data is available', () => {
    vi.mocked(useSubscriptionUsageData).mockReturnValue({
      data: {
        usageData: [
          {
            date: '2023-01-01',
            data: { T1: 100, T2: null, T3: null }
          },
          {
            date: '2023-02-01',
            data: { T1: 150, T2: null, T3: null }
          }
        ],
        requestedGranularity: 'month',
        requestedCompareTo: [],
        requestedStartDate: '2023-01-01',
        requestedEndDate: '2023-03-01',
        unit: 'kWh',
        compare: {}
      },
      mutate: vi.fn(),
      reset: vi.fn(),
      // Add all required properties for UseMutationResult
      error: null,
      variables: { startDate: undefined, endDate: undefined, granularity: 'month', compareTo: [] },
      isError: false,
      isIdle: false,
      isLoading: false,
      isPending: false,
      isSuccess: true,
      status: 'success',
      failureCount: 0,
      failureReason: null,
      isPaused: false,
      context: undefined,
      submittedAt: 0
    } as unknown as UseMutationResult<any, Error, { 
      startDate: Date | undefined; 
      endDate: Date | undefined; 
      granularity: "hour" | "day" | "month" | "year"; 
      compareTo: string[]; 
    }, unknown>);

    render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
    
    // Check if title is rendered
    expect(screen.getByText('Usage Data')).toBeInTheDocument();
    
    // Chart type selector should be present
    expect(screen.getByText('Bar Chart')).toBeInTheDocument();
    
    // Bar chart should be rendered by default
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    
    // Check data details in chart
    expect(screen.getByText('Granularity: month')).toBeInTheDocument();
    expect(screen.getByText('Unit: kWh')).toBeInTheDocument();
    expect(screen.getByText('Data Points: 2')).toBeInTheDocument();
  });

  it('switches chart type from bar to table', () => {
    vi.mocked(useSubscriptionUsageData).mockReturnValue({
      data: {
        usageData: [
          {
            date: '2023-01-01',
            data: { T1: 100, T2: null, T3: null }
          }
        ],
        requestedGranularity: 'month',
        requestedCompareTo: [],
        requestedStartDate: '2023-01-01',
        requestedEndDate: '2023-03-01',
        unit: 'kWh',
        compare: {}
      },
      mutate: vi.fn(),
      reset: vi.fn(),
      // Add all required properties
      error: null,
      variables: { startDate: undefined, endDate: undefined, granularity: 'month', compareTo: [] },
      isError: false,
      isIdle: false,
      isLoading: false,
      isPending: false,
      isSuccess: true,
      status: 'success',
      failureCount: 0,
      failureReason: null,
      isPaused: false,
      context: undefined,
      submittedAt: 0
    } as unknown as UseMutationResult<any, Error, { 
      startDate: Date | undefined; 
      endDate: Date | undefined; 
      granularity: "hour" | "day" | "month" | "year"; 
      compareTo: string[]; 
    }, unknown>);

    render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
    
    // Find the chart type select
    const chartTypeSelect = screen.getByText('Bar Chart').closest('button');
    if (chartTypeSelect) {
      fireEvent.click(chartTypeSelect);
      
      // Find and click the table option
      const tableOption = screen.getByRole('option', { name: 'Table Chart' });
      fireEvent.click(tableOption);
      
      // Table chart should now be visible
      expect(screen.getByTestId('table-chart')).toBeInTheDocument();
      expect(screen.queryByTestId('bar-chart')).not.toBeInTheDocument();
    }
  });

  it('handles export button click', () => {
    // Create a mock date to ensure consistent test behavior
    const mockDate = new Date('2023-03-15T12:00:00Z');
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);

    vi.mocked(useSubscriptionUsageData).mockReturnValue({
      data: {
        usageData: [
          {
            date: '2023-01-01',
            data: { T1: 100, T2: null, T3: null }
          }
        ],
        requestedGranularity: 'month',
        requestedCompareTo: [],
        requestedStartDate: '2023-01-01',
        requestedEndDate: '2023-03-01',
        unit: 'kWh',
        compare: {}
      },
      mutate: vi.fn(),
      reset: vi.fn(),
      // Add all required properties
      error: null,
      variables: { startDate: undefined, endDate: undefined, granularity: 'month', compareTo: [] },
      isError: false,
      isIdle: false,
      isLoading: false,
      isPending: false,
      isSuccess: true,
      status: 'success',
      failureCount: 0,
      failureReason: null,
      isPaused: false,
      context: undefined,
      submittedAt: 0
    } as unknown as UseMutationResult<any, Error, { 
      startDate: Date | undefined; 
      endDate: Date | undefined; 
      granularity: "hour" | "day" | "month" | "year"; 
      compareTo: string[]; 
    }, unknown>);

    render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
    
    // Find and click the export button
    const exportButton = screen.getByText('Export');
    fireEvent.click(exportButton);
    
    // Modal should be opened - update expectations to match what's actually passed
    expect(mockOpenModal).toHaveBeenCalledWith(expect.anything(), expect.objectContaining({
      title: 'Export Usage Data',
      subscriptionId: '123',
      granularity: 'month',
      compareTo: []
    }));

    vi.useRealTimers(); // Restore real timers
  });

  it('resets the data when clear button is clicked', () => {
    const resetMock = vi.fn();
    vi.mocked(useSubscriptionUsageData).mockReturnValue({
      data: {
        usageData: [
          {
            date: '2023-01-01',
            data: { T1: 100, T2: null, T3: null }
          }
        ],
        requestedGranularity: 'month',
        requestedCompareTo: [],
        requestedStartDate: '2023-01-01',
        requestedEndDate: '2023-03-01',
        unit: 'kWh',
        compare: {}
      },
      mutate: vi.fn(),
      reset: resetMock,
      // Add all required properties
      error: null,
      variables: { startDate: undefined, endDate: undefined, granularity: 'month', compareTo: [] },
      isError: false,
      isIdle: false,
      isLoading: false,
      isPending: false,
      isSuccess: true,
      status: 'success',
      failureCount: 0,
      failureReason: null,
      isPaused: false,
      context: undefined,
      submittedAt: 0
    } as unknown as UseMutationResult<any, Error, { 
      startDate: Date | undefined; 
      endDate: Date | undefined; 
      granularity: "hour" | "day" | "month" | "year"; 
      compareTo: string[]; 
    }, unknown>);

    render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
    
    // Find and click the clear button
    const clearButton = screen.getByText('Clear');
    fireEvent.click(clearButton);
    
    // Reset should be called
    expect(resetMock).toHaveBeenCalled();
  });

  it('enables three-time option if data has T1, T2, T3', () => {
    vi.mocked(useSubscriptionUsageData).mockReturnValue(createMutationResultMock({
      usageData: [
        {
          date: '2023-01-01',
          data: { T1: 100, T2: 200, T3: 300 }
        }
      ],
      requestedGranularity: 'month',
      requestedCompareTo: [],
      requestedStartDate: '2023-01-01',
      requestedEndDate: '2023-03-01',
      unit: 'kWh',
      compare: {}
    }));

    render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
    
    // Three-time selector should be visible
    expect(screen.getByText('Single Time')).toBeInTheDocument();
    
    // Change to three-time format
    const timeFormatSelect = screen.getByText('Single Time').closest('button');
    if (!timeFormatSelect) throw new Error('Time format select button not found');
    fireEvent.click(timeFormatSelect);
    
    const threeTimeOption = screen.getByRole('option', { name: 'Three Time' });
    fireEvent.click(threeTimeOption);
    
    // Three-time should be enabled
    expect(screen.getByText('3-Time: true')).toBeInTheDocument();
  });

  describe('query form', () => {
    beforeEach(() => {
      vi.mocked(useSubscriptionUsageData).mockReturnValue(createMutationResultMock(null));
    });

    it('renders the query form when no data is available', () => {
      render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
      
      // Query form title should be visible
      expect(screen.getByText('Usage Query')).toBeInTheDocument();
      
      // Form elements should be present
      expect(screen.getByText('Date Range')).toBeInTheDocument();
      expect(screen.getByText('Select Date')).toBeInTheDocument();
      expect(screen.getByText('Apply')).toBeInTheDocument();
    });

    it('changes granularity selection', () => {
      render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
      
      // Find the granularity select
      const granularitySelect = screen.getByText('Monthly').closest('button');
      if (!granularitySelect) throw new Error('Granularity select button not found');
      fireEvent.click(granularitySelect);
      
      // Select daily option
      const dailyOption = screen.getByRole('option', { name: 'Daily' });
      fireEvent.click(dailyOption);
      
      // Date picker should appear for daily selection
      expect(screen.getByTestId('date-picker')).toBeInTheDocument();
    });

    it('validates date before submitting query', () => {
      const mutateMock = vi.fn();
      vi.mocked(useSubscriptionUsageData).mockReturnValue(createMutationResultMock(null));

      render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
      
      // Click apply without selecting date
      const applyButton = screen.getByText('Apply');
      fireEvent.click(applyButton);
      
      // Error message should appear
      expect(screen.getByText('Invalid date')).toBeInTheDocument();
      
      // Mutate should not be called
      expect(mutateMock).not.toHaveBeenCalled();
    });

    it('submits query with valid date', () => {
      const mutateMock = vi.fn();
      vi.mocked(useSubscriptionUsageData).mockReturnValue({
        ...createMutationResultMock(null),
        mutate: mutateMock
      });

      render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
      
      // Select a valid past date (monthly)
      const monthSelect = screen.getByText('Select Month').closest('button');
      if (!monthSelect) throw new Error('Month select button not found');
      fireEvent.click(monthSelect);
      
      // Select first month option
      const firstOption = screen.getAllByRole('option')[0];
      fireEvent.click(firstOption);
      
      // Click apply
      const applyButton = screen.getByText('Apply');
      fireEvent.click(applyButton);
      
      // Mutate should be called
      expect(mutateMock).toHaveBeenCalled();
    });

    it('handles comparison selection', async () => {
      render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
      
      // Check compare section is present
      expect(screen.getByText('Compare')).toBeInTheDocument();
      
      // Find the comparison switch
      const firstSwitch = screen.getAllByRole('switch')[0];
      
      // Toggle the switch
      fireEvent.click(firstSwitch);
      
      // Wait for state update
      await waitFor(() => {
        expect(firstSwitch).toBeChecked();
      });
    });

    it('clears form when reset button is clicked', () => {
      const resetMock = vi.fn();
      vi.mocked(useSubscriptionUsageData).mockReturnValue({
        ...createMutationResultMock(null),
        reset: resetMock
      });

      render(<DataTab subscriptionId="123" />, { wrapper: createWrapper() });
      
      // Find and click the clear button
      const clearButton = screen.getByText('Clear');
      fireEvent.click(clearButton);
      
      // Reset should be called
      expect(resetMock).toHaveBeenCalled();
    });
  });
});