import { describe, it, expect, vi, beforeEach, afterAll } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import * as React from 'react';
import {
  subscriptionKeys,
  useSubscriptions,
  useSubscriptionDetail,
  useSubscriptionUsageData,
  useRegions,
  useRegionById,
  useRegionsPaginated,
  useCreateSubscription,
  useUpdateSubscription,
  useDeleteSubscription,
  useExportSubscriptionUsageMutation,
  useExportSubscriptionOutageMutation,
  useUpdateNotificationSettings,
} from './queries';
import { subscriptionService } from '../../../services/api/subscriptions';

// Mock the subscription service
vi.mock('@/services/api/subscriptions', () => ({
  subscriptionService: {
    getAll: vi.fn(),
    getById: vi.fn(),
    getUsageData: vi.fn(),
    getRegions: vi.fn(),
    getRegionById: vi.fn(),
    getRegionsPaginated: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    exportUsageData: vi.fn(),
    exportOutageData: vi.fn(),
    updateNotificationSettings: vi.fn(),
  },
}));

// Mock translation
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en'
    }
  }),
}));

// Mock console.error
const originalConsoleError = console.error;
beforeEach(() => {
  console.error = vi.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('subscriptionKeys generation', () => {
  it('generates list key correctly', () => {
    const key = subscriptionKeys.list({ pageNumber: 1, pageSize: 10 });
    expect(key[2]).toMatchObject({ pageNumber: 1 });
  });

  it('generates detail key correctly', () => {
    const key = subscriptionKeys.detail("123");
    expect(key).toEqual(["subscriptions", "detail", "123"]);
  });
  
  it('generates usage data key correctly', () => {
    const startDate = new Date('2023-01-01');
    const endDate = new Date('2023-01-31');
    const key = subscriptionKeys.usageData("123", startDate, endDate, "day", ["456"]);
    expect(key[0]).toEqual("subscriptions");
    expect(key[1]).toEqual("usage");
    expect(key[2]).toEqual("123");
    expect(key[3]).toEqual(startDate.toISOString());
    expect(key[4]).toEqual(endDate.toISOString());
    expect(key[5]).toEqual("day");
    expect(key[6]).toEqual(["456"]);
  });
  
  it('handles null dates in usage data key', () => {
    const key = subscriptionKeys.usageData("123", null, null, "day", []);
    expect(key[0]).toEqual("subscriptions");
    expect(key[1]).toEqual("usage");
    expect(key[2]).toEqual("123");
    expect(key[3]).toBeNull();
    expect(key[4]).toBeNull();
    expect(key[5]).toEqual("day");
    expect(key[6]).toEqual([]);
  });
  
  it('generates regions key correctly', () => {
    const key = subscriptionKeys.regions();
    expect(key).toEqual(["regions"]);
  });
  
  it('generates regionById key correctly', () => {
    const key = subscriptionKeys.regionById("r1");
    expect(key).toEqual(["regions", "r1"]);
  });
  
  it('generates regionsPaginated key correctly', () => {
    const key = subscriptionKeys.regionsPaginated(1, "search");
    expect(key).toEqual(["regions", "paginated", 1, "search"]);
  });
  
  it('handles empty search in regionsPaginated key', () => {
    const key = subscriptionKeys.regionsPaginated(1);
    expect(key).toEqual(["regions", "paginated", 1, ""]);
  });
});

describe('Subscription Queries', () => {
  let queryClient: QueryClient;
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.resetAllMocks();
  });

  describe('useSubscriptions', () => {
    it('fetches subscriptions successfully', async () => {
      const mockData = {
        content: [{
          id: '1', 
          name: 'Test Subscription',
          createdAt: '2023-01-01T00:00:00Z',
          individual: true,
          installationId: 'inst-1',
          key: 'test-key',
          personIdentifier: '12345678901',
          regionId: 'region-1',
          startDate: '2023-01-01',
          type: 'individual',
          userId: 'user-1'
        }],
        totalElements: 1,
        pageSize: 10,
        number: 1,
        totalPages: 1,
        pageable: {
          sort: { sorted: true, empty: false, unsorted: false },
          pageNumber: 1,
          pageSize: 10,
          offset: 0,
          paged: true,
          unpaged: false
        },
        last: true,
        first: true,
        size: 10,
        sort: { sorted: true, empty: false, unsorted: false },
        empty: false,
        numberOfElements: 1,
      };
      
      vi.mocked(subscriptionService.getAll).mockResolvedValue(mockData);

      const { result } = renderHook(() => useSubscriptions({}), { wrapper });

      await waitFor(() => {
        expect(result.current.data?.pages[0]).toEqual(mockData);
      });
      
      expect(subscriptionService.getAll).toHaveBeenCalledTimes(1);
      expect(subscriptionService.getAll).toHaveBeenCalledWith(expect.objectContaining({ 
        pageNumber: 1, 
        pageSize: 10,
        orderBy: 'createdAt:asc' 
      }));
    });
    
    it('handles error when fetching subscriptions', async () => {
      const error = new Error('Failed to fetch subscriptions');
      vi.mocked(subscriptionService.getAll).mockRejectedValue(error);
      
      const { result } = renderHook(() => useSubscriptions({}), { wrapper });
      
      await waitFor(() => {
        expect(result.current.error).toBeDefined();
      });
      
      expect(subscriptionService.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('useSubscriptionDetail', () => {
    it('fetches subscription detail successfully', async () => {
      const mockData = { 
        id: '1', 
        name: 'Test Subscription',
        createdAt: '2023-01-01T00:00:00Z',
        individual: true,
        installationId: 'inst-1',
        key: 'test-key',
        personIdentifier: '12345678901',
        regionId: 'region-1',
        startDate: '2023-01-01',
        type: 'individual',
        userId: 'user-1',
        distributionCompany: 'company1',
        taxNumber: '12345678901',
        unexpectedUsageThreshold: 80,
        userDefinedLimit: 1000
      };
      vi.mocked(subscriptionService.getById).mockResolvedValue(mockData);

      const { result } = renderHook(() => useSubscriptionDetail('1'), { wrapper });

      await waitFor(() => {
        expect(result.current.data).toEqual(mockData);
      });
      
      expect(subscriptionService.getById).toHaveBeenCalledWith('1');
    });
    
    it('returns undefined when subscription id is empty', async () => {
      const { result } = renderHook(() => useSubscriptionDetail(''), { wrapper });
      
      await waitFor(() => {
        expect(result.current.data).toBeUndefined();
      });
      
      expect(subscriptionService.getById).not.toHaveBeenCalled();
    });
  });

  describe('useSubscriptionUsageData', () => {
    it('fetches subscription usage data successfully', async () => {
      const mockData = {
        usageData: [
          { timeframe: '2023-01-01', usage: 100, cost: 50 }
        ],
        totalUsage: 100,
        totalCost: 50,
        requestedGranularity: 'day',
        requestedCompareTo: [],
        requestedStartDate: new Date('2023-01-01'),
        requestedEndDate: new Date('2023-01-31')
      };
      
      vi.mocked(subscriptionService.getUsageData).mockResolvedValue(mockData);

      const { result } = renderHook(() => useSubscriptionUsageData('123'), { wrapper });

      const params = {
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-01-31'),
        granularity: 'day' as const,
        compareTo: []
      };

      await result.current.mutateAsync(params);

      expect(subscriptionService.getUsageData).toHaveBeenCalledWith(
        '123',
        params.startDate,
        params.endDate,
        params.granularity,
        params.compareTo
      );
    });

    it('handles error when fetching usage data', async () => {
      const error = new Error('Failed to fetch usage data');
      vi.mocked(subscriptionService.getUsageData).mockRejectedValue(error);
      
      const { result } = renderHook(() => useSubscriptionUsageData('123'), { wrapper });
      
      const params = {
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-01-31'),
        granularity: 'day' as const,
        compareTo: []
      };

      await expect(result.current.mutateAsync(params)).rejects.toThrow();
    });
  });

  describe('useRegions', () => {
    it('fetches regions successfully', async () => {
      const mockData = [{ id: 'r1', name: 'Region 1' }];
      vi.mocked(subscriptionService.getRegions).mockResolvedValue(mockData);

      const { result } = renderHook(() => useRegions(), { wrapper });

      await waitFor(() => {
        expect(result.current.data).toEqual(mockData);
      });
      
      expect(subscriptionService.getRegions).toHaveBeenCalled();
    });
  });

  describe('useRegionById', () => {
    it('fetches region by id successfully', async () => {
      const mockData = { id: 'r1', name: 'Region 1' };
      vi.mocked(subscriptionService.getRegionById).mockResolvedValue(mockData);

      const { result } = renderHook(() => useRegionById('r1'), { wrapper });

      await waitFor(() => {
        expect(result.current.data).toEqual(mockData);
      });
      
      expect(subscriptionService.getRegionById).toHaveBeenCalledWith('r1');
    });
    
    it('returns undefined when region id is empty', async () => {
      const { result } = renderHook(() => useRegionById(''), { wrapper });
      
      await waitFor(() => {
        expect(result.current.data).toBeUndefined();
      });
      
      expect(subscriptionService.getRegionById).not.toHaveBeenCalled();
    });
  });

  describe('useCreateSubscription', () => {
    it('creates subscription successfully', async () => {
      const mockData = {
        id: '1', 
        name: 'Test Subscription',
        createdAt: '2023-01-01T00:00:00Z',
        individual: true,
        installationId: 'inst-1',
        key: 'test-key',
        personIdentifier: '12345678901',
        regionId: 'region-1',
        startDate: '2023-01-01',
        type: 'individual',
        userId: 'user-1'
      };
      vi.mocked(subscriptionService.create).mockResolvedValue(mockData);

      const { result } = renderHook(() => useCreateSubscription(), { wrapper });

      const mutationInput = { 
        name: 'Test Subscription', 
        type: 'individual' as const,
        distributionCompany: 'company1',
        installationId: 'inst1',
        facilityType: 'residential'
      };

      result.current.mutate(mutationInput);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
        expect(result.current.data).toEqual(mockData);
      });
      
      expect(subscriptionService.create).toHaveBeenCalledWith(mutationInput);
    });
  });

  describe('useUpdateSubscription', () => {
    it('updates subscription successfully', async () => {
      const mockData = {
        id: '1', 
        name: 'Updated Subscription',
        createdAt: '2023-01-01T00:00:00Z',
        individual: true,
        installationId: 'inst-1',
        key: 'test-key',
        personIdentifier: '12345678901',
        regionId: 'region-1',
        startDate: '2023-01-01',
        type: 'individual',
        userId: 'user-1'
      };
      vi.mocked(subscriptionService.update).mockResolvedValue(mockData);

      const { result } = renderHook(() => useUpdateSubscription(), { wrapper });

      const updateInput = { 
        id: '1', 
        data: { name: 'Updated Subscription' } 
      };

      result.current.mutate(updateInput);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
        expect(result.current.data).toEqual(mockData);
      });
      
      expect(subscriptionService.update).toHaveBeenCalledWith('1', updateInput.data);
    });
  });

  describe('useDeleteSubscription', () => {
    it('deletes subscription successfully', async () => {
      vi.mocked(subscriptionService.delete).mockResolvedValue(undefined);

      const { result } = renderHook(() => useDeleteSubscription(), { wrapper });

      result.current.mutate('1');

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });
      
      expect(subscriptionService.delete).toHaveBeenCalledWith('1');
    });
  });

  describe('useExportSubscriptionUsageMutation', () => {
    it('exports subscription usage successfully', async () => {
      const mockData = { 
        files: [{ url: 'https://example.com/file.csv', fileName: 'export.csv' }] 
      };
      vi.mocked(subscriptionService.exportUsageData).mockResolvedValue(mockData);

      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      
      const { result } = renderHook(
        () => useExportSubscriptionUsageMutation('1', startDate, endDate, 'day', []), 
        { wrapper }
      );

      const fileType = 'csv';
      await result.current.mutateAsync(fileType);

      expect(subscriptionService.exportUsageData).toHaveBeenCalledWith(
        '1', 
        startDate, 
        endDate, 
        'day', 
        [],
        fileType,
        'en'
      );
    });
  });

  describe('useExportSubscriptionOutageMutation', () => {
    it('exports subscription outage data successfully', async () => {
      const mockData = { 
        files: [{ url: 'https://example.com/file.pdf', fileName: 'outages.pdf' }] 
      };
      vi.mocked(subscriptionService.exportOutageData).mockResolvedValue(mockData);

      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      
      const { result } = renderHook(
        () => useExportSubscriptionOutageMutation('1', startDate, endDate), 
        { wrapper }
      );

      const fileType = 'pdf';
      await result.current.mutateAsync(fileType);

      expect(subscriptionService.exportOutageData).toHaveBeenCalledWith(
        '1', 
        startDate,
        endDate, 
        fileType,
        'en'
      );
    });
  });

  describe('useUpdateNotificationSettings', () => {
    it('updates notification settings successfully', async () => {
      vi.mocked(subscriptionService.updateNotificationSettings).mockResolvedValue(undefined);

      const { result } = renderHook(() => useUpdateNotificationSettings('123'), { wrapper });

      const notificationSettings = { 
        unexpectedUsageThreshold: 80, 
        userDefinedLimit: 1000 
      };

      result.current.mutate(notificationSettings);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });
      
      expect(subscriptionService.updateNotificationSettings).toHaveBeenCalledWith('123', notificationSettings);
    });

    it('handles error when updating notification settings', async () => {
      const error = new Error('Failed to update notification settings');
      vi.mocked(subscriptionService.updateNotificationSettings).mockRejectedValue(error);
      
      const { result } = renderHook(() => useUpdateNotificationSettings('123'), { wrapper });
      
      const notificationSettings = { 
        unexpectedUsageThreshold: 80, 
        userDefinedLimit: 1000 
      };

      result.current.mutate(notificationSettings);
      
      await waitFor(() => {
        expect(result.current.error).toBeDefined();
      });
      
      expect(console.error).toHaveBeenCalledWith('Query error:', error);
    });
  });
});
