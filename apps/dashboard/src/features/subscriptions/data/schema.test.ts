import { describe, it, expect } from 'vitest';
import { createSubscriptionSchema, updateSubscriptionSchema } from './schema';

describe('createSubscriptionSchema', () => {
  it('validates valid individual subscription', () => {
    const data = {
      name: "Test Subscription",
      type: "individual",
      distributionCompany: "Test Company",
      installationId: "inst1",
    };
    const result = createSubscriptionSchema.safeParse(data);
    expect(result.success).toBeTruthy();
  });

  it('fails for corporate subscription missing taxNumber', () => {
    const data = {
      name: "Corporate Subscription",
      type: "corporate",
      distributionCompany: "Corp Company",
      installationId: "inst2",
    };
    const result = createSubscriptionSchema.safeParse(data);
    expect(result.success).toBeFalsy();
  });

  it('validates corporate subscription with valid taxNumber', () => {
    const data = {
      name: "Corporate Subscription",
      type: "corporate",
      distributionCompany: "Corp Company",
      taxNumber: 12345678910,
      installationId: "inst2",
    };
    const result = createSubscriptionSchema.safeParse(data);
    expect(result.success).toBeTruthy();
  });
});

describe('updateSubscriptionSchema', () => {
  it('picks only the name field', () => {
    const data = { name: "Updated Name", distributionCompany: "Extra" };
    const result = updateSubscriptionSchema.safeParse(data);
    expect(result.success).toBeTruthy();
    if(result.success) {
      expect(result.data).toEqual({ name: "Updated Name" });
    }
  });
});
