/**
 * @vitest-environment jsdom
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import React from 'react';
import Subscription from './index';
import { useSubscriptions, useRegions, useDeleteSubscription } from './data/queries';
import { useModal } from '@mass/shared/components/organisms/modal/provider';
import { useTableFilter } from '@mass/shared/hooks/use-table-filter';
import { useDebounce } from '@mass/shared/hooks/use-debounce';
import { useDisabled, useIsDisabled } from '@/utils/use-disabled';
import { Dispatch, SetStateAction } from 'react';

// Define DateRange type to match what's expected by useTableFilter
interface DateRange {
  from: Date | null;
  to: Date | null;
}

// Mock all dependencies
vi.mock('./data/queries', () => ({
  useSubscriptions: vi.fn(),
  useRegions: vi.fn(),
  useDeleteSubscription: vi.fn()
}));

vi.mock('@mass/shared/components/organisms/modal/provider', () => ({
  useModal: vi.fn()
}));

vi.mock('@mass/shared/hooks/use-table-filter', () => ({
  useTableFilter: vi.fn()
}));

vi.mock('@mass/shared/hooks/use-debounce', () => ({
  useDebounce: vi.fn()
}));

vi.mock('@/utils/use-disabled', () => ({
  useDisabled: vi.fn(),
  useIsDisabled: vi.fn()
}));

// Fix mock for @tanstack/react-router
const mockNavigate = vi.fn();
vi.mock('@tanstack/react-router', () => {
  return {
    useNavigate: () => mockNavigate
  };
});

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      const translations: Record<string, string> = {
        'title': 'Subscriptions',
        'description': 'Manage your electricity subscriptions',
        'add_subscription': 'Add Subscription',
        'subscriptions_count': '{count} Subscriptions',
        'no_subscription_title': 'No Subscriptions',
        'no_subscription_filter_title': 'No Matching Subscriptions',
        'no_subscription_description': 'Create your first subscription',
        'no_subscription_filter_description': 'Try adjusting your filters',
        'common:common.filter': 'Filter',
        'common:common.add_new': 'Add New',
        'common:common.delete_all': 'Delete Selected',
        'common:common.clear_filters': 'Clear Filters',
        'common:common.search_placeholder': 'Search...'
      };

      // Properly replace variables in translation strings
      if (params && key === 'subscriptions_count') {
        return translations[key].replace('{count}', params.count);
      }

      // Fix for common:* namespace keys
      if (key.includes(':')) {
        const parts = key.split(':');
        if (parts.length === 2 && translations[key]) {
          return translations[key];
        }
        // If it's not found with full key, return the last part
        return parts[1];
      }

      return translations[key] || key;
    }
  })
}));

vi.mock('@mass/shared/components/atoms/empty', () => ({
  default: ({ title, description, children }: any) => (
    <div data-testid="empty-state">
      <h3>{title}</h3>
      <p>{description}</p>
      <div>{children}</div>
    </div>
  )
}));

vi.mock('@mass/shared/components/atoms/Iconify', () => ({
  default: ({ name }: any) => <span data-testid={`icon-${name}`}>{name}</span>
}));

vi.mock('@mass/shared/components/ui/badge', () => ({
  Badge: ({ children, color, variant, className }: any) => (
    <span 
      data-testid="badge" 
      data-color={color} 
      data-variant={variant}
      className={className}
    >
      {children}
    </span>
  )
}));

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, onClick, variant, className }: any) => (
    <button 
      onClick={onClick} 
      data-variant={variant}
      className={className}
      data-testid={`button-${variant || 'default'}`}
    >
      {children}
    </button>
  )
}));

vi.mock('@mass/shared/components/molecules/table-transition', () => ({
  AnimatedTransition: ({ children, loading, skeleton }: any) => (
    <div data-testid="animated-transition" data-loading={loading}>
      {loading ? skeleton : children}
    </div>
  )
}));

vi.mock('@mass/shared/components/organisms/layout/header', () => ({
  Header: ({ title, sidebarData, subContent }: any) => (
    <div data-testid="header">
      <div>{title}</div>
      <div data-testid="header-subcontent">{subContent}</div>
    </div>
  )
}));

vi.mock('@mass/shared/components/organisms/layout/main', () => ({
  Main: ({ children }: any) => <main data-testid="main">{children}</main>
}));

vi.mock('@mass/shared/components/organisms/table/index', () => ({
  DataTable: ({ 
    data, 
    onRowClick, 
    rowSelection, 
    onRowSelectionChange,
    onSortingChange,
    onPageChange,
    empty,
    tableOptions
  }: any) => (
    <div data-testid="data-table">
      <div data-testid="row-count">{data?.length || 0}</div>
      <div data-testid="table-options">{JSON.stringify(tableOptions)}</div>
      <button 
        data-testid="select-row-button" 
        onClick={() => onRowSelectionChange({ '1': true })}
      >
        Select Row
      </button>
      {data && data.length > 0 ? (
        <div>
          {data.map((item: any) => (
            <div 
              key={item.id} 
              data-testid={`row-${item.id}`}
              onClick={() => onRowClick(item)}
            >
              {item.name}
            </div>
          ))}
        </div>
      ) : (
        empty
      )}
      <button 
        data-testid="change-sort" 
        onClick={() => onSortingChange([{ id: 'name', desc: false }])}
      >
        Change Sort
      </button>
      <button 
        data-testid="change-page" 
        onClick={() => onPageChange(2)}
      >
        Next Page
      </button>
    </div>
  )
}));

vi.mock('./components/skeleton-loader', () => ({
  SubscriptionsTableSkeleton: () => <div data-testid="skeleton-loader">Loading...</div>
}));

describe('Subscription component', () => {
  const mockOpenModal = vi.fn();
  const mockMutate = vi.fn();
  const mockSetSearchQuery = vi.fn();
  const mockSetDateRange = vi.fn();
  const mockSetIsCalendarOpen = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock values
    vi.mocked(useSubscriptions).mockReturnValue({
      data: {
        pages: [{
          content: [
            { id: '1', name: 'Subscription 1' },
            { id: '2', name: 'Subscription 2' }
          ],
          totalPages: 2,
          totalElements: 2,
          number: 0
        }],
        pageParams: [1]
      },
      isLoading: false,
      error: null
    } as any);
    
    vi.mocked(useRegions).mockReturnValue({
      data: [
        { id: 'region1', name: 'Region 1' },
        { id: 'region2', name: 'Region 2' }
      ],
      isLoading: false
    } as any);
    
    vi.mocked(useDeleteSubscription).mockReturnValue({
      mutate: mockMutate,
      isPending: false
    } as any);
    
    vi.mocked(useModal).mockReturnValue({
      open: mockOpenModal
    } as any);
    
    vi.mocked(useTableFilter).mockReturnValue({
      searchQuery: '',
      setSearchQuery: mockSetSearchQuery as Dispatch<SetStateAction<string>>,
      dateRange: { from: new Date(), to: new Date() },
      setDateRange: mockSetDateRange as Dispatch<SetStateAction<DateRange>>,
      isCalendarOpen: false,
      setIsCalendarOpen: mockSetIsCalendarOpen as Dispatch<SetStateAction<boolean>>,
      isDateSelected: false
    });
    
    vi.mocked(useDebounce).mockImplementation((value) => value);
    
    vi.mocked(useDisabled).mockImplementation(() => {});
    vi.mocked(useIsDisabled).mockReturnValue(false);
  });

  it('renders the component with header and subscription list', () => {
    render(<Subscription />);

    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByTestId('main')).toBeInTheDocument();
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
    expect(screen.getByTestId('row-count').textContent).toBe('2');
  });

  it('handles empty state when there are no subscriptions', () => {
    vi.mocked(useSubscriptions).mockReturnValue({
      data: {
        pages: [{
          content: [],
          totalPages: 0,
          totalElements: 0,
          number: 0
        }],
        pageParams: [1]
      },
      isLoading: false,
      error: null
    } as any);

    render(<Subscription />);
    
    // Empty state should be visible
    expect(screen.getByTestId('empty-state')).toBeInTheDocument();
    expect(screen.getByText('No Subscriptions')).toBeInTheDocument();
  });

  it('shows loading state when data is loading', () => {
    vi.mocked(useSubscriptions).mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null
    } as any);

    vi.mocked(useRegions).mockReturnValue({
      data: undefined,
      isLoading: true
    } as any);

    render(<Subscription />);
    
    expect(screen.getByTestId('animated-transition')).toHaveAttribute('data-loading', 'true');
    expect(screen.getByTestId('skeleton-loader')).toBeInTheDocument();
  });

  it('opens filter modal when filter button is clicked', () => {
    render(<Subscription />);
    
    // Use data-testid instead of text content
    const filterButton = screen.getByTestId('button-outline');
    fireEvent.click(filterButton);
    
    expect(mockOpenModal).toHaveBeenCalledWith(expect.anything(), expect.objectContaining({
      name: "filter-billing-modal"
    }));
  });

  it('opens create modal when add button is clicked', () => {
    render(<Subscription />);
    
    // Use data-testid instead of text content
    const addButton = screen.getByTestId('button-default');
    fireEvent.click(addButton);
    
    expect(mockOpenModal).toHaveBeenCalledWith(expect.anything(), expect.objectContaining({
      name: "create-billing-modal"
    }));
  });

  it('handles row selection and delete', () => {
    render(<Subscription />);
    
    // Select a row
    const selectButton = screen.getByTestId('select-row-button');
    fireEvent.click(selectButton);
    
    // Delete button should appear - use data-testid instead of text
    const deleteButton = screen.getByTestId('button-destructive');
    expect(deleteButton).toBeInTheDocument();
    fireEvent.click(deleteButton);
    
    // Delete modal should open
    expect(mockOpenModal).toHaveBeenCalledWith(expect.anything(), expect.objectContaining({
      name: "delete-billing-modal"
    }));
  });

  it('navigates to subscription detail when row is clicked', () => {
    render(<Subscription />);
    
    // Click on a row
    const row = screen.getByTestId('row-1');
    fireEvent.click(row);
    
    // Should navigate to subscription detail
    expect(mockNavigate).toHaveBeenCalledWith({
      to: '/subscriptions/1'
    });
  });

  it('changes page when pagination is used', async () => {
    render(<Subscription />);
    
    // Click next page button
    const pageButton = screen.getByTestId('change-page');
    fireEvent.click(pageButton);
    
    // Current page should be updated
    await waitFor(() => {
      const options = JSON.parse(screen.getByTestId('table-options').textContent || '{}');
      expect(options.state.pagination.pageIndex).toBe(1); // 0-based index
    });
  });

  it('changes sort when sorting is changed', async () => {
    render(<Subscription />);
    
    // Click sort button
    const sortButton = screen.getByTestId('change-sort');
    fireEvent.click(sortButton);
    
    // Sort should be updated
    await waitFor(() => {
      const options = JSON.parse(screen.getByTestId('table-options').textContent || '{}');
      expect(options.state.sorting[0].id).toBe('name');
      expect(options.state.sorting[0].desc).toBe(false);
    });
  });

  it('applies filter correctly', () => {
    vi.mocked(useTableFilter).mockReturnValue({
      searchQuery: 'test',
      setSearchQuery: mockSetSearchQuery as Dispatch<SetStateAction<string>>,
      dateRange: { from: new Date(), to: new Date() },
      setDateRange: mockSetDateRange as Dispatch<SetStateAction<DateRange>>,
      isCalendarOpen: false,
      setIsCalendarOpen: mockSetIsCalendarOpen as Dispatch<SetStateAction<boolean>>,
      isDateSelected: false
    });

    render(<Subscription />);
    
    expect(useSubscriptions).toHaveBeenCalledWith(
      expect.objectContaining({
        "filter:ct": { name: "test" }
      })
    );
  });

  it('hides add button when add is disabled', () => {
    vi.mocked(useIsDisabled).mockReturnValue(true);
    
    render(<Subscription />);
    
    // Add button should not be present
    const addButton = screen.queryByText('Add New');
    expect(addButton).not.toBeInTheDocument();
  });

  it('displays count badge when subscriptions exist', () => {
    render(<Subscription />);
    
    expect(screen.getByTestId('badge')).toBeInTheDocument();
    // Check for "2 Subscriptions" instead of {count} Subscriptions
    expect(screen.getByText('2 Subscriptions')).toBeInTheDocument();
  });
  
  it('handles empty filters state', () => {
    vi.mocked(useSubscriptions).mockReturnValue({
      data: {
        pages: [{
          content: [],
          totalPages: 0,
          totalElements: 0,
          number: 0
        }],
        pageParams: [1]
      },
      isLoading: false,
      error: null
    } as any);
    
    vi.mocked(useTableFilter).mockReturnValue({
      searchQuery: 'test',
      setSearchQuery: mockSetSearchQuery as Dispatch<SetStateAction<string>>,
      dateRange: { from: new Date(), to: new Date() },
      setDateRange: mockSetDateRange as Dispatch<SetStateAction<DateRange>>,
      isCalendarOpen: false,
      setIsCalendarOpen: mockSetIsCalendarOpen as Dispatch<SetStateAction<boolean>>,
      isDateSelected: false
    });
    
    vi.mocked(useDebounce).mockReturnValue('test');

    render(<Subscription />);
    
    // Empty state with filter message should be visible
    expect(screen.getByText('No Matching Subscriptions')).toBeInTheDocument();
    expect(screen.getByText('Try adjusting your filters')).toBeInTheDocument();
    
    // Clear filters button should be present
    const clearButton = screen.getByText('Clear Filters');
    expect(clearButton).toBeInTheDocument();
    
    // Click clear button
    fireEvent.click(clearButton);
    
    // Should clear search
    expect(mockSetSearchQuery).toHaveBeenCalledWith('');
  });
});
