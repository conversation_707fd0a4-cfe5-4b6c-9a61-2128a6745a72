import { sidebarData } from "@/constants/sidebar-data";
import { ApiQueryParams } from "@/services/types";
import Empty from "@mass/shared/components/atoms/empty";
import Iconify from "@mass/shared/components/atoms/Iconify";
import { AnimatedTransition } from "@mass/shared/components/molecules/table-transition";
import { Header } from "@mass/shared/components/organisms/layout/header";
import { Main } from "@mass/shared/components/organisms/layout/main";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import { DataTable } from "@mass/shared/components/organisms/table/index";
import { Badge } from "@mass/shared/components/ui/badge";
import { Button } from "@mass/shared/components/ui/button";
import { useDebounce } from "@mass/shared/hooks/use-debounce";
import { useTableFilter } from "@mass/shared/hooks/use-table-filter";
import { useNavigate } from "@tanstack/react-router";
import { SortingState } from "@tanstack/react-table";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { columns } from "./components/columns";
import SubscriptionBillingModal from "./components/modals/subscription-billing";
import Delete from "./components/modals/delete";
import Filter from "./components/modals/filter";
import { SubscriptionsTableSkeleton } from "./components/skeleton-loader";
import {
  useDeleteSubscription,
  useSubscriptions,
  useRegions,
} from "./data/queries";
import { useDisabled, useIsDisabled } from "@/utils/use-disabled";

export default function Subscription() {
  useDisabled(["disabled.subscriptions.view"], "/notifications");

  const addDisabled = useIsDisabled([
    "disabled.subscriptions.view",
    "disabled.subscriptions.add",
  ]);

  const navigate = useNavigate();
  const { open } = useModal();
  const { t } = useTranslation("subscriptions");
  const [rowSelection, setRowSelection] = useState({});

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "createdAt",
      desc: true,
    },
  ]);

  const searchInputRef = useRef<HTMLInputElement>(null);

  const orderByParam =
    sorting.length > 0
      ? sorting.map((s) => `${s.id}:${s.desc ? "desc" : "asc"}`).join(",")
      : undefined;

  const { searchQuery, setSearchQuery } = useTableFilter();

  const [filterType, setFilterType] = useState("all");
  const [filterFacilityType, setFilterFacilityType] = useState("all");
  const [filterDistributionCompany, setFilterDistributionCompany] = useState(
    [] as string[]
  );

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const isSearchActive =
    (!!debouncedSearchQuery && debouncedSearchQuery.length > 0) ||
    filterType !== "all" ||
    filterFacilityType !== "all" ||
    filterDistributionCompany.length > 0;

  const hasActiveFilters = isSearchActive;

  const queryParams: ApiQueryParams = {
    pageNumber: currentPage,
    pageSize,
    ...(orderByParam && { orderBy: orderByParam }),
    ...(!!debouncedSearchQuery && debouncedSearchQuery.length > 0
      ? { "filter:ct": { name: debouncedSearchQuery } }
      : {}),
    ...(isSearchActive && {
      "filter:eq": {
        ...(filterType === "all"
          ? {}
          : { individual: filterType === "individual" ? "true" : "false" }),
        ...(filterFacilityType === "all" ? {} : { type: filterFacilityType }),
        ...(filterDistributionCompany.length > 0
          ? { regionId: filterDistributionCompany }
          : {}),
      },
    }),
  };

  const { data, isLoading: loading, error } = useSubscriptions(queryParams);
  const { data: regions, isLoading: regionsLoading } = useRegions();

  const { data: subscriptionCountData } = useSubscriptions({
    pageNumber: 1,
    pageSize: 1,
  });

  // todo: this is terrible
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchQuery]);

  const firstPage = data?.pages[0];
  const totalPages = Math.max(firstPage?.totalPages || 1, 1);

  const selectedRows = Object.entries(rowSelection)
    .filter(([_k, v]) => v)
    .map(([k]) => k)
    .filter((id) => {
      return data?.pages.some((page) => page.content.some((d) => d.id === id));
    });

  useEffect(() => {
    if (!loading && data && firstPage) {
      const currentPageContent = firstPage.content || [];

      if (
        currentPageContent.length === 0 &&
        totalPages > 0 &&
        currentPage > 1
      ) {
        setCurrentPage(totalPages);
      }

      else if (currentPage > totalPages && totalPages > 0) {
        setCurrentPage(totalPages);
      }
    }
  }, [data, loading, totalPages, currentPage]);

  if (error) {
    console.error("Error fetching subscriptions:", error);
  }

  const activeTab = "subscriptions";

  const openCreate = () => {
    open(SubscriptionBillingModal, {
      name: "create-billing-modal",
      activeTab,
      header: {
        title: t(activeTab === "subscriptions" ? "title" : "facilities"),
        description: t(
          activeTab === "subscriptions" ? "add_subscription" : "add_facility"
        ),
      },
    });
  };

  const deleteMutation = useDeleteSubscription();

  const openDelete = () => {
    open(Delete as React.ComponentType<any>, {
      name: "delete-billing-modal",
      onConfirm: () => {
        return Promise.all(
          selectedRows.map(
            (subscriptionId) =>
              new Promise<void>((resolve, reject) =>
                deleteMutation.mutate(subscriptionId, {
                  onSuccess: () => {
                    resolve();
                  },
                  onError: (error) => {
                    console.error("Error deleting subscription:", error);
                    reject(error);
                  },
                })
              )
          )
        );
      },
      activeTab: activeTab,
    });
  };

  const openFilter = () => {
    open(Filter as React.ComponentType<any>, {
      name: "filter-billing-modal",
      currentFilters: {
        type: filterType,
        facilityType: filterFacilityType,
        distributionCompany: filterDistributionCompany,
      },
      overflowHidden: false,
      // todo: any
      onConfirm: ({ type, facilityType, distributionCompany }: any) => {
        setFilterType(type);
        setFilterFacilityType(facilityType);
        console.log("set", distributionCompany);
        setFilterDistributionCompany(distributionCompany);
      },
      activeTab: activeTab,
      clearFilters,
    });
  };

  const handleRowClick = ({ id }: { id: string }) => {
    navigate({
      to: `/subscriptions/${id}`,
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(page, 1));
  };

  const clearFilters = () => {
    setSearchQuery("");
    setCurrentPage(1);
    setFilterType("all");
    setFilterFacilityType("all");
    setFilterDistributionCompany([]);

    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  return (
    <>
      <Header
        title={
          <span className="flex items-center gap-2">
            {t("title")}

            {(subscriptionCountData?.pages?.[0]?.totalElements ?? 0) > 0 ? (
              <Badge
                color="purple"
                variant="outline"
                className="rounded-full pt-1"
              >
                {t(
                  activeTab === "subscriptions"
                    ? "subscriptions_count"
                    : "facilities_count",
                  { count: subscriptionCountData?.pages?.[0]?.totalElements }
                )}
              </Badge>
            ) : null}
          </span>
        }
        sidebarData={sidebarData}
        subContent={
          <div className="flex flex-col gap-4">
            <p className="max-w-4xl pb-4 text-base leading-6 text-gray-600">
              {t("description")}
            </p>
          </div>
        }
      />
      <Main>
        <div className="flex items-center justify-end py-4 w-full gap-2">
          {/* <div className="flex-1 w-full relative flex flex-col items-start justify-center gap-0.5 text-left text-lg text-gray-900 font-text-sm-regular">
            <div className="self-stretch flex flex-row items-center justify-start gap-2">
              <div className="relative leading-[28px] font-semibold">
                {t(activeTab === "subscriptions" ? "title" : "facilities")}
              </div>
              {(subscriptionCountData?.pages?.[0]?.totalElements ?? 0) > 0 ? (
                <Badge
                  color="purple"
                  variant="outline"
                  className="rounded-full pt-1"
                >
                  {t(
                    activeTab === "subscriptions"
                      ? "subscriptions_count"
                      : "facilities_count",
                    { count: subscriptionCountData?.pages?.[0]?.totalElements }
                  )}
                </Badge>
              ) : null}
            </div>
            <div className="self-stretch relative text-sm leading-[20px] text-gray-600 overflow-hidden text-ellipsis whitespace-nowrap">
              {t(
                activeTab === "subscriptions"
                  ? "manage_subscriptions"
                  : "manage_facilities"
              )}
            </div>
          </div> */}
          {/* <div className="relative">
            <Iconify
              name="untitled:search-lg"
              className="absolute size-5 left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
            />
            <Input
              placeholder={t("common.search_placeholder", {
                ns: "common",
              })}
              value={searchQuery}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearchQuery(e.target.value)
              }
              className="max-w-sm pl-10"
              ref={searchInputRef}
            />
          </div> */}

          {selectedRows.length > 0 ? (
            <Button onClick={openDelete} variant={"destructive"}>
              {t("common.delete_all", {
                ns: "common",
              })}
              <Iconify name="untitled:trash-01" className="size-5" />
            </Button>
          ) : null}
          <Button variant="outline" onClick={openFilter}>
            {t("common.filter", {
              ns: "common",
            })}
            <Iconify name="untitled:filter-lines" className="size-5" />
          </Button>
          {addDisabled ? null : (
            <Button onClick={openCreate}>
              {t("common.add_new", {
                ns: "common",
              })}
              <Iconify name="untitled:plus" className="size-5" />
            </Button>
          )}
        </div>

        <AnimatedTransition
          loading={loading || regionsLoading}
          skeleton={<SubscriptionsTableSkeleton />}
        >
          <DataTable
            columns={columns}
            data={firstPage?.content ?? []}
            meta={{
              activeTab,
              regions: regions ?? [],
            }}
            onRowClick={handleRowClick}
            rowSelection={rowSelection}
            onRowSelectionChange={setRowSelection}
            tableOptions={{
              getRowId: (row) => row.id,
              enableRowSelection: true,
              state: {
                pagination: {
                  pageIndex: Math.max(currentPage - 1, 0),
                  pageSize,
                },
                sorting,
                rowSelection,
              },
              manualPagination: true,
              manualSorting: true,
              pageCount: totalPages,
            }}
            onSortingChange={setSorting}
            empty={
              <Empty
                title={t(
                  hasActiveFilters
                    ? "no_subscription_filter_title"
                    : "no_subscription_title"
                )}
                description={t(
                  hasActiveFilters
                    ? "no_subscription_filter_description"
                    : "no_subscription_description"
                )}
              >
                {hasActiveFilters ? (
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="mt-4"
                  >
                    {t("common:common.clear_filters")}
                  </Button>
                ) : (
                  <Button onClick={openCreate}>{t("add_subscription")}</Button>
                )}
              </Empty>
            }
            className="w-full h-full"
            onPageChange={handlePageChange}
          />
        </AnimatedTransition>
      </Main>
    </>
  );
}
