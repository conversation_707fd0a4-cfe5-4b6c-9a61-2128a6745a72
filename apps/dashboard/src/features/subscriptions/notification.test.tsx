import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { NotificationTab } from './notification';

vi.mock('./data/queries', () => ({
  useSubscriptionDetail: () => ({
    data: { unexpectedUsageThreshold: 50, userDefinedLimit: 100 },
    isLoading: false,
  }),
  useUpdateNotificationSettings: () => ({
    mutateAsync: vi.fn(),
  }),
}));

describe('NotificationTab', () => {
  it('renders without crashing', () => {
    render(<NotificationTab subscriptionId="1" />);
    // Instead of using getByText(/85/), check that an input with value "100" is rendered.
    expect(screen.getByDisplayValue("100")).toBeDefined();
  });
});
