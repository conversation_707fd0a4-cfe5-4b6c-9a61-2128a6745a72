import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mass/shared/components/ui/select";
import { Input } from "@mass/shared/components/ui/input";
import { Button } from "@mass/shared/components/ui/button";
import {
  useSubscriptionDetail,
  useUpdateNotificationSettings,
} from "./data/queries";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

interface SubscriptionDetail {
  unexpectedUsageThreshold: number | null;
  userDefinedLimit: number | null;
}

export const NotificationTab = (props: { subscriptionId: string }) => {
  const isFirstRender = useRef(true);
  const { data: subscription, isLoading } = useSubscriptionDetail(
    props.subscriptionId
  ) as { data: SubscriptionDetail | undefined; isLoading: boolean };
  const { t } = useTranslation("subscriptions");

  const { mutateAsync } = useUpdateNotificationSettings(props.subscriptionId);

  // todo: both defaults will come from the global settings
  const [warningThreshold, setWarningThreshold] = useState("85");
  const [kwhWarningThreshold, setKwhWarningThreshold] = useState("100");

  useEffect(() => {
    if (isLoading || !subscription) return;
    if (!isFirstRender.current) return;

    if (subscription.unexpectedUsageThreshold !== null) {
      setWarningThreshold(
        Math.round((subscription.unexpectedUsageThreshold - 1) * 100).toString()
      );
    }
    if (subscription.userDefinedLimit !== null) {
      setKwhWarningThreshold(subscription.userDefinedLimit.toString());
    }

    isFirstRender.current = false;
  }, [isLoading]);

  const handleSave = async () => {
    await mutateAsync({
      unexpectedUsageThreshold: 1 + parseInt(warningThreshold) / 100,
      userDefinedLimit: parseInt(kwhWarningThreshold),
    });

    toast.success(t("notification_settings_saved"));
  };

  return (
    <>
      <div className="w-full py-4 flex flex-col md:flex-row md:items-center gap-2 md:gap-0">
        <div className="font-normal text-sm md:flex-1">
          {t("warning_threshold")}
        </div>
        <div className="flex gap-2 w-full md:w-auto md:flex-1">
          <Select
            value={warningThreshold.toString()}
            onValueChange={setWarningThreshold}
          >
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder={t("pick_threshold")} />
            </SelectTrigger>
            <SelectContent>
              {[...Array(20)].map((_, p) => {
                return (
                  <SelectItem
                    key={p * 5}
                    value={`${p * 5}`}
                  >{`${p * 5}%`}</SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="w-full py-4 flex flex-col md:flex-row md:items-center gap-2 md:gap-0">
        <div className="font-normal text-sm md:flex-1">
          {t("kwh_warning_threshold")}
        </div>
        <div className="flex gap-2 w-full md:w-auto md:flex-1">
          <Input
            className="w-full md:w-[180px]"
            value={kwhWarningThreshold}
            onChange={(e) => {
              const value = e.target.value;
              if (/^\d{0,3}$/.test(value)) {
                setKwhWarningThreshold(value);
              }
            }}
            type="text"
            inputMode="numeric"
          />
        </div>
      </div>
      <div className="flex justify-end w-full">
        <Button onClick={handleSave}>
          {t("common.save", {
            ns: "common",
          })}
        </Button>
      </div>
    </>
  );
};
