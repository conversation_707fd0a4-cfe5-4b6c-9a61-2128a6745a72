import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { OutagesTab } from './outages';

vi.mock('./data/queries', () => ({
		useSubscriptionDetail: () => ({
				data: { name: 'Sub Outage', type: 'individual' },
				isLoading: false,
		}),
		useExportSubscriptionOutageMutation: () => ({
				mutateAsync: vi.fn(),
				isPending: false,
		}),
}));

// Mock the GlobalSettings hook
vi.mock('@mass/shared/hooks/use-global-settings', () => ({
  useGlobalSettings: () => ({
    data: {
      value: {
        min: '2020',
        max: '2025',
      }
    },
    isLoading: false,
  }),
}));

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock API service
vi.mock('@/services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

describe('OutagesTab', () => {
  it('renders outages content', () => {
    // Create a new QueryClient for each test
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    
    render(
      <QueryClientProvider client={queryClient}>
        <OutagesTab subscriptionId="1" />
      </QueryClientProvider>
    );
    // Check for export button instead of "Sub Outage" which isn't in the rendered output
    expect(screen.getByText("common.export")).toBeInTheDocument();
  });
});
