import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mass/shared/components/ui/select";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { getYear } from "date-fns";
import { Button } from "@mass/shared/components/ui/button";
import { useExportSubscriptionOutageMutation } from "./data/queries";
import { toast } from "sonner";
import { useGlobalSettings } from "@mass/shared/hooks/use-global-settings";
import api from "@/services/api";

export const OutagesTab = (props: { subscriptionId: string }) => {
  const { t } = useTranslation("subscriptions");
  const [fileType, setFileType] = useState("csv");
  const [year, setYear] = useState(getYear(new Date()).toString());
  const startDate = year + "-01-01T00:00:00.000Z";
  const endDate = parseInt(year) + 1 + "-01-01T00:00:00.000Z";

  const { mutateAsync, isPending } = useExportSubscriptionOutageMutation(
    props.subscriptionId,
    new Date(startDate),
    new Date(endDate)
  );

  const { data: _dateRangeLimits } = useGlobalSettings(
    "subscriptions.usage.date-limits",
    api
  ) as any; // todo: any
  const dateRangeLimits = Object.fromEntries(
    Object.entries(_dateRangeLimits?.value ?? {}).map(([k, v]) => [
      k,
      parseInt(v as string),
    ] as const)
  );

  return (
    <>
      <div className="w-full py-4 flex items-center">
        <div className="font-normal text-sm flex-1">
          {t("usage.export.filetype")}
        </div>
        <div className="flex gap-2 flex-1">
          <Select value={fileType} onValueChange={setFileType}>
            <SelectTrigger>
              <SelectValue placeholder={t("usage.export.filetype")} />
            </SelectTrigger>
            <SelectContent>
              {["csv", "pdf", "xlsx"].map((option) => (
                <SelectItem key={option} value={option}>
                  {option.toUpperCase()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="w-full py-4 flex items-center">
        <div className="font-normal text-sm flex-1">
          {t("usage.export.year")}
        </div>
        <div className="flex gap-2 flex-1">
          <Select value={year} onValueChange={setYear}>
            <SelectTrigger>
              <SelectValue placeholder={t("usage.export.year")} />
            </SelectTrigger>
            <SelectContent>
              {[...Array(dateRangeLimits?.yearMax ?? 2)]
                .map((_, i) => (getYear(new Date()) - i).toString())
                .map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="flex w-full justify-end">
        <Button
          onClick={async () => {
            const download = async () => {
              const data = await mutateAsync(fileType);

              for (const file of data.files) {
                const { url, fileName } = file;

                const outsideRes = await fetch(url);

                const blob = await outsideRes.blob();
                const urlObject = URL.createObjectURL(blob);

                const link = document.createElement("a");
                link.href = urlObject;
                link.download = fileName;
                link.click();

                URL.revokeObjectURL(url);
                URL.revokeObjectURL(urlObject);
              }
            };

            const downloadPromise = download();

            toast.promise(downloadPromise, {
              loading: t("download_loading"),
              success: t("download_successful"),
              error: t("download_failed"),
            });

            await downloadPromise;
          }}
          disabled={isPending}
        >
          {t("common.export", {
            ns: "common",
          })}
        </Button>
      </div>
    </>
  );
};
