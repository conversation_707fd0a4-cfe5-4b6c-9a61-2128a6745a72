import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { OverviewTab } from './overview';

vi.mock('./data/queries', () => ({
  useSubscriptionDetail: () => ({
    data: { name: 'Overview Subscription', startDate: '2020-01-01', type: 'individual', regionId: 'r1', installationId: 'i1', individual: true, personIdentifier: 'p1' },
    isLoading: false,
  }),
  useRegions: () => ({
    data: [{ id: 'r1', name: 'Region 1' }],
    isLoading: false,
  }),
}));

describe('OverviewTab', () => {
  it('renders overview with subscription name', () => {
    render(<OverviewTab subscriptionId="1" />);
    expect(screen.getByText('Overview Subscription')).toBeInTheDocument();
  });
});
