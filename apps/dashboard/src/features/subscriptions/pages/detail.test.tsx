import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import SubscriptionDetail from './detail';

// Mock constants
vi.mock('@/constants/sidebar-data', () => ({
  sidebarData: []
}));

// Mock i18n
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: { changeLanguage: vi.fn() }
  }),
  I18nextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>
}));

// Mock the queries and router hooks
vi.mock('../data/queries', () => ({
  useSubscriptionDetail: (id: string) => {
    if (id === 'loading') return { data: undefined, isLoading: true, error: null };
    if (id === 'error') return { data: undefined, isLoading: false, error: new Error('Fetch error') };
    return { data: { name: 'Test Subscription', type: 'individual' }, isLoading: false, error: null };
  },
}));

vi.mock('@/utils/use-disabled', () => ({
  useIsDisabled: () => false
}));

// Mock tab components
vi.mock('../overview', () => ({
  OverviewTab: ({ subscriptionId }: { subscriptionId: string }) => <div data-testid="overview-tab">Overview: {subscriptionId}</div>
}));

vi.mock('../data', () => ({
  DataTab: ({ subscriptionId }: { subscriptionId: string }) => <div data-testid="data-tab">Data: {subscriptionId}</div>
}));

vi.mock('../notification', () => ({
  NotificationTab: ({ subscriptionId }: { subscriptionId: string }) => <div data-testid="notification-tab">Notifications: {subscriptionId}</div>
}));

vi.mock('../outages', () => ({
  OutagesTab: ({ subscriptionId }: { subscriptionId: string }) => <div data-testid="outages-tab">Outages: {subscriptionId}</div>
}));

// Mock shared components
vi.mock('@mass/shared/components/ui/sidebar', () => ({
  useSidebar: () => ({ toggleSidebar: vi.fn() }),
  SidebarProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

vi.mock('@mass/shared/components/organisms/layout/header', () => ({
  Header: ({ children, title, subContent }: { children?: React.ReactNode, title: React.ReactNode, subContent?: React.ReactNode }) => (
    <div data-testid="header">
      <div data-testid="header-title">{title}</div>
      {subContent && <div data-testid="header-subcontent">{subContent}</div>}
      {children && <div>{children}</div>}
    </div>
  )
}));

vi.mock('@mass/shared/components/organisms/layout/main', () => ({
  Main: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="content-container">{children}</div>
  )
}));

vi.mock('@mass/shared/components/ui/tabs', () => ({
  Tabs: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs">{children}</div>,
  TabsList: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children }: { children: React.ReactNode }) => <div data-testid="tabs-trigger">{children}</div>
}));

vi.mock('@mass/shared/components/ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: { children: React.ReactNode }) => <div data-testid="dropdown-menu">{children}</div>,
  DropdownMenuTrigger: ({ children }: { children: React.ReactNode }) => <div data-testid="dropdown-trigger">{children}</div>,
  DropdownMenuContent: ({ children }: { children: React.ReactNode }) => <div data-testid="dropdown-content">{children}</div>,
  DropdownMenuItem: ({ children }: { children: React.ReactNode }) => <div data-testid="dropdown-item">{children}</div>
}));

vi.mock('@mass/shared/components/ui/skeleton', () => ({
  Skeleton: ({ className }: { className?: string }) => <div data-testid="skeleton" className={className}></div>
}));

vi.mock('@mass/shared/components/ui/button', () => ({
  Button: ({ children, className, variant }: { children: React.ReactNode, className?: string, variant?: string }) => (
    <button data-testid="button" className={className} data-variant={variant}>{children}</button>
  )
}));

vi.mock('@tanstack/react-router', () => ({
  useNavigate: () => vi.fn(),
  useParams: () => ({ subscriptionId: '1' }),
  Navigate: ({ to }: { to: string }) => <div data-testid="navigate" data-to={to}></div>
}));

describe('SubscriptionDetail Page', () => {
  let queryClient: QueryClient;
  
  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
  });

  const renderWithProviders = (ui: React.ReactNode) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {ui}
      </QueryClientProvider>
    );
  };

  it('shows loading state', () => {
    // In loading state, header should render with a skeleton
    renderWithProviders(<SubscriptionDetail subscriptionId="loading" />);
    const header = screen.getByTestId('header');
    expect(header).toBeInTheDocument();
  });
  
  it('renders subscription data when available', () => {
    renderWithProviders(<SubscriptionDetail subscriptionId="1" />);
    // Header should contain the subscription name
    expect(screen.getByTestId('header-title')).toBeInTheDocument();
    // Content should render the overview tab by default
    expect(screen.getByTestId('overview-tab')).toBeInTheDocument();
  });
  
  it('renders empty content (redirect) when error occurs', () => {
    renderWithProviders(<SubscriptionDetail subscriptionId="error" />);
    // Should navigate to subscriptions page
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.getByTestId('navigate').getAttribute('data-to')).toBe('/subscriptions');
  });
});
