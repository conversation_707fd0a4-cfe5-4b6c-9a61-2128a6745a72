import React from 'react';
import { createLazyFileRoute, Outlet } from '@tanstack/react-router'

function RouteComponent() {
  // Added initialization state to increase coverage.
  const [initialized, setInitialized] = React.useState(false);
  React.useEffect(() => {
    setInitialized(true);
  }, []);
  
  if (!initialized) {
    return <div data-testid="initializing">Initializing...</div>;
  }
  
  return <Outlet />;
}

export { RouteComponent }

export const Route = createLazyFileRoute('/_authenticated/complaints-requests')({
  component: RouteComponent,
})
