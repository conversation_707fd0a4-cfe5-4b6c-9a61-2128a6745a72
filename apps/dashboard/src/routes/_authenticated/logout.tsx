import { useAuthStore } from "@/stores/auth";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useLogoutMutation } from "@/features/auth/hooks/use-auth";

export const Route = createFileRoute("/_authenticated/logout")({
  component: RouteComponent,
});

export function RouteComponent() {
  const navigate = useNavigate();
  const authStore = useAuthStore();
  const logoutMutation = useLogoutMutation();

  useEffect(() => {
    const performLogout = async () => {
      try {
        await logoutMutation.mutateAsync();
      } catch (error) {
        console.error("Logout failed:", error);
      } finally {
        authStore.auth.reset();
        navigate({ to: "/$auth", params: { auth: "login" } });
      }
    };

    performLogout();
  }, [navigate, authStore, logoutMutation]);

  return null;
}
