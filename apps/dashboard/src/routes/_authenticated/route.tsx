import { sidebarData } from '@/constants/sidebar-data'
import { AppSidebar } from '@mass/shared/components/organisms/layout/app-sidebar'
import { SidebarProvider } from '@mass/shared/components/ui/sidebar'
import { SearchProvider } from '@mass/shared/context/search-context'
import { cn } from '@mass/shared/lib/utils'
import { createFileRoute, Outlet } from '@tanstack/react-router'
import Cookies from 'js-cookie'
import { useAuthStore } from '@/stores/auth'
import { useEffect } from 'react'
import { useMe } from '@/features/auth'
import api from '@/services/api'
import AgreementWrapper from '@/features/auth/components/AgreementWrapper'

export const Route = createFileRoute('/_authenticated')({
    component: RouteComponent,
})

export function RouteComponent() {
    const defaultOpen = Cookies.get('sidebar:state') !== 'false'
    const { data: me } = useMe()
    const setUser = useAuthStore(state => state.auth.setUser)

    useEffect(() => {
        if (me) {
            setUser({
                id: me.id ?? '',
                permissions: me.permissions ?? [],
                email: me.email ?? null,
                phone: me.phone ?? null,
                tckn: me.tckn ?? null,
                firstName: me.firstName ?? null,
                lastName: me.lastName ?? null,
            })
        }
    }, [me, setUser])

    return (
        <SearchProvider>
            <SidebarProvider defaultOpen={defaultOpen}>
                <AppSidebar sidebarData={sidebarData} api={api}/>

                <div
                    id="content"
                    className={cn(
                        'ml-auto w-full max-w-full',
                        'peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]',
                        'peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]',
                        'transition-[width] duration-200 ease-linear',
                        'flex h-svh flex-col',
                    )}
                >
                    <AgreementWrapper>
                        <Outlet />
                    </AgreementWrapper>
                </div>
            </SidebarProvider>
        </SearchProvider>
    )
}
