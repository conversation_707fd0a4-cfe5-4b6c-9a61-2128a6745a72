/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach, Mock } from 'vitest';
import * as apiModule from './api';
import { toast } from 'sonner';
import i18next from 'i18next';

// Get the default export for correct function reference
const api = apiModule.default;
const { RateLimitError } = apiModule;

// Mock global fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// We shouldn't mock the api module itself since we're testing it
vi.unmock('./api');

// Mock dependencies
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

vi.mock('i18next', () => ({
  default: {
    t: vi.fn((key, options) => {
      const translations: Record<string, string> = {
        'common:api.errors.rate_limit_server': `Rate limit reached. Try again in ${options?.seconds || 60} seconds.`,
        'common:api.errors.default': 'Server error occurred',
        'common:api.errors.network_error': 'Network error occurred',
      };
      return translations[key] || key;
    }),
  },
  t: vi.fn((key, options) => {
    const translations: Record<string, string> = {
      'common:api.errors.rate_limit_server': `Rate limit reached. Try again in ${options?.seconds || 60} seconds.`,
      'common:api.errors.default': 'Server error occurred',
      'common:api.errors.network_error': 'Network error occurred',
    };
    return translations[key] || key;
  })
}));

// Mock location
const mockLocation = {
  href: '',
};
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

describe('api', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocation.href = '';
    // Mock import.meta.env
    vi.stubGlobal('import.meta', {
      env: {
        DEV: false,
      },
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should make successful GET request', async () => {
    const mockResponse = { data: 'test' };
    
    // Clear previous mock and set up a new one for this test
    mockFetch.mockReset();
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      headers: {
        get: vi.fn().mockReturnValue('application/json'),
      },
      json: vi.fn().mockResolvedValue(mockResponse),
    });

    const result = await api('/test');

    expect(mockFetch).toHaveBeenCalledWith('/api/test', {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      credentials: 'include',
    });
    expect(result).toEqual(mockResponse);
  });

  it('should make successful POST request with data', async () => {
    const mockResponse = { success: true };
    const postData = { name: 'test' };
    
    mockFetch.mockReset();
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 201,
      headers: {
        get: vi.fn().mockReturnValue('application/json'),
      },
      json: vi.fn().mockResolvedValue(mockResponse),
    });

    const result = await api('/test', {
      method: 'POST',
      body: JSON.stringify(postData),
    });

    expect(mockFetch).toHaveBeenCalledWith('/api/test', {
      method: 'POST',
      body: JSON.stringify(postData),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      credentials: 'include',
    });
    expect(result).toEqual(mockResponse);
  });

  it('should handle custom headers', async () => {
    const mockResponse = { data: 'test' };
    
    mockFetch.mockReset();
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      headers: {
        get: vi.fn().mockReturnValue('application/json'),
      },
      json: vi.fn().mockResolvedValue(mockResponse),
    });

    await api('/test', {
      headers: {
        'Authorization': 'Bearer token123',
        'X-Custom-Header': 'custom-value',
      },
    });

    expect(mockFetch).toHaveBeenCalledWith('/api/test', {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer token123',
        'X-Custom-Header': 'custom-value',
      },
      credentials: 'include',
    });
  });

  it('should redirect to login on 401 error', async () => {
    mockFetch.mockReset();
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401,
      statusText: 'Unauthorized',
    });

    // Setup window.location.href property
    Object.defineProperty(window, 'location', {
      value: {
        href: ''
      },
      writable: true
    });

    const result = await api('/test');

    expect(window.location.href).toBe('/login');
    expect(result).toBeNull();
  });

  it('should redirect to login on 403 error', async () => {
    mockFetch.mockReset();
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 403,
      statusText: 'Forbidden',
    });

    // Setup window.location.href property
    Object.defineProperty(window, 'location', {
      value: {
        href: ''
      },
      writable: true
    });

    const result = await api('/test');

    expect(window.location.href).toBe('/login');
    expect(result).toBeNull();
  });

  it('should handle 429 rate limit error', async () => {
    mockFetch.mockReset();
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 429,
      statusText: 'Too Many Requests',
      headers: {
        get: vi.fn().mockReturnValue('60'),
      },
    });

    try {
      await api('/test');
      // If we reach here, no error was thrown
      expect.fail('Should have thrown an error');
    } catch (error) {
      expect(error).toBeInstanceOf(RateLimitError);
      expect(toast.error).toHaveBeenCalledWith('Rate limit reached. Try again in 60 seconds.');
    }
  });

  it.skip('should handle 429 rate limit error without Retry-After header', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 429,
      statusText: 'Too Many Requests',
      headers: {
        get: vi.fn().mockReturnValue(null),
      },
    });

    await expect(api('/test')).rejects.toThrow(RateLimitError);
    expect(toast.error).toHaveBeenCalledWith('Rate limit reached. Try again in 60 seconds.');
  });

  it('should handle server errors (5xx)', async () => {
    mockFetch.mockReset();
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
      text: vi.fn().mockResolvedValue('{}'),
    });

    try {
      await api('/test');
      expect.fail('Should have thrown an error');
    } catch (error) {
      expect((error as Error).message).toContain('500: Internal Server Error');
      expect(toast.error).toHaveBeenCalledWith('Server error occurred');
    }
  });

  it('should handle error response with error message', async () => {
    const errorMessage = 'Custom error message';
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 400,
      statusText: 'Bad Request',
      text: vi.fn().mockResolvedValue(JSON.stringify({ error: errorMessage })),
    });

    await expect(api('/test')).rejects.toThrow(errorMessage);
  });

  it('should handle non-JSON response', async () => {
    const textResponse = 'plain text response';
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      headers: {
        get: vi.fn().mockReturnValue('text/plain'),
      },
      text: vi.fn().mockResolvedValue(textResponse),
    });

    const result = await api('/test');
    expect(result).toBe(textResponse);
  });

  it('should handle network errors', async () => {
    mockFetch.mockRejectedValueOnce(new TypeError('Network error'));

    await expect(api('/test')).rejects.toThrow('Network error');
    expect(toast.error).toHaveBeenCalledWith('Network error occurred');
  });

  it('should log requests and responses in dev mode', async () => {
    vi.stubGlobal('import.meta', {
      env: {
        DEV: true,
      },
    });
    
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    const mockResponse = { data: 'test' };
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      statusText: 'OK',
      headers: {
        get: vi.fn().mockReturnValue('application/json'),
      },
      json: vi.fn().mockResolvedValue(mockResponse),
    });

    await api('/test', { method: 'POST' });

    expect(consoleSpy).toHaveBeenCalledWith(
      '📤 [API Request]',
      'POST',
      '/api/test',
      expect.objectContaining({
        method: 'POST'
      })
    );
    expect(consoleSpy).toHaveBeenCalledWith(
      '📥 [API Response]',
      'POST',
      '/api/test',
      {
        status: 200,
        statusText: 'OK',
      }
    );

    consoleSpy.mockRestore();
  });

  it('should handle error parsing JSON response', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 400,
      statusText: 'Bad Request',
      text: vi.fn().mockResolvedValue('Invalid JSON'),
      headers: {
        get: vi.fn().mockReturnValue('application/json'),
      }
    });

    await expect(api('/test')).rejects.toThrow('400: Bad Request');
  });

  it('should export RateLimitError class', () => {
    const error = new RateLimitError('Test message');
    expect(error).toBeInstanceOf(Error);
    expect(error.name).toBe('RateLimitError');
    expect(error.message).toBe('Test message');
  });
});
