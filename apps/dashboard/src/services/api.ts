import { toast } from "sonner";
import i18next from 'i18next';

/**
 * Custom error class for rate limit errors
 */
export class RateLimitError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "RateLimitError";
  }
}

type ApiInit = RequestInit & { skipPrefix?: boolean };

/**
 * Detects if we are in development environment
 */
const isDev = typeof import.meta !== 'undefined' ? 
  import.meta.env?.DEV : 
  process.env.NODE_ENV === "development";

/**
 * Makes API requests with proper error handling and standardized headers
 * 
 * @param path - API endpoint path
 * @param init - Request options
 * @returns Response data
 */
function api(path: string, init: ApiInit = {}) {
  const url = init.skipPrefix ? path : `/api${path}`;
  if (isDev) console.log("📤 [API Request]", init.method || "GET", url, init);

  // Set default headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...init.headers,
  };

  // Merge options with defaults
  const options: RequestInit = {
    ...init,
    headers,
    credentials: 'include' as RequestCredentials,
  };

  return fetch(url, options)
    .then(response => {
      if (isDev) console.log("📥 [API Response]", init.method || "GET", url, {
        status: response.status,
        statusText: response.statusText,
      });

      // Handle authentication errors
      if ([401, 403].includes(response.status)) {
        window.location.href = "/login";
        return null;
      }

      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After') || '60';
        const seconds = parseInt(retryAfter, 10);
        const message = i18next.t('common:api.errors.rate_limit_server', { seconds });
        toast.error(message);
        throw new RateLimitError(message);
      }

      // Handle other errors
      if (!response.ok) {
        return response.text().then(text => {
          let errorMessage;
          try {
            const errorData = JSON.parse(text);
            errorMessage = errorData.error || `${response.status}: ${response.statusText}`;
          } catch (e) {
            errorMessage = `${response.status}: ${response.statusText}`;
          }

          toast.error(response.status >= 500 
            ? i18next.t('common:api.errors.default') 
            : errorMessage);
          throw new Error(errorMessage);
        });
      }

      // Process successful response
      const contentType = response.headers.get("content-type") ?? "";
      if (contentType.includes("application/json")) {
        return response.json();
      } else {
        return response.text();
      }
    })
    .catch(err => {
      // Handle network errors
      if (!(err instanceof RateLimitError) && !(err instanceof Error && err.message.includes(':'))) {
        toast.error(i18next.t('common:api.errors.network_error'));
      }
      throw err;
    });
}

export default api;
