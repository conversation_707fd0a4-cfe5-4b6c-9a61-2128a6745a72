/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach, Mock } from 'vitest';
import { authService, type MeResponse, type LoginResponse, type RegisterResponse } from './auth';
import api from '../api';
import { Api } from '../enums';

// Mock the api module
vi.mock('../api', () => ({
  default: vi.fn(),
}));

const mockApi = api as Mock;

describe('authService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('me', () => {
    it('should fetch user data successfully', async () => {
      const mockMeResponse: MeResponse = {
        id: '123',
        permissions: [{ scope: 'read' }],
        email: '<EMAIL>',
        phone: '+1234567890',
        tckn: '12345678901',
        firstName: 'John',
        lastName: 'Doe',
      };

      mockApi.mockResolvedValueOnce(mockMeResponse);

      const result = await authService.me();

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/me`, {
        method: 'GET',
      });
      expect(result).toEqual(mockMeResponse);
    });

    it('should handle error during me request', async () => {
      const error = new Error('API Error');
      mockApi.mockRejectedValueOnce(error);

      await expect(authService.me()).rejects.toThrow(error);
      expect(console.error).toHaveBeenCalledWith('Error during me:', error);
    });
  });

  describe('login', () => {
    it('should login successfully', async () => {
      const loginData = { email: '<EMAIL>', password: 'password123' };
      const mockLoginResponse: LoginResponse = {
        token: 'mock-token',
        user: { id: '123', email: '<EMAIL>', name: 'John Doe' },
        session: true,
      };

      mockApi.mockResolvedValueOnce(mockLoginResponse);

      const result = await authService.login(loginData);

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/login`, {
        method: 'POST',
        body: JSON.stringify(loginData),
      });
      expect(result).toEqual(mockLoginResponse);
    });

    it('should handle login error', async () => {
      const loginData = { email: '<EMAIL>', password: 'wrongpassword' };
      const error = new Error('Invalid credentials');
      mockApi.mockRejectedValueOnce(error);

      await expect(authService.login(loginData)).rejects.toThrow(error);
      expect(console.error).toHaveBeenCalledWith('Error during login:', error);
    });
  });

  describe('register', () => {
    it('should register successfully with minimal data', async () => {
      const registerData = { email: '<EMAIL>', password: 'password123' };
      const mockRegisterResponse: RegisterResponse = {
        success: true,
        message: 'Registration successful',
        userId: '123',
      };

      mockApi.mockResolvedValueOnce(mockRegisterResponse);

      const result = await authService.register(registerData);

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/register`, {
        method: 'POST',
        body: JSON.stringify(registerData),
      });
      expect(result).toEqual(mockRegisterResponse);
    });

    it('should register successfully with full data', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        phone: '+1234567890',
        tckn: '12345678901',
      };
      const mockRegisterResponse: RegisterResponse = {
        success: true,
        message: 'Registration successful',
        userId: '123',
      };

      mockApi.mockResolvedValueOnce(mockRegisterResponse);

      const result = await authService.register(registerData);

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/register`, {
        method: 'POST',
        body: JSON.stringify(registerData),
      });
      expect(result).toEqual(mockRegisterResponse);
    });

    it('should handle registration error', async () => {
      const registerData = { email: '<EMAIL>', password: 'password123' };
      const error = new Error('Registration failed');
      mockApi.mockRejectedValueOnce(error);

      await expect(authService.register(registerData)).rejects.toThrow(error);
      expect(console.error).toHaveBeenCalledWith('Error during registration:', error);
    });
  });

  describe('forgotPassword', () => {
    it('should send forgot password request successfully', async () => {
      const forgotPasswordData = { email: '<EMAIL>' };
      const mockResponse = { success: true, message: 'Password reset email sent' };

      mockApi.mockResolvedValueOnce(mockResponse);

      const result = await authService.forgotPassword(forgotPasswordData);

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/forgot-password`, {
        method: 'POST',
        body: JSON.stringify(forgotPasswordData),
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle forgot password error', async () => {
      const forgotPasswordData = { email: '<EMAIL>' };
      const error = new Error('Email not found');
      mockApi.mockRejectedValueOnce(error);

      await expect(authService.forgotPassword(forgotPasswordData)).rejects.toThrow(error);
      expect(console.error).toHaveBeenCalledWith('Error requesting password reset:', error);
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      const resetPasswordData = { token: 'reset-token', password: 'newpassword123' };
      const mockResponse = { success: true, message: 'Password reset successful' };

      mockApi.mockResolvedValueOnce(mockResponse);

      const result = await authService.resetPassword(resetPasswordData);

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/reset-password`, {
        method: 'POST',
        body: JSON.stringify(resetPasswordData),
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle reset password error', async () => {
      const resetPasswordData = { token: 'invalid-token', password: 'newpassword123' };
      const error = new Error('Invalid token');
      mockApi.mockRejectedValueOnce(error);

      await expect(authService.resetPassword(resetPasswordData)).rejects.toThrow(error);
      expect(console.error).toHaveBeenCalledWith('Error resetting password:', error);
    });
  });

  describe('verifyOtp', () => {
    it('should verify OTP successfully', async () => {
      const verifyOtpData = { email: '<EMAIL>', code: '123456' };
      const mockResponse = { success: true, message: 'OTP verified successfully' };

      mockApi.mockResolvedValueOnce(mockResponse);

      const result = await authService.verifyOtp(verifyOtpData);

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/verify-otp`, {
        method: 'POST',
        body: JSON.stringify(verifyOtpData),
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle verify OTP error', async () => {
      const verifyOtpData = { email: '<EMAIL>', code: 'invalid' };
      const error = new Error('Invalid OTP code');
      mockApi.mockRejectedValueOnce(error);

      await expect(authService.verifyOtp(verifyOtpData)).rejects.toThrow(error);
      expect(console.error).toHaveBeenCalledWith('Error verifying OTP:', error);
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      mockApi.mockResolvedValueOnce(undefined);

      await authService.logout();

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/logout`, {
        method: 'DELETE',
      });
    });

    it('should handle logout error', async () => {
      const error = new Error('Logout failed');
      mockApi.mockRejectedValueOnce(error);

      await expect(authService.logout()).rejects.toThrow(error);
      expect(console.error).toHaveBeenCalledWith('Error during logout:', error);
    });
  });

  describe('deleteAccount', () => {
    it('should delete account successfully', async () => {
      mockApi.mockResolvedValueOnce(undefined);

      await authService.deleteAccount();

      expect(mockApi).toHaveBeenCalledWith(`${Api.auth}/account/delete`, {
        method: 'DELETE',
      });
    });

    it('should handle delete account error', async () => {
      const error = new Error('Delete account failed');
      mockApi.mockRejectedValueOnce(error);

      await expect(authService.deleteAccount()).rejects.toThrow(error);
      expect(console.error).toHaveBeenCalledWith('Error deleting account:', error);
    });
  });
});
