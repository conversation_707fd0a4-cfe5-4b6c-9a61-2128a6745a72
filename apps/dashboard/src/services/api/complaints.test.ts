/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  fetchComplaints, 
  createComplaint,
  fetchDocument,
  markDocumentAsDone,
  cancelComplaint,
  type ComplaintPayload 
} from './complaints';
import api from '../api';
import type { ApiQueryParams } from '../types';

// Mock the API
vi.mock('../api', () => ({
  default: vi.fn(),
}));

const mockApi = api as any;

describe('Complaints API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('fetchComplaints', () => {
    it('should fetch complaints with pagination', async () => {
      const mockResponse = {
        content: [
          { id: '1', subscriptionId: 'sub1', body: 'Test complaint', category: 'technical', subcategory: 'bug' },
          { id: '2', subscriptionId: 'sub2', body: 'Another complaint', category: 'billing', subcategory: 'refund' },
        ],
        pageable: { pageNumber: 0, pageSize: 10 },
        totalElements: 2,
        totalPages: 1,
      };

      mockApi
        .mockResolvedValueOnce(mockResponse)
        .mockResolvedValueOnce({ id: 'sub1', name: 'Subscription 1' })
        .mockResolvedValueOnce({ id: 'sub2', name: 'Subscription 2' });

      const params: ApiQueryParams = { pageNumber: 1, pageSize: 10 };
      const result = await fetchComplaints(params);

      expect(mockApi).toHaveBeenCalledWith('/complaint?pageNumber=1&pageSize=10', { method: 'GET' });
      expect(result.content).toHaveLength(2);
      expect(result.content[0]).toHaveProperty('subscription');
    });

    it('should handle query string building with filters', async () => {
      const mockResponse = { content: [], pageable: { pageNumber: 1, pageSize: 20 }, totalElements: 0, totalPages: 0 };
      mockApi.mockResolvedValueOnce(mockResponse);

      const params: ApiQueryParams = {
        pageNumber: 2,
        pageSize: 20,
        orderBy: 'createdAt,desc',
        'filter:eq': { status: 'open', category: 'technical' }
      };

      await fetchComplaints(params);

      expect(mockApi).toHaveBeenCalledWith(
        expect.stringContaining('/complaint?'),
        { method: 'GET' }
      );
    });

    it('should throw error when no response from server', async () => {
      mockApi.mockResolvedValueOnce(null);

      const params: ApiQueryParams = { pageNumber: 1, pageSize: 10 };

      await expect(fetchComplaints(params)).rejects.toThrow('No response from server');
    });
  });

  describe('createComplaint', () => {
    it('should create a new complaint', async () => {
      const payload: ComplaintPayload = {
        body: 'New complaint',
        category: 'technical',
        subcategory: 'bug',
        subscriptionId: 'sub1',
        files: ['file1.pdf', 'file2.jpg'],
      };

      const mockResponse = { id: '456', ...payload };
      mockApi.mockResolvedValueOnce(mockResponse);

      const result = await createComplaint(payload);

      expect(mockApi).toHaveBeenCalledWith('/complaint', {
        method: 'POST',
        body: JSON.stringify(payload),
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('fetchDocument', () => {
    it('should fetch a document by file ID', async () => {
      const mockDocument = { id: 'file123', name: 'document.pdf', url: 'https://example.com/file.pdf' };
      mockApi.mockResolvedValueOnce(mockDocument);

      const result = await fetchDocument('file123');

      expect(mockApi).toHaveBeenCalledWith('/document/file123');
      expect(result).toEqual(mockDocument);
    });
  });

  describe('markDocumentAsDone', () => {
    it('should mark document as done', async () => {
      mockApi.mockResolvedValueOnce({ status: 'success' });

      await markDocumentAsDone('file123');

      expect(mockApi).toHaveBeenCalledWith('/document/file123/done', { method: 'PATCH' });
    });
  });

  describe('cancelComplaint', () => {
    it('should cancel a complaint', async () => {
      mockApi.mockResolvedValueOnce({ status: 'cancelled' });

      await cancelComplaint('complaint123');

      expect(mockApi).toHaveBeenCalledWith('/complaint/complaint123/cancel', { method: 'PATCH' });
    });
  });

  describe('error handling', () => {
    it('should handle API errors in fetchComplaints', async () => {
      mockApi.mockRejectedValueOnce(new Error('API Error'));

      const params: ApiQueryParams = { pageNumber: 1, pageSize: 10 };

      await expect(fetchComplaints(params)).rejects.toThrow('API Error');
    });

    it('should handle API errors in createComplaint', async () => {
      const payload: ComplaintPayload = {
        body: 'New complaint',
        category: 'technical',
        subcategory: 'bug',
        subscriptionId: 'sub1',
        files: [],
      };

      mockApi.mockRejectedValueOnce(new Error('Creation failed'));

      await expect(createComplaint(payload)).rejects.toThrow('Creation failed');
    });

    it('should handle API errors in fetchDocument', async () => {
      mockApi.mockRejectedValueOnce(new Error('Document not found'));

      await expect(fetchDocument('invalid-id')).rejects.toThrow('Document not found');
    });

    it('should handle API errors in markDocumentAsDone', async () => {
      mockApi.mockRejectedValueOnce(new Error('Update failed'));

      await expect(markDocumentAsDone('file123')).rejects.toThrow('Update failed');
    });

    it('should handle API errors in cancelComplaint', async () => {
      mockApi.mockRejectedValueOnce(new Error('Cancel failed'));

      await expect(cancelComplaint('complaint123')).rejects.toThrow('Cancel failed');
    });
  });
});
