import { DetailSubscription } from "@/features/subscriptions/pages/detail";
import api from "../api";
import { Api } from "../enums";
import type { ApiQueryParams, PagedResponse } from "../types";
import { buildApiQueryString } from "../types";
import { subscriptionService } from "./subscriptions";
import { settingsService } from "./settings";

export interface Notification {
  id: string;
  createdAt: string;
  textContent: {
    TR: string;
    EN: string;
  };
  read: boolean;
  regionId: string;
  subscriptionId: string;
  subscription: DetailSubscription | null;
  type: string;
  subtype: string;
}

export type NotificationsQueryParams = ApiQueryParams;

export const notificationService = {
  async getAll(
    params: NotificationsQueryParams
  ): Promise<PagedResponse<Notification>> {
    try {
      const queryString = buildApiQueryString(params);

      const response = await api(`${Api.notifications}?${queryString}`, {
        method: "GET",
      });

      return {
        ...response,
        content: await Promise.all(
          response.content.map(async (notification: Notification) => ({
            ...notification,
            subscription: notification.subscriptionId
              ? await subscriptionService.getById(notification.subscriptionId)
              : null,
          }))
        ),
      };
    } catch (error) {
      console.error("Error fetching notifications:", error);
      throw error;
    }
  },

  async getById(id: string): Promise<Notification> {
    try {
      const response = await api(`${Api.notifications}/${id}`, {
        method: "GET",
      });
      return response;
    } catch (error) {
      console.error(`Error fetching notification ${id}:`, error);
      throw error;
    }
  },

  async count(read: boolean, archived: boolean): Promise<number> {
    try {
      const response = await api(
        `${Api.notifications}/count?read=${read ? "true" : "false"}&archived=${archived ? "true" : "false"}`,
        {
          method: "GET",
        }
      );

      return response?.count ?? 0;
    } catch (error) {
      console.error(`Error fetching notification count:`, error);
      throw error;
    }
  },

  async markAsRead(id: string, tag?: string): Promise<void> {
    try {
      const effectiveTag = tag ? tag : "READ";
      await api(`${Api.notifications}/${id}/read?tag=${effectiveTag}`, {
        method: "PATCH",
      });
    } catch (error) {
      console.error(`Error updating notification status for ${id}:`, error);
      throw error;
    }
  },

  async getCategories(): Promise<any> {
    try {
      return await settingsService.getGlobalSetting("notifications.categories");
    } catch (error) {
      console.error("Error fetching notification categories:", error);
      throw error;
    }
  },
  async getSubCategories(): Promise<any> {
    try {
      return await settingsService.getGlobalSetting("notifications.categories");
    } catch (error) {
      console.error("Error fetching notification categories:", error);
      throw error;
    }
  },
};
