import api from "@/services/api";
import { settingsService } from "./settings";

const log = (label: string, err: unknown) => console.error(label, err);

export interface Notification {
  orderBy: string;
  pageNumber: number;
  id: string;
  createdAt: string;
  textContent: Record<string, string>;
  read: boolean;
  regionId: string;
  subscriptionId: string | null;
  subscription: any | null;
  type: string;
  subtype: string;
}

export const notificationService = {
  getAll: async (params: Record<string, unknown>) => {
    try {
      // Convert params to query string
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value));
        }
      });
      
      const queryString = queryParams.toString();
      const url = queryString ? `/notifications?${queryString}` : '/notifications';
      
      return await api(url, { method: "GET" });
    } catch (e) {
      log("Error fetching notifications:", e);
      throw e;
    }
  },

  getById: async (id: string) => {
    try {
      return await api(`/notifications/${id}`, { method: "GET" });
    } catch (e) {
      log(`Error fetching notification ${id}:`, e);
      throw e;
    }
  },

  count: async (read: boolean, archived: boolean) => {
    try {
      const result = await api(
        `/notifications/count?read=${read}&archived=${archived}`,
        { method: "GET" }
      );
      return result?.count || 0;
    } catch (e) {
      log("Error fetching notification count:", e);
      throw e;
    }
  },

  markAsRead: async (id: string, tag: string = "READ") => {
    try {
      const actualTag = !tag ? "READ" : tag;
      return await api(`/notifications/${id}/read?tag=${actualTag}`, { method: "PATCH" });
    } catch (e) {
      log(`Error updating notification status for ${id}:`, e);
      throw e;
    }
  },

  getCategories: async () => {
    try {
      return await settingsService.getGlobalSetting("notifications.categories");
    } catch (e) {
      log("Error fetching notification categories:", e);
      throw e;
    }
  },
};

export default notificationService;
