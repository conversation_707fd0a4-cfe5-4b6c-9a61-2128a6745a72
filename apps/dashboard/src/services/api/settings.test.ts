/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { settingsService } from './settings';
import api from '../api';
import { Api } from '../enums';

// Mock the API
vi.mock('../api', () => ({
  default: vi.fn(),
}));

// Mock console.error to spy on error logging
const mockConsoleError = vi.fn();
console.error = mockConsoleError;

const mockApi = api as any;

describe('Settings Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockConsoleError.mockClear();
  });

  describe('getNotificationSettings', () => {
    it('should fetch notification settings successfully', async () => {
      const mockSettings = {
        emailNotifications: true,
        smsNotifications: false,
        pushNotifications: true,
      };

      mockApi.mockResolvedValueOnce({ value: mockSettings });

      const result = await settingsService.getNotificationSettings();

      expect(mockApi).toHaveBeenCalledWith(
        `${Api.settings}/user/notification.preferences`,
        { method: 'GET' }
      );
      expect(result).toEqual(mockSettings);
    });

    it('should handle API errors when fetching notification settings', async () => {
      const error = new Error('API Error');
      mockApi.mockRejectedValueOnce(error);

      await expect(settingsService.getNotificationSettings()).rejects.toThrow('API Error');
      expect(mockApi).toHaveBeenCalledWith(
        `${Api.settings}/user/notification.preferences`,
        { method: 'GET' }
      );
    });
  });

  describe('setNotificationSettings', () => {
    it('should update notification settings successfully', async () => {
      const newSettings = {
        emailNotifications: false,
        smsNotifications: true,
        pushNotifications: false,
      };

      mockApi.mockResolvedValueOnce(undefined);

      await settingsService.setNotificationSettings(newSettings);

      expect(mockApi).toHaveBeenCalledWith(
        `${Api.settings}/user/notification.preferences`,
        {
          method: 'PATCH',
          body: JSON.stringify({ value: newSettings }),
        }
      );
    });

    it('should handle API errors when updating notification settings', async () => {
      const newSettings = { emailNotifications: true };
      const error = new Error('Update failed');
      mockApi.mockRejectedValueOnce(error);

      await expect(settingsService.setNotificationSettings(newSettings)).rejects.toThrow('Update failed');
      expect(mockApi).toHaveBeenCalledWith(
        `${Api.settings}/user/notification.preferences`,
        {
          method: 'PATCH',
          body: JSON.stringify({ value: newSettings }),
        }
      );
    });

    it('should handle complex notification settings object', async () => {
      const complexSettings = {
        emailNotifications: true,
        smsNotifications: false,
        pushNotifications: true,
        preferences: {
          frequency: 'daily',
          categories: ['updates', 'alerts'],
        },
      };

      mockApi.mockResolvedValueOnce(undefined);

      await settingsService.setNotificationSettings(complexSettings);

      expect(mockApi).toHaveBeenCalledWith(
        `${Api.settings}/user/notification.preferences`,
        {
          method: 'PATCH',
          body: JSON.stringify({ value: complexSettings }),
        }
      );
    });
  });

  describe('getGlobalSetting', () => {
    it('should fetch global setting successfully', async () => {
      const settingValue = 'some-global-value';
      mockApi.mockResolvedValueOnce({ value: settingValue });

      const result = await settingsService.getGlobalSetting('feature-flag');

      expect(mockApi).toHaveBeenCalledWith(
        `${Api.settings}/global/feature-flag`,
        { method: 'GET' }
      );
      expect(result).toBe(settingValue);
    });

    it('should handle different types of global settings', async () => {
      const booleanSetting = true;
      const numberSetting = 42;
      const objectSetting = { enabled: true, limit: 100 };

      // Test boolean setting
      mockApi.mockResolvedValueOnce({ value: booleanSetting });
      let result = await settingsService.getGlobalSetting('boolean-flag');
      expect(result).toBe(booleanSetting);

      // Test number setting
      mockApi.mockResolvedValueOnce({ value: numberSetting });
      result = await settingsService.getGlobalSetting('rate-limit');
      expect(result).toBe(numberSetting);

      // Test object setting
      mockApi.mockResolvedValueOnce({ value: objectSetting });
      result = await settingsService.getGlobalSetting('config');
      expect(result).toEqual(objectSetting);
    });

    it('should handle API errors and log them', async () => {
      const error = new Error('Setting not found');
      mockApi.mockRejectedValueOnce(error);

      await expect(settingsService.getGlobalSetting('non-existent-key')).rejects.toThrow('Setting not found');
      
      expect(mockConsoleError).toHaveBeenCalledWith(
        'Error fetching global setting [non-existent-key]:',
        error
      );
      expect(mockApi).toHaveBeenCalledWith(
        `${Api.settings}/global/non-existent-key`,
        { method: 'GET' }
      );
    });

    it('should handle network errors and log them', async () => {
      const networkError = new Error('Network timeout');
      mockApi.mockRejectedValueOnce(networkError);

      await expect(settingsService.getGlobalSetting('timeout-key')).rejects.toThrow('Network timeout');
      
      expect(mockConsoleError).toHaveBeenCalledWith(
        'Error fetching global setting [timeout-key]:',
        networkError
      );
    });

    it('should handle special characters in setting keys', async () => {
      const settingValue = 'special-value';
      mockApi.mockResolvedValueOnce({ value: settingValue });

      await settingsService.getGlobalSetting('setting-with-dashes_and_underscores.dots');

      expect(mockApi).toHaveBeenCalledWith(
        `${Api.settings}/global/setting-with-dashes_and_underscores.dots`,
        { method: 'GET' }
      );
    });
  });
});
