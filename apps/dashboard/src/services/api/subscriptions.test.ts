import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { subscriptionService } from './subscriptions';
import api from '../api';
import type { ApiQueryParams } from '../types';

// Mock the api module
vi.mock('../api', () => ({
  default: vi.fn(),
}));

// Mock console methods
const mockConsoleError = vi.fn();
console.error = mockConsoleError;

describe('Subscription Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockConsoleError.mockClear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getAll', () => {
    it('should fetch subscriptions with default parameters', async () => {
      const mockResponse = {
        content: [
          {
            id: '1',
            name: 'Test Subscription',
            individual: true,
            personIdentifier: '12345678901',
            regionId: 'region-1',
            installationId: 'inst-1',
          },
        ],
        totalElements: 1,
        totalPages: 1,
        first: true,
        last: true,
      };

      (api as any).mockResolvedValue(mockResponse);

      const result = await subscriptionService.getAll();

      expect(api).toHaveBeenCalledWith(
        '/subscriptions?pageNumber=1&pageSize=10&orderBy=createdAt%3Aasc',
        { method: 'GET' }
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors', async () => {
      const error = new Error('API Error');
      (api as any).mockRejectedValue(error);

      await expect(subscriptionService.getAll()).rejects.toThrow('API Error');
      expect(mockConsoleError).toHaveBeenCalledWith('Error fetching subscriptions:', error);
    });
  });

  describe('getById', () => {
    it('should fetch subscription by id', async () => {
      const mockSubscription = {
        id: '123',
        name: 'Test Subscription',
        individual: true,
        personIdentifier: '12345678901',
      };

      (api as any).mockResolvedValue(mockSubscription);

      const result = await subscriptionService.getById('123');

      expect(api).toHaveBeenCalledWith('/subscriptions/123', {
        method: 'GET',
      });
      expect(result).toEqual(mockSubscription);
    });

    it('should handle API errors when fetching by id', async () => {
      const error = new Error('Not found');
      (api as any).mockRejectedValue(error);

      await expect(subscriptionService.getById('123')).rejects.toThrow('Not found');
      expect(mockConsoleError).toHaveBeenCalledWith('Error fetching subscription 123:', error);
    });
  });

  describe('create', () => {
    it('should create subscription for individual', async () => {
      const mockResponse = {
        data: {
          id: '123',
          name: 'New Subscription',
          individual: true,
        },
      };

      (api as any).mockResolvedValue(mockResponse);

      const createData = {
        name: 'New Subscription',
        type: 'individual' as const,
        tckn: '12345678901',
        distributionCompany: 'region-1',
        installationId: 'inst-1',
        facilityType: 'residential',
      };

      const result = await subscriptionService.create(createData);

      expect(api).toHaveBeenCalledWith('/subscriptions', {
        method: 'POST',
        body: JSON.stringify({
          name: 'New Subscription',
          individual: true,
          personIdentifier: '12345678901',
          regionId: 'region-1',
          installationId: 'inst-1',
        }),
      });
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle API errors when creating', async () => {
      const error = new Error('Creation failed');
      (api as any).mockRejectedValue(error);

      const createData = {
        name: 'Test',
        type: 'individual' as const,
        tckn: '12345678901',
        distributionCompany: 'region-1',
        installationId: 'inst-1',
        facilityType: 'residential',
      };

      await expect(subscriptionService.create(createData)).rejects.toThrow('Creation failed');
      expect(mockConsoleError).toHaveBeenCalledWith('Error creating subscription:', error);
    });
  });

  describe('update', () => {
    it('should update subscription', async () => {
      const mockResponse = {
        data: {
          id: '123',
          name: 'Updated Subscription',
        },
      };

      (api as any).mockResolvedValue(mockResponse);

      const updateData = {
        name: 'Updated Subscription',
      };

      const result = await subscriptionService.update('123', updateData);

      expect(api).toHaveBeenCalledWith('/subscriptions/123', {
        method: 'PATCH',
        body: JSON.stringify(updateData),
      });
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('updateNotificationSettings', () => {
    it('should update notification settings', async () => {
      (api as any).mockResolvedValue({});

      const notificationData = {
        unexpectedUsageThreshold: 80,
        userDefinedLimit: 1000,
      };

      await subscriptionService.updateNotificationSettings('123', notificationData);

      expect(api).toHaveBeenCalledWith('/subscriptions/123/notification', {
        method: 'PATCH',
        body: JSON.stringify(notificationData),
      });
    });

    it('should handle API errors when updating notification settings', async () => {
      const mockError = new Error('API Error');
      (api as any).mockRejectedValue(mockError);

      const notificationData = {
        unexpectedUsageThreshold: 80,
        userDefinedLimit: 1000,
      };

      await expect(
        subscriptionService.updateNotificationSettings('123', notificationData)
      ).rejects.toThrow('API Error');
    });
  });

  describe('delete', () => {
    it('should delete subscription', async () => {
      (api as any).mockResolvedValue({});

      await subscriptionService.delete('123');

      expect(api).toHaveBeenCalledWith('/subscriptions/123', {
        method: 'DELETE',
      });
    });
  });

  describe('getUsageData', () => {
    it('should fetch usage data', async () => {
      const mockUsageData = {
        usageData: [
          {
            timeframe: '2024-01-01',
            usage: 100,
            cost: 50.5,
            co2Saved: 25.2,
          },
        ],
        totalUsage: 100,
        totalCost: 50.5,
      };

      (api as any).mockResolvedValue(mockUsageData);

      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const granularity = 'daily';
      const compareTo = ['previous-month'];

      const result = await subscriptionService.getUsageData('123', startDate, endDate, granularity, compareTo);

      expect(api).toHaveBeenCalledWith(
        expect.stringContaining('/subscriptions/123/usage'),
        { method: 'GET' }
      );
      expect(result.usageData).toBeDefined();
    });

    it('should handle API errors when fetching usage data', async () => {
      const error = new Error('Usage data fetch failed');
      (api as any).mockRejectedValue(error);

      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      await expect(
        subscriptionService.getUsageData('123', startDate, endDate, 'daily', [])
      ).rejects.toThrow('Usage data fetch failed');
      expect(mockConsoleError).toHaveBeenCalledWith('Error fetching usage data:', error);
    });
  });

  describe('getRegions', () => {
    it('should fetch regions and cache them', async () => {
      const mockRegions = [
        { id: 'region-1', name: 'Region 1' },
        { id: 'region-2', name: 'Region 2' },
      ];

      (api as any).mockResolvedValue(mockRegions);

      const result = await subscriptionService.getRegions();

      expect(api).toHaveBeenCalledWith('/region', {
        method: 'GET',
      });
      expect(result).toEqual(mockRegions);
    });
  });

  describe('getRegionById', () => {
    it('should return region by id', async () => {
      const mockRegions = [
        { id: 'region-1', name: 'Region 1' },
        { id: 'region-2', name: 'Region 2' },
      ];

      (api as any).mockResolvedValue(mockRegions);

      const result = await subscriptionService.getRegionById('region-1');

      expect(result).toEqual({ id: 'region-1', name: 'Region 1' });
    });

    it('should return undefined for non-existent region', async () => {
      const mockRegions = [
        { id: 'region-1', name: 'Region 1' },
      ];

      (api as any).mockResolvedValue(mockRegions);

      const result = await subscriptionService.getRegionById('non-existent');

      expect(result).toBeUndefined();
    });

    it('should return undefined for empty id', async () => {
      const result = await subscriptionService.getRegionById('');

      expect(result).toBeUndefined();
    });
  });

  describe('exportUsageData', () => {
    it('should export usage data', async () => {
      const mockExportResponse = {
        downloadUrl: 'https://example.com/download/usage-data.xlsx',
        fileName: 'usage-data.xlsx',
      };

      (api as any).mockResolvedValue(mockExportResponse);

      const result = await subscriptionService.exportUsageData(
        '123',
        new Date('2024-01-01'),
        new Date('2024-01-31'),
        'daily',
        [],
        'xlsx',
        'en'
      );

      expect(api).toHaveBeenCalledWith(
        expect.stringContaining('/subscriptions/123/usage/export'),
        { method: 'POST' }
      );
      expect(result).toEqual(mockExportResponse);
    });
  });

  describe('exportOutageData', () => {
    it('should export outage data', async () => {
      const mockExportResponse = {
        downloadUrl: 'https://example.com/download/outages.pdf',
        fileName: 'outages.pdf',
      };

      (api as any).mockResolvedValue(mockExportResponse);

      const result = await subscriptionService.exportOutageData('123', new Date(), new Date(), 'pdf', 'en');

      expect(api).toHaveBeenCalledWith(
        expect.stringContaining('/subscriptions/123/outages/export'),
        { method: 'POST' }
      );
      expect(result).toEqual(mockExportResponse);
    });
  });

  describe('getRegionsPaginated', () => {
    it('should fetch paginated regions without search', async () => {
      // Mock getRegions to return regions
      const mockRegions = [
        { id: 'region-1', name: 'Region 1' },
        { id: 'region-2', name: 'Region 2' },
        { id: 'region-3', name: 'Region 3' },
      ];

      // Mock the getRegions call within getRegionsPaginated
      const getRegionsSpy = vi.spyOn(subscriptionService, 'getRegions').mockResolvedValue(mockRegions);

      const result = await subscriptionService.getRegionsPaginated(0, 2);

      expect(getRegionsSpy).toHaveBeenCalled();
      expect(result).toEqual({
        items: [
          { id: 'region-1', name: 'Region 1' },
          { id: 'region-2', name: 'Region 2' },
        ],
        totalPages: 2,
        totalElements: 3,
        hasNextPage: true,
      });

      getRegionsSpy.mockRestore();
    });

    it('should fetch paginated regions with search', async () => {
      const mockRegions = [
        { id: 'region-1', name: 'North Region' },
        { id: 'region-2', name: 'South Region' },
        { id: 'region-3', name: 'East City' },
      ];

      const getRegionsSpy = vi.spyOn(subscriptionService, 'getRegions').mockResolvedValue(mockRegions);

      const result = await subscriptionService.getRegionsPaginated(0, 10, 'region');

      expect(result.items).toHaveLength(2);
      expect(result.items[0].name).toContain('Region');
      expect(result.items[1].name).toContain('Region');

      getRegionsSpy.mockRestore();
    });

    it('should handle empty search results', async () => {
      const mockRegions = [
        { id: 'region-1', name: 'North Region' },
        { id: 'region-2', name: 'South Region' },
      ];

      const getRegionsSpy = vi.spyOn(subscriptionService, 'getRegions').mockResolvedValue(mockRegions);

      const result = await subscriptionService.getRegionsPaginated(0, 10, 'nonexistent');

      expect(result.items).toHaveLength(0);
      expect(result.totalElements).toBe(0);
      expect(result.hasNextPage).toBe(false);

      getRegionsSpy.mockRestore();
    });
  });
});