import { describe, it, expect } from 'vitest';
import { Api } from './enums';

describe('Api enum', () => {
  it('should have correct API endpoints', () => {
    expect(Api.subscriptions).toBe('/subscriptions');
    expect(Api.auth).toBe('/auth');
    expect(Api.regions).toBe('/region');
    expect(Api.notifications).toBe('/notifications');
    expect(Api.settings).toBe('/setting');
  });

  it('should have all expected enum keys', () => {
    const expectedKeys = ['subscriptions', 'auth', 'regions', 'notifications', 'settings'];
    const actualKeys = Object.keys(Api);
    
    expect(actualKeys).toEqual(expect.arrayContaining(expectedKeys));
    expect(actualKeys).toHaveLength(expectedKeys.length);
  });

  it('should have consistent endpoint format', () => {
    const endpoints = Object.values(Api);
    
    endpoints.forEach(endpoint => {
      expect(endpoint).toMatch(/^\/[a-z]+[a-z]*$/);
    });
  });
});
