/**
 * @vitest-environment jsdom
 */
import { describe, it, expect } from 'vitest';
import { buildApiQueryString, type ApiQueryParams } from './types';

describe('API Types Utilities', () => {
  describe('buildApiQueryString', () => {
    it('should build query string with default pagination when no params provided', () => {
      const params: ApiQueryParams = {};
      const result = buildApiQueryString(params);
      
      expect(result).toBe('pageNumber=1&pageSize=10');
    });

    it('should build query string with custom pagination parameters', () => {
      const params: ApiQueryParams = {
        pageNumber: 2,
        pageSize: 20,
      };
      const result = buildApiQueryString(params);
      
      expect(result).toBe('pageNumber=2&pageSize=20');
    });

    it('should enforce minimum page number of 1', () => {
      const params: ApiQueryParams = {
        pageNumber: 0,
        pageSize: 10,
      };
      const result = buildApiQueryString(params);
      
      expect(result).toBe('pageNumber=1&pageSize=10');
    });

    it('should handle negative page numbers', () => {
      const params: ApiQueryParams = {
        pageNumber: -5,
        pageSize: 10,
      };
      const result = buildApiQueryString(params);
      
      expect(result).toBe('pageNumber=1&pageSize=10');
    });

    it('should include orderBy parameter when provided', () => {
      const params: ApiQueryParams = {
        pageNumber: 1,
        pageSize: 10,
        orderBy: 'createdAt,desc',
      };
      const result = buildApiQueryString(params);
      
      expect(result).toBe('pageNumber=1&pageSize=10&orderBy=createdAt%2Cdesc');
    });

    it('should handle equality filters with string values', () => {
      const params: ApiQueryParams = {
        pageNumber: 1,
        pageSize: 10,
        'filter:eq': {
          status: 'active',
          category: 'technical',
        },
      };
      const result = buildApiQueryString(params);
      
      expect(result).toContain('filter%3Aeq=status%3Dactive');
      expect(result).toContain('filter%3Aeq=category%3Dtechnical');
    });

    it('should handle equality filters with array values', () => {
      const params: ApiQueryParams = {
        pageNumber: 1,
        pageSize: 10,
        'filter:eq': {
          status: ['active', 'pending'],
        },
      };
      const result = buildApiQueryString(params);
      
      expect(result).toContain('filter%3Aeq=status%3Dactive');
      expect(result).toContain('filter%3Aeq=status%3Dpending');
    });

    it('should handle between filters', () => {
      const params: ApiQueryParams = {
        pageNumber: 1,
        pageSize: 10,
        'filter:bw': {
          price: [100, 500],
          date: ['2023-01-01', '2023-12-31'],
        },
      };
      const result = buildApiQueryString(params);
      
      expect(result).toContain('filter%3Abw=price%3D100%2C500');
      expect(result).toContain('filter%3Abw=date%3D2023-01-01%2C2023-12-31');
    });

    it('should handle contains filters', () => {
      const params: ApiQueryParams = {
        pageNumber: 1,
        pageSize: 10,
        'filter:ct': {
          title: 'search term',
          description: 'another term',
        },
      };
      const result = buildApiQueryString(params);
      
      expect(result).toContain('filter%3Act=title%3Dsearch%2520term');
      expect(result).toContain('filter%3Act=description%3Danother%2520term');
    });

    it('should handle like filters', () => {
      const params: ApiQueryParams = {
        pageNumber: 1,
        pageSize: 10,
        'filter:lk': {
          name: 'john%',
          email: '%@example.com',
        },
      };
      const result = buildApiQueryString(params);
      
      expect(result).toContain('filter%3Alk=name%3Djohn%2525');
      expect(result).toContain('filter%3Alk=email%3D%2525%2540example.com');
    });

    it('should handle complex query with all filter types', () => {
      const params: ApiQueryParams = {
        pageNumber: 3,
        pageSize: 25,
        orderBy: 'updatedAt,asc',
        'filter:eq': {
          status: 'active',
          priority: ['high', 'medium'],
        },
        'filter:bw': {
          price: [50, 200],
        },
        'filter:ct': {
          title: 'important',
        },
        'filter:lk': {
          author: 'admin%',
        },
      };
      const result = buildApiQueryString(params);
      
      expect(result).toContain('pageNumber=3');
      expect(result).toContain('pageSize=25');
      expect(result).toContain('orderBy=updatedAt%2Casc');
      expect(result).toContain('filter%3Aeq=status%3Dactive');
      expect(result).toContain('filter%3Aeq=priority%3Dhigh');
      expect(result).toContain('filter%3Aeq=priority%3Dmedium');
      expect(result).toContain('filter%3Abw=price%3D50%2C200');
      expect(result).toContain('filter%3Act=title%3Dimportant');
      expect(result).toContain('filter%3Alk=author%3Dadmin%25');
    });

    it('should properly encode special characters', () => {
      const params: ApiQueryParams = {
        'filter:eq': {
          'special-key': 'value with spaces & symbols!',
        },
        'filter:ct': {
          'another+key': 'search/term',
        },
      };
      const result = buildApiQueryString(params);
      
      expect(result).toContain('special-key%3Dvalue%2520with%2520spaces%2520%2526%2520symbols%21');
      expect(result).toContain('another%252Bkey%3Dsearch%252Fterm');
    });

    it('should handle empty filter objects', () => {
      const params: ApiQueryParams = {
        pageNumber: 1,
        pageSize: 10,
        'filter:eq': {},
        'filter:bw': {},
        'filter:ct': {},
        'filter:lk': {},
      };
      const result = buildApiQueryString(params);
      
      expect(result).toBe('pageNumber=1&pageSize=10');
    });

    it('should handle undefined filter values gracefully', () => {
      const params: ApiQueryParams = {
        pageNumber: 1,
        pageSize: 10,
        orderBy: undefined,
      };
      const result = buildApiQueryString(params);
      
      expect(result).toBe('pageNumber=1&pageSize=10');
    });
  });
});
