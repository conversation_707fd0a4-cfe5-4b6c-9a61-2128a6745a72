export interface PagedResponse<T> {
  content: T[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    sort: {
      sorted: boolean;
      empty: boolean;
      unsorted: boolean;
    };
    offset: number;
    paged: boolean;
    unpaged: boolean;
  };
  last: boolean;
  totalPages: number;
  totalElements: number;
  size: number;
  number: number;
  sort: {
    sorted: boolean;
    empty: boolean;
    unsorted: boolean;
  };
  first: boolean;
  numberOfElements: number;
  empty: boolean;
}

export interface ApiQueryParams {
  pageNumber?: number;
  pageSize?: number;

  orderBy?: string;

  "filter:eq"?: Record<string, string | string[]>;
  "filter:bw"?: Record<string, [number | string, number | string]>;
  "filter:ct"?: Record<string, string>;
  "filter:lk"?: Record<string, string>;
}

export function buildApiQueryString(params: ApiQueryParams): string {
  const queryParams = new URLSearchParams();

  if (params.pageNumber !== undefined) {
    queryParams.append("pageNumber", Math.max(params.pageNumber, 1).toString());
  } else {
    queryParams.append("pageNumber", "1");
  }

  if (params.pageSize !== undefined) {
    queryParams.append("pageSize", params.pageSize.toString());
  } else {
    queryParams.append("pageSize", "10");
  }

  if (params.orderBy) {
    queryParams.append("orderBy", params.orderBy);
  }

  if (params["filter:eq"]) {
    Object.entries(params["filter:eq"]).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach((val) => {
          queryParams.append("filter:eq", `${encodeURIComponent(key)}=${encodeURIComponent(val)}`);
        });
      } else {
        queryParams.append("filter:eq", `${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    });
  }

  if (params["filter:bw"]) {
    Object.entries(params["filter:bw"]).forEach(([key, [min, max]]) => {
      queryParams.append("filter:bw", `${encodeURIComponent(key)}=${encodeURIComponent(min)},${encodeURIComponent(max)}`);
    });
  }

  if (params["filter:ct"]) {
    Object.entries(params["filter:ct"]).forEach(([key, value]) => {
      queryParams.append("filter:ct", `${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    });
  }

  if (params["filter:lk"]) {
    Object.entries(params["filter:lk"]).forEach(([key, value]) => {
      queryParams.append("filter:lk", `${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    });
  }

  return queryParams.toString();
}
