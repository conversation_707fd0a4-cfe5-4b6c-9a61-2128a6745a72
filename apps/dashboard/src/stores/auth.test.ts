/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import Cookies from 'js-cookie';
import { useAuthStore, useAuth, type AuthUser } from './auth';

// Mock js-cookie
vi.mock('js-cookie', () => ({
  default: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn(),
  },
}));

const mockCookies = Cookies as any;

describe('Auth Store', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockCookies.get.mockReturnValue(undefined);
  });

  describe('useAuthStore', () => {
    it('should initialize with empty state when no cookie exists', () => {
      mockCookies.get.mockReturnValue(undefined);

      const { result } = renderHook(() => useAuthStore());

      expect(result.current.auth.user).toBeNull();
      expect(result.current.auth.accessToken).toBe('');
    });

    it('should set user correctly', () => {
      const { result } = renderHook(() => useAuthStore());

      const mockUser: AuthUser = {
        id: '123',
        permissions: [{ scope: 'read' }],
        email: '<EMAIL>',
        phone: null,
        tckn: null,
        firstName: 'John',
        lastName: 'Doe',
      };

      act(() => {
        result.current.auth.setUser(mockUser);
      });

      expect(result.current.auth.user).toEqual(mockUser);
    });

    it('should set access token and save to cookie', () => {
      const { result } = renderHook(() => useAuthStore());

      const token = 'new-access-token';

      act(() => {
        result.current.auth.setAccessToken(token);
      });

      expect(result.current.auth.accessToken).toBe(token);
      expect(mockCookies.set).toHaveBeenCalledWith('sess', JSON.stringify(token));
    });

    it('should reset access token and remove cookie', () => {
      const { result } = renderHook(() => useAuthStore());

      // Set token first
      act(() => {
        result.current.auth.setAccessToken('some-token');
      });

      // Reset token
      act(() => {
        result.current.auth.resetAccessToken();
      });

      expect(result.current.auth.accessToken).toBe('');
      expect(mockCookies.remove).toHaveBeenCalledWith('sess');
    });

    it('should reset both user and access token', () => {
      const { result } = renderHook(() => useAuthStore());

      const mockUser: AuthUser = {
        id: '123',
        permissions: [{ scope: 'read' }],
        email: '<EMAIL>',
        phone: null,
        tckn: null,
        firstName: 'John',
        lastName: 'Doe',
      };

      // Set user and token
      act(() => {
        result.current.auth.setUser(mockUser);
        result.current.auth.setAccessToken('some-token');
      });

      expect(result.current.auth.user).toEqual(mockUser);
      expect(result.current.auth.accessToken).toBe('some-token');

      // Reset everything
      act(() => {
        result.current.auth.reset();
      });

      expect(result.current.auth.user).toBeNull();
      expect(result.current.auth.accessToken).toBe('');
      expect(mockCookies.remove).toHaveBeenCalledWith('sess');
    });
  });

  describe('useAuth hook', () => {
    it('should return auth state', () => {
      const { result } = renderHook(() => useAuth());

      expect(result.current).toHaveProperty('user');
      expect(result.current).toHaveProperty('setUser');
      expect(result.current).toHaveProperty('accessToken');
      expect(result.current).toHaveProperty('setAccessToken');
      expect(result.current).toHaveProperty('resetAccessToken');
      expect(result.current).toHaveProperty('reset');
    });

    it('should reflect state changes', () => {
      const { result } = renderHook(() => useAuth());

      const mockUser: AuthUser = {
        id: '123',
        permissions: [{ scope: 'read' }],
        email: '<EMAIL>',
        phone: null,
        tckn: null,
        firstName: 'John',
        lastName: 'Doe',
      };

      act(() => {
        result.current.setUser(mockUser);
      });

      expect(result.current.user).toEqual(mockUser);
    });
  });
});
