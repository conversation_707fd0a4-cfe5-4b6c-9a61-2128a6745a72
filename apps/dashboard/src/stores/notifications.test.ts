import { describe, it, expect, beforeEach } from 'vitest';
import { useNotificationStore } from './notifications';

describe('Notifications Store', () => {
  beforeEach(() => {
    // Reset the store before each test
    useNotificationStore.setState({ count: 0 });
  });

  it('should initialize with default values', () => {
    const state = useNotificationStore.getState();
    expect(state.count).toBe(0);
  });

  it('should update count when setCount is called', () => {
    // Initial state check
    expect(useNotificationStore.getState().count).toBe(0);
    
    // Update state
    useNotificationStore.getState().setCount(5);
    
    // Verify state was updated
    expect(useNotificationStore.getState().count).toBe(5);
  });

  it('should update count to zero', () => {
    // Set initial count to non-zero
    useNotificationStore.setState({ count: 10 });
    expect(useNotificationStore.getState().count).toBe(10);
    
    // Update count to zero
    useNotificationStore.getState().setCount(0);
    
    // Verify count was updated to zero
    expect(useNotificationStore.getState().count).toBe(0);
  });

  it('should handle negative counts', () => {
    // Update to negative count
    useNotificationStore.getState().setCount(-5);
    
    // Verify state was updated to negative count
    expect(useNotificationStore.getState().count).toBe(-5);
  });

  it('should handle large notification counts', () => {
    // Update to a large number
    useNotificationStore.getState().setCount(9999);
    
    // Verify state was updated to the large number
    expect(useNotificationStore.getState().count).toBe(9999);
  });

  it('should not change other state properties when setCount is called', () => {
    // Add a custom property to the state
    const testStore = useNotificationStore as any;
    testStore.setState({ testProp: 'test' });
    
    // Update only the count state
    useNotificationStore.getState().setCount(3);
    
    // Verify that our custom property is still there
    expect(testStore.getState().testProp).toBe('test');
    // And count was updated
    expect(testStore.getState().count).toBe(3);
  });
});
