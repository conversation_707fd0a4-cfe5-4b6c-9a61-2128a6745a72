import { describe, it, expect, beforeEach } from 'vitest';
import { useSidebarStore } from './sidebar';

describe('Sidebar Store', () => {
  beforeEach(() => {
    // Reset the store before each test
    useSidebarStore.setState({ collapsed: false });
  });

  it('should initialize with default values', () => {
    const state = useSidebarStore.getState();
    expect(state.collapsed).toBe(false);
  });

  it('should update collapsed state when setCollapsed is called', () => {
    // Initial state check
    expect(useSidebarStore.getState().collapsed).toBe(false);
    
    // Update state
    useSidebarStore.getState().setCollapsed(true);
    
    // Verify state was updated
    expect(useSidebarStore.getState().collapsed).toBe(true);
  });

  it('should toggle collapsed state multiple times', () => {
    const { setCollapsed } = useSidebarStore.getState();
    
    // Start with default (false)
    expect(useSidebarStore.getState().collapsed).toBe(false);
    
    // Toggle to true
    setCollapsed(true);
    expect(useSidebarStore.getState().collapsed).toBe(true);
    
    // Toggle back to false
    setCollapsed(false);
    expect(useSidebarStore.getState().collapsed).toBe(false);
    
    // Toggle back to true
    setCollapsed(true);
    expect(useSidebarStore.getState().collapsed).toBe(true);
  });

  it('should not change other state properties when setCollapsed is called', () => {
    // Add a custom property to the state
    const testStore = useSidebarStore as any;
    testStore.setState({ testProp: 'test' });
    
    // Update only the collapsed state
    useSidebarStore.getState().setCollapsed(true);
    
    // Verify that our custom property is still there
    expect(testStore.getState().testProp).toBe('test');
    // And collapsed was updated
    expect(testStore.getState().collapsed).toBe(true);
  });
});
