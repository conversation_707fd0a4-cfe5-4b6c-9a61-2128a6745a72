// Mock implementation of user-event compatible with Vitest
import { vi } from 'vitest';

const userEventMethods = {
  click: vi.fn(),
  dblClick: vi.fn(),
  type: vi.fn(),
  clear: vi.fn(),
  tab: vi.fn(),
  keyboard: vi.fn(),
  paste: vi.fn(),
  hover: vi.fn(),
  unhover: vi.fn(),
  selectOptions: vi.fn(),
  deselectOptions: vi.fn(),
  upload: vi.fn()
};

// Setup all major userEvent methods as vitest mock functions
export default {
  setup: vi.fn(() => userEventMethods),
  ...userEventMethods
};

