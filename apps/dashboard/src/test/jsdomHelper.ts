/**
 * This file will be imported directly into the test files rather than being used as a setupFile
 * to bypass the jsdomimport error. This is just a workaround for a known Vitest issue.
 */
export function setupJsdom() {
  // Fix for jsdomimport error in tests
  if (typeof global.TextEncoder === 'undefined') {
    const util = require('util');
    global.TextEncoder = util.TextEncoder;
    global.TextDecoder = util.TextDecoder;
  }

  // Provide missing browser APIs that might be used in tests
  if (typeof window !== 'undefined' && typeof window.URL.createObjectURL === 'undefined') {
    Object.defineProperty(window.URL, 'createObjectURL', { value: () => 'mock://object.url' });
  }
}
