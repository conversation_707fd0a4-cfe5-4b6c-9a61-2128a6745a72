// This file is used to set up the testing environment properly for JSDOM
// It can help avoid JSDOM import errors that sometimes occur with Vitest

// Configure JSDOM environment explicitly
if (typeof global.TextEncoder === 'undefined') {
  const { TextEncoder, TextDecoder } = require('util');
  global.TextEncoder = TextEncoder;
  global.TextDecoder = TextDecoder;
}

// Provide missing browser APIs that might be used in tests
if (typeof window.URL.createObjectURL === 'undefined') {
  Object.defineProperty(window.URL, 'createObjectURL', { value: () => 'mock://object.url' });
}

// Fix for jsdomimport error
if (typeof process !== 'undefined') {
  process.env.NODE_ENV = 'test';
}
