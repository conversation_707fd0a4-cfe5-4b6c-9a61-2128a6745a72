// mockRadixModules.ts
import { vi } from 'vitest';
import React from 'react';

// Setup mocks for Radix UI packages
vi.mock('@radix-ui/react-primitive', () => ({
  Primitive: vi.fn(({ children, ...props }: any) => React.createElement('div', props, children))
}));

vi.mock('@radix-ui/react-dialog', () => ({
  Root: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Trigger: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Portal: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Content: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Title: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Description: vi.fn(({ children }: any) => React.createElement('div', null, children))
}));

vi.mock('@radix-ui/react-id', () => ({
  useId: vi.fn(() => 'mock-id')
}));

// Setup mock for cmdk
vi.mock('cmdk', () => {
  const Command = ({ children }: any) => React.createElement('div', { 'data-testid': 'cmdk-command' }, children);
  Command.List = ({ children }: any) => React.createElement('div', { 'data-testid': 'cmdk-list' }, children);
  Command.Input = (props: any) => React.createElement('input', { 'data-testid': 'cmdk-input', ...props });
  Command.Item = ({ children }: any) => React.createElement('div', { 'data-testid': 'cmdk-item' }, children);
  Command.Group = ({ children }: any) => React.createElement('div', { 'data-testid': 'cmdk-group' }, children);
  Command.Separator = () => React.createElement('div', { 'data-testid': 'cmdk-separator' });
  Command.Empty = ({ children }: any) => React.createElement('div', { 'data-testid': 'cmdk-empty' }, children);
  
  return {
    Command
  };
});

// Other mocks
vi.mock('@radix-ui/react-popover', () => ({
  Root: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Trigger: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Content: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Arrow: vi.fn(() => React.createElement('div'))
}));

vi.mock('@radix-ui/react-dropdown-menu', () => ({
  Root: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Trigger: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Portal: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Content: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Item: vi.fn(({ children }: any) => React.createElement('div', null, children)),
  Separator: vi.fn(() => React.createElement('div'))
}));

vi.mock('@radix-ui/react-slot', () => ({
  Slot: vi.fn(({ children }: any) => children)
}));
