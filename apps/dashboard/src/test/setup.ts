// Simple test setup file for vitest
import { expect, vi, beforeEach, afterEach } from 'vitest';
import * as matchers from '@testing-library/jest-dom/matchers';
import '@testing-library/jest-dom';

// Polyfill for Promise.withResolvers (needed for PDF.js)
if (!Promise.withResolvers) {
  Promise.withResolvers = function<T>() {
    let resolve: (value: T | PromiseLike<T>) => void;
    let reject: (reason?: any) => void;
    const promise = new Promise<T>((res, rej) => {
      resolve = res;
      reject = rej;
    });
    return { promise, resolve: resolve!, reject: reject! };
  };
}

// Setup localStorage mock if not already set
if (typeof window !== 'undefined' && !window.localStorage) {
  Object.defineProperty(window, 'localStorage', {
    value: {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    },
    writable: true
  });
}

// Setup location mock if not already set
if (typeof window !== 'undefined' && !window.location) {
  Object.defineProperty(window, 'location', {
    value: {
      replace: vi.fn(),
      href: 'http://localhost',
    },
    writable: true
  });
}

// Handle virtual imports
vi.mock('virtual:i18next-loader', () => {
  return {
    default: {
      en: {
        translation: {
          hello: 'Hello',
          nested: {
            key: 'Nested Key'
          }
        }
      },
      tr: {
        translation: {
          hello: 'Merhaba',
          nested: {
            key: 'İç içe anahtar'
          }
        }
      }
    }
  };
});

if (typeof global.TextEncoder === 'undefined') {
  const util = require('util');
  global.TextEncoder = util.TextEncoder;
  global.TextDecoder = util.TextDecoder;
}

// Set the environment explicitly
process.env.NODE_ENV = 'test';

// Note: Vitest globals are enabled in config, so no need to assign them manually

// Make sure matchers is not null before extending
if (matchers) {
  expect.extend(matchers);
}

// Mock path aliases that frequently cause issues
vi.mock('@/constants/sidebar-data', () => ({
  sidebarData: [],
}));

vi.mock('@/services/api', () => ({
  default: {
    get: vi.fn().mockResolvedValue({}),
    post: vi.fn().mockResolvedValue({}),
    put: vi.fn().mockResolvedValue({}),
    delete: vi.fn().mockResolvedValue({}),
  }
}));

vi.mock('@/features/notifications/components/sidebar-number', () => ({
  SidebarNumber: vi.fn().mockReturnValue(null),
}));

vi.mock('@/utils/use-disabled', () => ({
  useDisabled: vi.fn().mockReturnValue(false),
}));

// Clean up mocks after each test
afterEach(() => {
  vi.resetAllMocks();
});

if (typeof document === 'undefined') {
  global.document = window.document;
}

// Mock window object for browser APIs
if (typeof window !== 'undefined') {
  // Only modify window if it exists
  Object.defineProperties(window, {
    matchMedia: {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))
    },
    localStorage: {
      writable: true,
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn()
      }
    },
    location: {
      writable: true,
      value: {
        href: 'http://localhost:3000/',
        origin: 'http://localhost:3000',
        protocol: 'http:',
        host: 'localhost:3000',
        hostname: 'localhost',
        port: '3000',
        pathname: '/',
        search: '',
        hash: '',
        assign: vi.fn(),
        replace: vi.fn(),
        reload: vi.fn()
      }
    },
    innerWidth: {
      writable: true,
      value: 1024
    },
    innerHeight: {
      writable: true,
      value: 768
    }
  });
}

// Setup mocks for i18n
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: any) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn()
    }
  })
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn()
  }
}));

// Mock image assets
vi.mock('@mass/shared/assets/auth/slider-1.png', () => ({ default: '/mock-slider-1.png' }));
vi.mock('@mass/shared/assets/auth/slider-2.png', () => ({ default: '/mock-slider-2.png' }));
vi.mock('@mass/shared/assets/auth/slider-3.png', () => ({ default: '/mock-slider-3.png' }));
vi.mock('@mass/shared/assets/auth/slider-1-card.png', () => ({ default: '/mock-slider-1-card.png' }));
vi.mock('@mass/shared/assets/auth/slider-1-card-1.png', () => ({ default: '/mock-slider-1-card-1.png' }));
vi.mock('@mass/shared/assets/auth/slider-2-card.png', () => ({ default: '/mock-slider-2-card.png' }));
vi.mock('@mass/shared/assets/auth/slider-2-card-1.png', () => ({ default: '/mock-slider-2-card-1.png' }));
vi.mock('@mass/shared/assets/auth/slider-3-card.png', () => ({ default: '/mock-slider-3-card.png' }));
vi.mock('@mass/shared/assets/auth/slider-3-card-1.png', () => ({ default: '/mock-slider-3-card-1.png' }));
vi.mock('@mass/shared/assets/auth/slider-3-card-2.png', () => ({ default: '/mock-slider-3-card-2.png' }));
vi.mock('@mass/shared/assets/auth/slider-1-mobile.png', () => ({ default: '/mock-slider-1-mobile.png' }));
vi.mock('@mass/shared/assets/auth/slider-2-mobile.png', () => ({ default: '/mock-slider-2-mobile.png' }));
vi.mock('@mass/shared/assets/auth/slider-3-mobile.png', () => ({ default: '/mock-slider-3-mobile.png' }));