import { useEffect } from 'react';
import { useManyGlobalSettings } from '@/data/queries';

export function useIsDisabled(keys: string[]): boolean {
  const { data } = useManyGlobalSettings(keys);
  return !!data && keys.some((k) => !!data[k]);
}

export function useDisabled(keys: string[], redirect = '/'): void {
  const disabled = useIsDisabled(keys);
  useEffect(() => {
    if (disabled) window.location.replace(redirect);
  }, [disabled, redirect]);
}

export default { useIsDisabled, useDisabled };
