import { describe, it, expect } from 'vitest'

/**
 * Array utility functions tests
 */
describe('Array utilities', () => {
  it('should handle array operations', () => {
    const arr = [1, 2, 3, 4, 5]
    expect(arr.length).toBe(5)
    expect(arr[0]).toBe(1)
    expect(arr[arr.length - 1]).toBe(5)
  })

  it('should handle array methods', () => {
    const arr = [1, 2, 3]
    expect(arr.map(x => x * 2)).toEqual([2, 4, 6])
    expect(arr.filter(x => x > 1)).toEqual([2, 3])
    expect(arr.reduce((sum, x) => sum + x, 0)).toBe(6)
  })

  it('should handle array search', () => {
    const arr = ['apple', 'banana', 'cherry']
    expect(arr.indexOf('banana')).toBe(1)
    expect(arr.includes('cherry')).toBe(true)
    expect(arr.find(item => item.startsWith('a'))).toBe('apple')
  })

  it('should handle array modification', () => {
    const arr = [1, 2, 3]
    const newArr = [...arr, 4]
    expect(newArr).toEqual([1, 2, 3, 4])
    
    const sliced = arr.slice(1, 3)
    expect(sliced).toEqual([2, 3])
  })
})
