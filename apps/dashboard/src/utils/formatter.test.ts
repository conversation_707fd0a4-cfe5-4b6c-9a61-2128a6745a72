import { describe, it, expect } from 'vitest';
import { formatCurrency, formatDate } from './formatter';

describe('Formatter utilities', () => {
  describe('formatCurrency', () => {
    it('should format USD by default', () => {
      expect(formatCurrency(1000)).toBe('$1,000.00');
    });

    it('should format with specified currency', () => {
      expect(formatCurrency(1000, 'EUR')).toBe('€1,000.00');
    });

    it('should handle zero value', () => {
      expect(formatCurrency(0)).toBe('$0.00');
    });

    it('should handle negative values', () => {
      expect(formatCurrency(-1234.56)).toBe('-$1,234.56');
    });

    it('should handle decimal values', () => {
      expect(formatCurrency(1234.56)).toBe('$1,234.56');
    });

    it('should handle large numbers', () => {
      expect(formatCurrency(1234567.89)).toBe('$1,234,567.89');
    });

    it('should handle small decimal values', () => {
      expect(formatCurrency(0.99)).toBe('$0.99');
    });

    it('should format with different currencies', () => {
      expect(formatCurrency(1000, 'GBP')).toBe('£1,000.00');
      expect(formatCurrency(1000, 'JPY')).toBe('¥1,000');
    });
  });

  describe('formatDate', () => {
    it('should format date in US format', () => {
      const date = new Date('2023-01-15');
      expect(formatDate(date)).toMatch(/1\/15\/2023/);
    });

    it('should handle different months', () => {
      const date = new Date('2023-12-25');
      expect(formatDate(date)).toMatch(/12\/25\/2023/);
    });

    it('should handle leap year dates', () => {
      const date = new Date('2024-02-29');
      expect(formatDate(date)).toMatch(/2\/29\/2024/);
    });

    it('should handle current date', () => {
      const date = new Date();
      const result = formatDate(date);
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
      expect(result).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
    });

    it('should handle different years', () => {
      const date = new Date('2022-06-15');
      expect(formatDate(date)).toMatch(/6\/15\/2022/);
    });
  });
});
