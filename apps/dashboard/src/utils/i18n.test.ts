/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';

vi.mock('react-i18next', () => ({
  initReactI18next: {
    type: '3rdParty',
    init: vi.fn(() => ({
    }))
  }
}));

// Mock the virtual module before importing i18n
vi.mock('virtual:i18next-loader', () => ({
  default: {
    en: {
      translation: {
        hello: 'Hello',
        nested: { key: 'Nested Key' }
      }
    },
    tr: {
      translation: {
        hello: 'Merhaba',
        nested: { key: 'İç içe anahtar' }
      }
    }
  }
}));

// Mock localStorage before importing i18n
const localStorageMock = {
  getItem: vi.fn((key) => key === 'language' ? 'tr' : null),
  setItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true
});

// Now import i18n
import i18n, { changeLanguage, hasTranslation } from './i18n';

describe('i18n utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    i18n.changeLanguage('tr');
  });

  it('should change language and update localStorage', async () => {
    await changeLanguage('en');
    expect(i18n.language).toBe('en');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('language', 'en');
  });

  it('should detect if translation exists', () => {
    i18n.changeLanguage('en');
    expect(hasTranslation('hello')).toBe(true);
    expect(hasTranslation('nonexistent')).toBe(false);
  });

  it('should detect nested translation keys', () => {
    i18n.changeLanguage('en');
    expect(hasTranslation('nested.key')).toBe(true);
  });

  it('should use fallback language when translation is missing', async () => {
    i18n.addResource('en', 'translation', 'fallbackOnly', 'Fallback Content');
    await changeLanguage('tr');
    expect(i18n.t('fallbackOnly')).toBe('Fallback Content');
  });

  it('should interpolate values correctly', () => {
    i18n.addResource('en', 'translation', 'welcome', 'Welcome, {{name}}!');
    i18n.changeLanguage('en');
    expect(i18n.t('welcome', { name: 'User' })).toBe('Welcome, User!');
  });
});