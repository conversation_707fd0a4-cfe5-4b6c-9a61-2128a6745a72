import { describe, it, expect } from 'vitest'

/**
 * Number utility functions tests
 */
describe('Number utilities', () => {
  it('should handle basic number operations', () => {
    expect(1 + 1).toBe(2)
    expect(10 - 5).toBe(5)
    expect(3 * 4).toBe(12)
    expect(8 / 2).toBe(4)
  })

  it('should handle number validation', () => {
    expect(Number.isInteger(42)).toBe(true)
    expect(Number.isInteger(42.5)).toBe(false)
    expect(Number.isNaN(NaN)).toBe(true)
    expect(Number.isFinite(Infinity)).toBe(false)
  })

  it('should handle number formatting', () => {
    const num = 1234.567
    expect(num.toFixed(2)).toBe('1234.57')
    expect(Math.round(num)).toBe(1235)
    expect(Math.floor(num)).toBe(1234)
    expect(Math.ceil(num)).toBe(1235)
  })

  it('should handle random numbers', () => {
    const random = Math.random()
    expect(random >= 0 && random < 1).toBe(true)
    
    const randomInt = Math.floor(Math.random() * 10)
    expect(randomInt >= 0 && randomInt < 10).toBe(true)
  })
})
