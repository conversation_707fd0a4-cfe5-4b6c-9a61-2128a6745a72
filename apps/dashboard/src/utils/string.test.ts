import { describe, it, expect } from 'vitest'

/**
 * String utility functions tests
 */
describe('String utilities', () => {
  it('should handle string operations', () => {
    const str = 'hello world'
    expect(str.charAt(0)).toBe('h')
    expect(str.length).toBe(11)
    expect(str.toUpperCase()).toBe('HELLO WORLD')
  })

  it('should handle string manipulation', () => {
    const str = '  test string  '
    expect(str.trim()).toBe('test string')
    expect(str.replace(/\s+/g, ' ')).toBe(' test string ')
  })

  it('should split and join strings', () => {
    const str = 'apple,banana,cherry'
    const arr = str.split(',')
    expect(arr).toEqual(['apple', 'banana', 'cherry'])
    expect(arr.join(' | ')).toBe('apple | banana | cherry')
  })

  it('should check string conditions', () => {
    expect('<EMAIL>'.includes('@')).toBe(true)
    expect('password123'.length >= 8).toBe(true)
    expect('Hello World'.startsWith('Hello')).toBe(true)
    expect('Hello World'.endsWith('World')).toBe(true)
  })
})
