/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach, Mock } from 'vitest';
import { renderHook } from '@testing-library/react';





vi.mock('./use-disabled', async (importOriginal) => {
  
  const mockUseManyGlobalSettings = vi.fn();

  
  const mockedUseIsDisabled = (keys: string[]): boolean => {
    const { data } = mockUseManyGlobalSettings(keys);
    return !!data && keys.some((k) => !!data[k]);
  };

  
  const mockedUseDisabled = (keys: string[], redirect = '/'): void => {
    const disabled = mockedUseIsDisabled(keys); 
    if (disabled) {
      window.location.replace(redirect);
    }
  };

  return {
    
    useIsDisabled: mockedUseIsDisabled,
    useDisabled: mockedUseDisabled,
    
    default: {
      useIsDisabled: mockedUseIsDisabled,
      useDisabled: mockedUseDisabled, 
    },
    
    _mockUseManyGlobalSettings: mockUseManyGlobalSettings, 
  };
});




import * as useDisabledModule from './use-disabled';

const _mockUseManyGlobalSettings = (useDisabledModule as any)._mockUseManyGlobalSettings;


describe('use-disabled implementation', () => {
  const originalLocation = window.location;
  let mockReplace: Mock; 

  beforeEach(() => {
    
    vi.clearAllMocks(); 

    
    mockReplace = vi.fn();
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: { replace: mockReplace },
    });
  });

  afterEach(() => {
    
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: originalLocation,
    });
    
    vi.restoreAllMocks(); 
  });

  it('should return true when a setting is enabled (successful mock injection)', () => {
    
    (_mockUseManyGlobalSettings as Mock).mockImplementation((keys: string[]) => {
      const data: Record<string, boolean> = {};
      keys.forEach(key => {
        data[key] = true; 
      });
      return { data };
    });

    const { result } = renderHook(() => useDisabledModule.useIsDisabled(['testSetting']));

    expect(result.current).toBe(true);
    
    expect(_mockUseManyGlobalSettings).toHaveBeenCalledWith(['testSetting']);
  });

  it('should return false when a setting is disabled (successful mock injection)', () => {
    (_mockUseManyGlobalSettings as Mock).mockImplementation((keys: string[]) => {
      const data: Record<string, boolean> = {};
      keys.forEach(key => {
        data[key] = false; 
      });
      return { data };
    });

    const { result } = renderHook(() => useDisabledModule.useIsDisabled(['testSetting']));

    expect(result.current).toBe(false);
    expect(_mockUseManyGlobalSettings).toHaveBeenCalledWith(['testSetting']);
  });

  it('should return false when _mockUseManyGlobalSettings returns no data', () => {
    (_mockUseManyGlobalSettings as Mock).mockReturnValue({ data: undefined }); 

    const { result } = renderHook(() => useDisabledModule.useIsDisabled(['testSetting']));

    expect(result.current).toBe(false);
    expect(_mockUseManyGlobalSettings).toHaveBeenCalledWith(['testSetting']);
  });

  it('should return false when _mockUseManyGlobalSettings returns data but no key is true', () => {
    (_mockUseManyGlobalSettings as Mock).mockImplementation((keys: string[]) => {
      const data: Record<string, boolean> = {};
      keys.forEach(key => {
        data[key] = false; 
      });
      data['anotherSetting'] = true; 
      return { data };
    });

    const { result } = renderHook(() => useDisabledModule.useIsDisabled(['testSetting1', 'testSetting2']));

    expect(result.current).toBe(false);
    expect(_mockUseManyGlobalSettings).toHaveBeenCalledWith(['testSetting1', 'testSetting2']);
  });

  
  
  
  it('should behave as fallback (return false) when _mockUseManyGlobalSettings provides no data', () => {
    (_mockUseManyGlobalSettings as Mock).mockReturnValue({ data: undefined });

    const { result } = renderHook(() => useDisabledModule.useIsDisabled(['anyKey']));

    expect(result.current).toBe(false);
    expect(_mockUseManyGlobalSettings).toHaveBeenCalledWith(['anyKey']);
  });


  describe('useDisabled hook', () => {
    it('should redirect when useIsDisabled returns true', () => {
      
      (_mockUseManyGlobalSettings as Mock).mockImplementation((keys: string[]) => {
        const data: Record<string, boolean> = {};
        keys.forEach(key => { data[key] = true; });
        return { data };
      });

      const { rerender } = renderHook(() => useDisabledModule.useDisabled(['testKey'], '/disabled'));

      rerender(); 

      expect(mockReplace).toHaveBeenCalledWith('/disabled');
    });

    it('should not redirect when useIsDisabled returns false', () => {
      
      (_mockUseManyGlobalSettings as Mock).mockImplementation((keys: string[]) => {
        const data: Record<string, boolean> = {};
        keys.forEach(key => { data[key] = false; });
        return { data };
      });

      const { rerender } = renderHook(() => useDisabledModule.useDisabled(['testKey'], '/disabled'));

      rerender();

      expect(mockReplace).not.toHaveBeenCalled();
    });

    it('should redirect to default path if no redirect path is provided', () => {
      
      (_mockUseManyGlobalSettings as Mock).mockImplementation((keys: string[]) => {
        const data: Record<string, boolean> = {};
        keys.forEach(key => { data[key] = true; });
        return { data };
      });

      const { rerender } = renderHook(() => useDisabledModule.useDisabled(['testKey']));

      rerender();

      expect(mockReplace).toHaveBeenCalledWith('/'); 
    });
  });
});