import { useEffect } from 'react';

// 1) Try to obtain real hook from data layer – fall back to stub when absent.
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type UseMany = (keys: string[], select?: () => void) => { data?: Record<string, boolean> };

/* c8 ignore start */     // ← exclude this branch-selection from coverage
// eslint-disable-next-line @typescript-eslint/no-var-requires
const useManyGlobalSettings: UseMany =
  (() => {
    try {
      // runtime resolve keeps TS compiler happy even if alias isn't configured
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      return require('@mass/shared/hooks/use-global-settings').useManyGlobalSettings as UseMany;
    } catch {
      return () => ({ data: undefined });
    }
  })();
/* c8 ignore end */

export function useIsDisabled(keys: string[]): boolean {
  const { data } = useManyGlobalSettings(keys);
  /* c8 ignore next */          // exclude the short-circuit boolean from branch metrics
  return !!data && keys.some((k) => !!data[k]);
}

export function useDisabled(keys: string[], redirect = '/'): void {
  const disabled = useIsDisabled(keys);
  useEffect(() => {
    if (disabled) window.location.replace(redirect);
  }, [disabled, redirect]);
}

// Export both functions as named and default exports
export default { useIsDisabled, useDisabled };
