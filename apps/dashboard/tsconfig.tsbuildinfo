{"root": ["./src/env.d.ts", "./src/routetree.gen.ts", "./src/__mocks__/react-i18next.ts", "./src/constants/config.test.ts", "./src/constants/environment.test.ts", "./src/constants/sidebar-data.test.ts", "./src/constants/sidebar-data.ts", "./src/features/auth/index.ts", "./src/features/auth/hooks/use-auth.test.ts", "./src/features/auth/hooks/use-auth.ts", "./src/features/auth/types/index.ts", "./src/features/complaints-requests/data/queries.test.ts", "./src/features/complaints-requests/data/queries.ts", "./src/features/complaints-requests/data/schema.ts", "./src/features/notifications/components/use-notification-types.test.ts", "./src/features/notifications/components/use-notification-types.ts", "./src/features/notifications/data/queries.ts", "./src/features/settings/data/queries.ts", "./src/features/subscriptions/data/queries.ts", "./src/features/subscriptions/data/schema.test.ts", "./src/features/subscriptions/data/schema.ts", "./src/services/api-utils.test.ts", "./src/services/api.test.ts", "./src/services/api.ts", "./src/services/enums.test.ts", "./src/services/enums.ts", "./src/services/types.test.ts", "./src/services/types.ts", "./src/services/api/auth.test.ts", "./src/services/api/auth.ts", "./src/services/api/complaints.test.ts", "./src/services/api/complaints.ts", "./src/services/api/notifications.test.ts", "./src/services/api/notifications.ts", "./src/services/api/settings.test.ts", "./src/services/api/settings.ts", "./src/services/api/subscriptions.test.ts", "./src/services/api/subscriptions.ts", "./src/stores/auth.test.ts", "./src/stores/auth.ts", "./src/stores/notifications.test.ts", "./src/stores/notifications.ts", "./src/stores/sidebar.test.ts", "./src/stores/sidebar.ts", "./src/test/jsdomhelper.ts", "./src/test/jsdomsetup.ts", "./src/test/mockradixmodules.ts", "./src/test/setup.ts", "./src/test/__mocks__/api.ts", "./src/test/__mocks__/auth-api.ts", "./src/test/__mocks__/services-api.ts", "./src/test/__mocks__/sidebar-data.ts", "./src/test/__mocks__/sidebar-number.ts", "./src/test/__mocks__/use-disabled.ts", "./src/test/mocks/i18next-loader-mock.ts", "./src/types/declarations.d.ts", "./src/types/mime-types.d.ts", "./src/types/module.d.ts", "./src/utils/array.test.ts", "./src/utils/data-processing.test.ts", "./src/utils/date.test.ts", "./src/utils/formatter.test.ts", "./src/utils/formatter.ts", "./src/utils/general.test.ts", "./src/utils/i18n.test.ts", "./src/utils/i18n.ts", "./src/utils/number.test.ts", "./src/utils/string.test.ts", "./src/utils/use-disabled.test.ts", "./src/utils/use-disabled.ts", "./src/utils/__mocks__/use-disabled.ts", "./src/main.tsx", "./src/components/title.test.tsx", "./src/components/title.tsx", "./src/components/integration.test.tsx", "./src/features/auth/components/agreement.test.tsx", "./src/features/auth/components/agreement.tsx", "./src/features/auth/components/agreementwrapper.test.tsx", "./src/features/auth/components/agreementwrapper.tsx", "./src/features/auth/components/authlayout.test.tsx", "./src/features/auth/components/authlayout.tsx", "./src/features/auth/components/loginform.test.tsx", "./src/features/auth/components/loginform.tsx", "./src/features/auth/pages/login.test.tsx", "./src/features/auth/pages/login.tsx", "./src/features/complaints-requests/index.test.tsx", "./src/features/complaints-requests/index.tsx", "./src/features/complaints-requests/__mocks__/index.tsx", "./src/features/complaints-requests/components/columns.test.tsx", "./src/features/complaints-requests/components/columns.tsx", "./src/features/complaints-requests/components/document.test.tsx", "./src/features/complaints-requests/components/document.tsx", "./src/features/complaints-requests/components/skeleton-loader.test.tsx", "./src/features/complaints-requests/components/skeleton-loader.tsx", "./src/features/complaints-requests/components/status-badge.test.tsx", "./src/features/complaints-requests/components/status-badge.tsx", "./src/features/complaints-requests/components/modals/complaint-application.test.tsx", "./src/features/complaints-requests/components/modals/complaint-application.tsx", "./src/features/complaints-requests/components/modals/filter.test.tsx", "./src/features/complaints-requests/components/modals/filter.tsx", "./src/features/complaints-requests/components/modals/reserve.test.tsx", "./src/features/complaints-requests/components/modals/reserve.tsx", "./src/features/complaints-requests/pages/complaint-detail.test.tsx", "./src/features/complaints-requests/pages/complaint-detail.tsx", "./src/features/errors/forbidden.test.tsx", "./src/features/errors/forbidden.tsx", "./src/features/errors/general-error.test.tsx", "./src/features/errors/general-error.tsx", "./src/features/errors/maintenance-error.test.tsx", "./src/features/errors/maintenance-error.tsx", "./src/features/errors/not-found-error.test.tsx", "./src/features/errors/not-found-error.tsx", "./src/features/errors/unauthorized-error.test.tsx", "./src/features/errors/unauthorized-error.tsx", "./src/features/help-center/index.test.tsx", "./src/features/help-center/index.tsx", "./src/features/help-center/pages/about.test.tsx", "./src/features/help-center/pages/about.tsx", "./src/features/help-center/pages/faq.test.tsx", "./src/features/help-center/pages/faq.tsx", "./src/features/help-center/pages/use-cases.test.tsx", "./src/features/help-center/pages/use-cases.tsx", "./src/features/notifications/index.test.tsx", "./src/features/notifications/index.tsx", "./src/features/notifications/components/columns.test.tsx", "./src/features/notifications/components/columns.tsx", "./src/features/notifications/components/sidebar-number.test.tsx", "./src/features/notifications/components/sidebar-number.tsx", "./src/features/notifications/components/skeleton-loader.test.tsx", "./src/features/notifications/components/skeleton-loader.tsx", "./src/features/notifications/components/type-badge.test.tsx", "./src/features/notifications/components/type-badge.tsx", "./src/features/notifications/components/modals/filter.test.tsx", "./src/features/notifications/components/modals/filter.tsx", "./src/features/notifications/components/modals/preview.test.tsx", "./src/features/notifications/components/modals/preview.tsx", "./src/features/notifications/data/queries-simple.test.tsx", "./src/features/notifications/data/queries.test.tsx", "./src/features/settings/index.test.tsx", "./src/features/settings/index.tsx", "./src/features/settings/notifications.test.tsx", "./src/features/settings/notifications.tsx", "./src/features/settings/components/delete-account-modal.test.tsx", "./src/features/settings/components/delete-account-modal.tsx", "./src/features/settings/data/queries.test.tsx", "./src/features/subscriptions/data.test.tsx", "./src/features/subscriptions/data.tsx", "./src/features/subscriptions/index.test.tsx", "./src/features/subscriptions/index.tsx", "./src/features/subscriptions/notification.test.tsx", "./src/features/subscriptions/notification.tsx", "./src/features/subscriptions/outages.test.tsx", "./src/features/subscriptions/outages.tsx", "./src/features/subscriptions/overview.test.tsx", "./src/features/subscriptions/overview.tsx", "./src/features/subscriptions/components/columns.test.tsx", "./src/features/subscriptions/components/columns.tsx", "./src/features/subscriptions/components/skeleton-loader.test.tsx", "./src/features/subscriptions/components/skeleton-loader.tsx", "./src/features/subscriptions/components/modals/delete.test.tsx", "./src/features/subscriptions/components/modals/delete.tsx", "./src/features/subscriptions/components/modals/edit.test.tsx", "./src/features/subscriptions/components/modals/edit.tsx", "./src/features/subscriptions/components/modals/export.test.tsx", "./src/features/subscriptions/components/modals/export.tsx", "./src/features/subscriptions/components/modals/filter.test.tsx", "./src/features/subscriptions/components/modals/filter.tsx", "./src/features/subscriptions/components/modals/subscription-billing.test.tsx", "./src/features/subscriptions/components/modals/subscription-billing.tsx", "./src/features/subscriptions/data/queries.test.tsx", "./src/features/subscriptions/pages/detail.test.tsx", "./src/features/subscriptions/pages/detail.tsx", "./src/routes/__root.tsx", "./src/routes/(auth)/$auth.lazy.tsx", "./src/routes/(errors)/$error.lazy.tsx", "./src/routes/_authenticated/index.tsx", "./src/routes/_authenticated/logout.tsx", "./src/routes/_authenticated/route.tsx", "./src/routes/_authenticated/complaints-requests/$complaintid.lazy.tsx", "./src/routes/_authenticated/complaints-requests/index.tsx", "./src/routes/_authenticated/complaints-requests/route.lazy.tsx", "./src/routes/_authenticated/help-center/$page.lazy.tsx", "./src/routes/_authenticated/notifications/archived.lazy.tsx", "./src/routes/_authenticated/notifications/index.lazy.tsx", "./src/routes/_authenticated/settings/index.lazy.tsx", "./src/routes/_authenticated/settings/notification.lazy.tsx", "./src/routes/_authenticated/subscriptions/index.lazy.tsx", "./src/test/integration-coverage.test.tsx", "./types/declarations.d.ts", "./types/module.d.ts", "../../shared/components/organisms/layout/document-list.tsx"], "errors": true, "version": "5.7.3"}