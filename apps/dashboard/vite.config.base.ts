import * as path from 'node:path'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react-swc'
import i18nextLoader from 'vite-plugin-i18next-loader'
import mdx from '@mdx-js/rollup'
import tsconfigPaths from 'vite-tsconfig-paths'

export const sharedPlugins = [
  mdx(),
  react(),
  TanStackRouterVite(),
  i18nextLoader({
    paths: [path.resolve(__dirname, '../../shared/locales')],
    include: ['**/*.json'],
    namespaceResolution: 'basename',
  }),
  tsconfigPaths(),
]

export const sharedResolve = {
  alias: {
    "@": path.resolve(__dirname, "./src"),
    "@tabler/icons-react": "@tabler/icons-react/dist/esm/icons/index.mjs",
    "@mass/shared": path.resolve(__dirname, "../../shared"),
    '@mass': path.resolve(__dirname, '../../packages'),
    '@mass/shared/assets/auth': path.resolve(
      __dirname,
      '../../shared/assets/auth',
    ),
  },
}
