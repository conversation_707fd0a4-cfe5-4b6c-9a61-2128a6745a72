import { defineConfig } from 'vite'
import { sharedPlugins, sharedResolve } from './vite.config.base'

export default defineConfig({
  plugins: sharedPlugins,
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['@tanstack/react-router'],
          ui: ['@radix-ui/react-icons', 'lucide-react'],
        },
      },
    },
  },
  resolve: sharedResolve,
})
