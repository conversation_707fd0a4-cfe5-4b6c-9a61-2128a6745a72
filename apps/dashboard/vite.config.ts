import { defineConfig, loadEnv } from 'vite'
import { sharedPlugins, sharedResolve } from './vite.config.base'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());

  return {
    plugins: sharedPlugins,
    server: {
      port: 3000,
      proxy: {
        '/api': {
          target: env.VITE_BASE_URL,
          changeOrigin: true,
          withCredentials: true,
          secure: true,
          ws: true,
          followRedirects: true,
          autoRewrite: true,

          rewrite: (path) => path.replace(/^\/api/, ''),

          configure: (proxy, _options) => {
            if (!process.env.DEV) return

            proxy.on('proxyReq', (proxyReq, req) => {
              console.log(`Request: ${req.method} ${req.url}`);

              proxyReq.setHeader('Cache-Control', 'no-cache');

              proxyReq.removeHeader('If-Modified-Since');
              proxyReq.removeHeader('If-None-Match');

              proxyReq.setHeader('Accept', 'application/json');
            });

            proxy.on('proxyRes', (proxyRes, req, res) => {
              console.log(`Response: ${proxyRes.statusCode} ${req.url}`);

              if (
                typeof proxyRes.statusCode === 'number' &&
                proxyRes.statusCode >= 300 &&
                proxyRes.statusCode < 400
              ) {
                console.log(`Redirect location: ${proxyRes.headers.location}`);
              }

              if (proxyRes.statusCode === 304) {
                proxyRes.statusCode = 200;
                delete proxyRes.headers['etag'];
              }
            });
          },
        },
      },
      cors: true,
    },
    resolve: sharedResolve,
  };
});
