import * as path from 'node:path'
import { defineConfig, loadEnv } from 'vite'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react-swc'
import i18nextLoader from 'vite-plugin-i18next-loader'
import mdx from '@mdx-js/rollup'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const isDev = mode === 'development'

  return {
    plugins: [
      mdx(),
      react(),
      TanStackRouterVite(),
      i18nextLoader({
        paths: [path.resolve(__dirname, '../../shared/locales')],
        include: ['**/*.json'],
        namespaceResolution: 'basename',
      }),
      tsconfigPaths(),
    ],

    server: {
      port: 3000,
      proxy: {
        '/api': {
          target: env.VITE_BASE_URL,
          changeOrigin: true,
          withCredentials: true,
          secure: true,
          ws: true,
          followRedirects: true,
          autoRewrite: true,
          rewrite: (path) => path.replace(/^\/api/, ''),

          configure: (proxy, _options) => {
            if (!isDev) return

            proxy.on('proxyReq', (proxyReq, req) => {
              console.log(`Request: ${req.method} ${req.url}`)
              proxyReq.setHeader('Cache-Control', 'no-cache')
              proxyReq.removeHeader('If-Modified-Since')
              proxyReq.removeHeader('If-None-Match')
              proxyReq.setHeader('Accept', 'application/json')
            })

            proxy.on('proxyRes', (proxyRes, req) => {
              console.log(`Response: ${proxyRes.statusCode} ${req.url}`)

              if (proxyRes.statusCode && proxyRes.statusCode >= 300 && proxyRes.statusCode < 400) {
                console.log(`Redirect location: ${proxyRes.headers.location}`)
              }

              if (proxyRes.statusCode === 304) {
                proxyRes.statusCode = 200
                delete proxyRes.headers['etag']
              }
            })
          },
        },
      },
      cors: true,
    },

    build: {
      outDir: 'dist',
      sourcemap: !isDev,
      minify: 'esbuild',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            router: ['@tanstack/react-router'],
            ui: ['@radix-ui/react-icons', 'lucide-react'],
          },
        },
      },
    },

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@tabler/icons-react': '@tabler/icons-react/dist/esm/icons/index.mjs',
        '@mass/shared': path.resolve(__dirname, '../../shared'),
        '@mass': path.resolve(__dirname, '../../packages'),
        '@mass/shared/assets/auth': path.resolve(__dirname, '../../shared/assets/auth'),
      },
    },
  }
})
