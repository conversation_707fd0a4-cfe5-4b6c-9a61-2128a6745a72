/// <reference types="vitest" />
import { defineConfig } from 'vitest/config'
import { fileURLToPath } from 'node:url'

export default defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/test/setup.ts', './src/test/mockRadixModules.ts'],
    testTimeout: 15000,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/test/**',
        'src/**/*.d.ts',
        'src/main.tsx',
        'src/routeTree.gen.ts',
        '**/*.config.{ts,js}',
      ]
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@mass/shared': fileURLToPath(new URL('../../shared', import.meta.url)),
      '@mass': fileURLToPath(new URL('../../packages', import.meta.url)),
      '@mass/shared/assets/auth': fileURLToPath(new URL('../../shared/assets/auth', import.meta.url)),
      '@tabler/icons-react': '@tabler/icons-react/dist/esm/icons/index.mjs',
      '@testing-library/user-event': fileURLToPath(new URL('./src/test/__mocks__/@testing-library/user-event.js', import.meta.url)),
      'virtual:i18next-loader': fileURLToPath(new URL('./src/test/mocks/i18next-loader-mock.ts', import.meta.url))
    }
  }
});
