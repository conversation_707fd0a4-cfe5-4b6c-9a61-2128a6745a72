import { defineConfig } from 'vitest/config';
import { fileURLToPath } from 'node:url';
import path from 'path';

export default defineConfig({
  test: {
    environment: 'jsdom',
    environmentOptions: {
      jsdom: {
        resources: 'usable',
        url: 'http://localhost'
      }
    },
    globals: true,
    setupFiles: ['./src/test/setup.ts', './src/test/mockRadixModules.ts'],
    mockReset: true,
    restoreMocks: true,
    testTimeout: 15000,
    alias: [
      { find: /^@\/(.*)$/, replacement: path.resolve(__dirname, 'src/$1') },
      { find: 'virtual:i18next-loader', replacement: path.resolve(__dirname, 'src/test/mocks/i18next-loader-mock.ts') }
    ],
    deps: {
      optimizer: {
        web: {
          exclude: ['react', 'react-dom', 'react/jsx-runtime', '@testing-library/jest-dom']
        }
      }
    },
    server: {
      deps: {
        inline: [
          'react',
          'react/jsx-runtime',
          'react/jsx-dev-runtime',
          'react-dom',
          'react-dom/test-utils',
          '@testing-library/react',
          '@testing-library/jest-dom',
          '@testing-library/user-event',
          'clsx',
          'tailwind-merge',
          '@tanstack/react-query',
          'lucide-react',
          '@mass/shared'
        ]
      }
    },
    coverage: {
      provider: 'istanbul',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: '../../coverage/dashboard',
      exclude: [
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/**/*.integration.{test,spec}.{ts,tsx}',
        'src/**/*.e2e.{test,spec}.{ts,tsx}',
        'src/test/**',
        'src/**/*.d.ts',
        'src/main.tsx',
        'src/env.d.ts',
        'src/routeTree.gen.ts',
        'src/routes/**/*.test.{ts,tsx}',
        'src/routes/**',
        'src/**/__mocks__/**',
        '**.config.{ts,js}',
        'src/constants/**.ts',
      ]
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@/services': fileURLToPath(new URL('./src/services', import.meta.url)),
      '@/features': fileURLToPath(new URL('./src/features', import.meta.url)),
      '@/constants': fileURLToPath(new URL('./src/constants', import.meta.url)),
      '@/utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
      '@/components': fileURLToPath(new URL('./src/components', import.meta.url)),
      '@mass/shared': fileURLToPath(new URL('../../shared', import.meta.url)),
      '@mass': fileURLToPath(new URL('../../packages', import.meta.url)),
      '@mass/shared/assets/auth': fileURLToPath(new URL('../../shared/assets/auth', import.meta.url)),
      '@tabler/icons-react': '@tabler/icons-react/dist/esm/icons/index.mjs',
      '@testing-library/user-event': fileURLToPath(new URL('../../test/__mocks__/user-event.ts', import.meta.url))
    }
  }
});
