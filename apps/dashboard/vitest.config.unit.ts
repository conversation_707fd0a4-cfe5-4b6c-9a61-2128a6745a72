import { defineConfig } from 'vitest/config'
import { fileURLToPath } from 'node:url'

export default defineConfig({
  test: {
    name: 'unit',
    environment: 'jsdom',
    environmentOptions: {
      jsdom: {
        resources: 'usable',
        url: 'http://localhost'
      }
    },
    globals: true,
    setupFiles: ['./src/test/setup.ts', './src/test/mockRadixModules.ts'],
    mockReset: true,
    restoreMocks: true,
    testTimeout: 10000,
    include: ['src/**/*.{test,spec}.{ts,tsx}'],
    exclude: [
      'src/**/*.integration.{test,spec}.{ts,tsx}',
      'src/**/*.e2e.{test,spec}.{ts,tsx}',
      'node_modules/**',
      'dist/**'
    ],
    alias: [
      { find: /^@\/(.*)$/, replacement: fileURLToPath(new URL('./src/$1', import.meta.url)) },
      { find: 'virtual:i18next-loader', replacement: fileURLToPath(new URL('./src/test/mocks/i18next-loader-mock.ts', import.meta.url)) }
    ],
    deps: {
      optimizer: {
        web: {
          exclude: ['react', 'react-dom', 'react/jsx-runtime', '@testing-library/jest-dom']
        }
      }
    },
    server: {
      deps: {
        inline: [
          'react',
          'react/jsx-runtime',
          'react/jsx-dev-runtime',
          'react-dom',
          'react-dom/test-utils',
          '@testing-library/react',
          '@testing-library/jest-dom',
          '@testing-library/user-event',
          'clsx',
          'tailwind-merge',
          '@tanstack/react-query',
          'lucide-react',
          '@mass/shared'
        ]
      }
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@/services': fileURLToPath(new URL('./src/services', import.meta.url)),
      '@/features': fileURLToPath(new URL('./src/features', import.meta.url)), 
      '@/constants': fileURLToPath(new URL('./src/constants', import.meta.url)),
      '@/utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
      '@/components': fileURLToPath(new URL('./src/components', import.meta.url)),
      '@mass/shared': fileURLToPath(new URL('../../shared', import.meta.url)),
      '@mass': fileURLToPath(new URL('../../packages', import.meta.url)),
      '@mass/shared/assets/auth': fileURLToPath(new URL('../../shared/assets/auth', import.meta.url)),
      '@tabler/icons-react': '@tabler/icons-react/dist/esm/icons/index.mjs',
      '@testing-library/user-event': fileURLToPath(new URL('../../test/__mocks__/user-event.ts', import.meta.url))
    }
  }
})
