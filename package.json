{"name": "mass", "version": "1.0.11", "description": "", "main": "index.js", "scripts": {"dev:dashboard": "pnpm --filter @mass/dashboard dev", "dev:admin": "pnpm --filter @mass/admin dev", "dev:shared": "pnpm --filter @mass/shared dev", "build": "pnpm --filter @mass/dashboard build && pnpm --filter @mass/admin build && pnpm --filter @mass/shared build", "build:shared": "pnpm --filter @mass/shared build", "build:dashboard": "pnpm --filter @mass/dashboard build", "preview:dashboard": "pnpm --filter @mass/dashboard preview", "build:admin": "pnpm --filter @mass/admin build", "preview:admin": "pnpm --filter @mass/admin preview", "knip": "knip", "postinstall": "pnpm build:shared"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@types/node": "^20", "@vitest/coverage-istanbul": "^3.2.1", "istanbul-merge": "^2.0.0", "jq": "^1.7.2", "jsdom": "^26.1.0", "knip": "^5.45.0", "nyc": "^15.1.0", "typescript": "^5.8.2", "vitest": "^3.1.4"}, "dependencies": {"autoprefixer": "^10.4.20", "postcss": "^8", "tailwindcss": "3"}, "pnpm": {"ignoredBuiltDependencies": ["@swc/core", "esbuild"]}, "workspaces": ["apps/*", "packages/*"]}