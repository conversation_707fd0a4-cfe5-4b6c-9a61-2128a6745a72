/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/react';
import Iconify from './Iconify';
import { addCollection, Icon } from '@iconify/react';

// Mock @iconify/react
vi.mock('@iconify/react', () => ({
  addCollection: vi.fn(),
  Icon: vi.fn(props => (
    <div 
      data-testid="mock-icon"
      data-icon={props.icon}
      data-color={props.color}
      data-width={props.width}
      data-height={props.height}
      className={props.className}
    />
  ))
}));

// Mock icon collections
vi.mock('../../assets/icons/mass.json', () => ({
  default: {
    prefix: 'mass',
    icons: {
      'logo-icon': {},
      'logo-mark': {},
      'edevlet': {}
    }
  }
}));

vi.mock('../../assets/icons/untitled.json', () => ({
  default: {
    prefix: 'untitled',
    icons: {
      'home-01': {},
      'search-lg': {},
      'search-md': {},
      'search-sm': {}
    }
  }
}));

describe('Iconify component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders an icon with default props', () => {
    const { container } = render(<Iconify name="untitled:home-01" />);
    
    const icon = container.querySelector('[data-testid="mock-icon"]');
    expect(icon).not.toBeNull();
    
    if (icon instanceof HTMLElement) {
      expect(icon.getAttribute('data-icon')).toBe('untitled:home-01');
      expect(icon.getAttribute('data-width')).toBe('12');
      expect(icon.getAttribute('data-height')).toBe('12');
    }
  });

  it('renders an icon with custom size', () => {
    const { container } = render(<Iconify name="mass:logo-icon" size={24} />);
    
    const icon = container.querySelector('[data-testid="mock-icon"]');
    expect(icon).not.toBeNull();
    
    if (icon instanceof HTMLElement) {
      expect(icon.getAttribute('data-width')).toBe('24');
      expect(icon.getAttribute('data-height')).toBe('24');
    }
  });

  it('renders an icon with custom color', () => {
    const { container } = render(<Iconify name="untitled:search-lg" color="#ff0000" />);
    
    const icon = container.querySelector('[data-testid="mock-icon"]');
    expect(icon).not.toBeNull();
    
    if (icon instanceof HTMLElement) {
      expect(icon.getAttribute('data-color')).toBe('#ff0000');
    }
  });

  it('applies className when provided', () => {
    const { container } = render(<Iconify name="mass:logo-mark" className="test-class" />);
    
    const icon = container.querySelector('[data-testid="mock-icon"]');
    expect(icon).not.toBeNull();
    
    if (icon instanceof HTMLElement) {
      expect(icon.className).toContain('test-class');
    }
  });
});
