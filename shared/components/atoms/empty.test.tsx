/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import Empty from './empty';

// Mock SVG imports
vi.mock('../../assets/empty.svg', () => ({
  default: 'empty-svg-mock'
}));
vi.mock('../../assets/pattern_decorative.svg', () => ({
  default: 'pattern-svg-mock'
}));

describe('Empty component', () => {
  const defaultProps = {
    title: 'No data found',
    description: 'There is no data to display at the moment'
  };

  it('renders correctly with required props', () => {
    const { container } = render(<Empty {...defaultProps} />);
    
    // Check title and description
    const titleElement = container.querySelector('.font-semibold');
    const descriptionElement = container.querySelector('.text-base.text-gray-600');
    
    expect(titleElement).not.toBeNull();
    expect(descriptionElement).not.toBeNull();
    
    if (titleElement) {
      expect(titleElement.textContent).toBe(defaultProps.title);
    }
    
    if (descriptionElement) {
      expect(descriptionElement.textContent).toBe(defaultProps.description);
    }
    
    // Check images
    const images = container.querySelectorAll('img');
    expect(images.length).toBe(2);
    
    const emptyVectorImg = Array.from(images).find(img => img.getAttribute('alt') === 'empty-vector');
    const patternImg = Array.from(images).find(img => img.getAttribute('alt') === 'pattern');
    
    expect(emptyVectorImg).not.toBeUndefined();
    expect(patternImg).not.toBeUndefined();
  });

  it('renders children when provided', () => {
    const { container } = render(
      <Empty {...defaultProps}>
        <button data-testid="action-button">Take Action</button>
      </Empty>
    );
    
    const button = container.querySelector('[data-testid="action-button"]');
    expect(button).not.toBeNull();
    
    if (button) {
      expect(button.textContent).toBe('Take Action');
    }
  });

  it('applies correct classes for styling', () => {
    const { container } = render(<Empty {...defaultProps} />);
    
    // Check container classes
    const mainContainer = container.firstChild;
    expect(mainContainer).not.toBeNull();
    
    if (mainContainer instanceof HTMLElement) {
      expect(mainContainer.className).toContain('flex');
      expect(mainContainer.className).toContain('flex-col');
      expect(mainContainer.className).toContain('items-center');
    }
    
    // Check title classes
    const title = container.querySelector('.font-semibold');
    expect(title).not.toBeNull();
    
    // Check description classes
    const description = container.querySelector('.text-gray-600');
    expect(description).not.toBeNull();
  });
});
