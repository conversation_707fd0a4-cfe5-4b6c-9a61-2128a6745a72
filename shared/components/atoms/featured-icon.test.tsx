/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import FeaturedIcon from './featured-icon';

// Mock the Iconify component and its types
vi.mock('./Iconify', () => {
  return {
    default: vi.fn((props) => (
      <div 
        data-testid="mock-iconify" 
        data-icon-name={props.name} 
        className={props.className} 
      />
    ))
  };
});

describe('FeaturedIcon component', () => {
  it('renders correctly with default variant', () => {
    const { container } = render(<FeaturedIcon name="untitled:home-01" />);
    
    const iconContainer = container.firstChild as HTMLElement;
    expect(iconContainer).not.toBeNull();
    
    if (iconContainer) {
      expect(iconContainer.className).toContain('rounded-[28px]');
      expect(iconContainer.className).toContain('border-[8px]');
      
      const iconElement = container.querySelector('[data-testid="mock-iconify"]');
      expect(iconElement).not.toBeNull();
      if (iconElement) {
        expect(iconElement.getAttribute('data-icon-name')).toBe('untitled:home-01');
        expect(iconElement.getAttribute('class')).toBe('text-gray-500 size-6');
      }
    }
  });

  it('renders correctly with alternative variant', () => {
    const { container } = render(<FeaturedIcon name="untitled:home-01" variant="alternative" />);
    
    const iconContainer = container.firstChild as HTMLElement;
    expect(iconContainer).not.toBeNull();
    
    if (iconContainer) {
      expect(iconContainer.className).toContain('rounded-lg');
      expect(iconContainer.className).toContain('bg-base-white');
    }
  });

  it('applies additional className when provided', () => {
    const { container } = render(<FeaturedIcon name="untitled:home-01" className="test-class" />);
    
    const iconContainer = container.firstChild as HTMLElement;
    expect(iconContainer).not.toBeNull();
    
    if (iconContainer) {
      expect(iconContainer.className).toContain('test-class');
    }
  });

  it('passes additional props to the container div', () => {
    const { container } = render(
      <FeaturedIcon 
        name="untitled:home-01" 
        data-testid="featured-icon" 
        aria-label="Icon"
      />
    );
    
    const iconContainer = container.firstChild as HTMLElement;
    expect(iconContainer).not.toBeNull();
    
    if (iconContainer) {
      expect(iconContainer.getAttribute('data-testid')).toBe('featured-icon');
      expect(iconContainer.getAttribute('aria-label')).toBe('Icon');
    }
  });
});
