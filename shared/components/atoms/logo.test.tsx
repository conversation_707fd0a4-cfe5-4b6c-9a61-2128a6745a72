/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import Logo from './logo';

// Mock the images
vi.mock('../../assets/logo-mark.svg?url', () => ({
  default: 'logo-mark-mock-url'
}));

vi.mock('../../assets/logo.svg?url', () => ({
  default: 'logo-mock-url'
}));

// Mock the Link component from @tanstack/react-router
vi.mock('@tanstack/react-router', () => ({
  Link: vi.fn(({ children, ...props }) => (
    <a data-testid="mock-link" {...props}>
      {children}
    </a>
  ))
}));

describe('Logo component', () => {
  it('renders expanded logo correctly', () => {
    const { container } = render(<Logo state="expanded" />);
    
    const img = container.querySelector('img');
    expect(img).not.toBeNull();
    if (img instanceof HTMLElement) {
      expect(img.getAttribute('src')).toBe('logo-mark-mock-url');
      expect(img.className).toContain('mx-auto');
    }
    
    const link = container.querySelector('[data-testid="mock-link"]');
    expect(link).not.toBeNull();
    if (link instanceof HTMLElement) {
      expect(link.getAttribute('href')).toBe('/');
    }
  });

  it('renders collapsed logo correctly', () => {
    const { container } = render(<Logo state="collapsed" />);
    
    const img = container.querySelector('img');
    expect(img).not.toBeNull();
    if (img instanceof HTMLElement) {
      expect(img.getAttribute('src')).toBe('logo-mock-url');
      expect(img.className).toContain('aspect-square');
      expect(img.className).toContain('flex-shrink-0');
    }
  });

  it('applies additional className when provided', () => {
    const { container } = render(<Logo state="expanded" className="test-class" />);
    
    const logoContainer = container.firstChild;
    expect(logoContainer).not.toBeNull();
    if (logoContainer instanceof HTMLElement) {
      expect(logoContainer.className).toContain('test-class');
      expect(logoContainer.className).toContain('flex');
      expect(logoContainer.className).toContain('gap-3');
    }
  });

  it('passes additional props to the container div', () => {
    const { container } = render(<Logo state="expanded" data-testid="logo-component" />);
    
    const logoContainer = container.querySelector('[data-testid="logo-component"]');
    expect(logoContainer).not.toBeNull();
    if (logoContainer instanceof HTMLElement) {
      expect(logoContainer.tagName.toLowerCase()).toBe('div');
    }
  });
});
