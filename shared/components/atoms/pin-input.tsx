import { cn } from "../../lib/utils";
import React, { createRef, useRef, useState, useEffect } from "react";

// Extended input props to support component prop
interface PinInputFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  component?: React.ComponentType<any>;
}

// Simple implementation of PinInput component
export const PinInputField = React.forwardRef<
  HTMLInputElement, 
  PinInputFieldProps
>(({ className, component: Component, ...props }, ref) => {
  // If a custom component is provided, use it
  if (Component) {
    return (
      <Component
        type="text"
        inputMode="numeric"
        pattern="[0-9]*"
        className={cn(
          "h-12 w-12 rounded-md border border-gray-300 text-center text-xl focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary-500 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        maxLength={1}
        ref={ref}
        autoComplete="one-time-code"
        {...props}
      />
    );
  }

  // Otherwise use a standard input
  return (
    <input
      type="text"
      inputMode="numeric"
      pattern="[0-9]*"
      className={cn(
        "h-12 w-12 rounded-md border border-gray-300 text-center text-xl focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary-500 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      maxLength={1}
      ref={ref}
      autoComplete="one-time-code"
      {...props}
    />
  );
});

PinInputField.displayName = "PinInputField";

interface PinInputProps {
  length?: number;
  onChange?: (value: string) => void;
  value?: string;
  disabled?: boolean;
  children?: React.ReactNode;
  className?: string;
  onComplete?: () => void;
  onIncomplete?: () => void;
}

export const PinInput = React.forwardRef<HTMLDivElement, PinInputProps>(
  ({ length = 6, onChange, value = "", disabled = false, children, className, onComplete, onIncomplete }, ref) => {
    const [values, setValues] = useState<string[]>(Array(length).fill(""));
    const inputRefs = useRef<Array<React.RefObject<HTMLInputElement | null>>>(Array.from({ length }, () => createRef<HTMLInputElement>()));

    // Update internal state when value prop changes
    useEffect(() => {
      if (value) {
        const valueArray = value.split("").slice(0, length);
        setValues((prev) => {
          const newValues = [...prev];
          valueArray.forEach((val, idx) => {
            newValues[idx] = val;
          });
          return newValues;
        });
      }
    }, [value, length]);
    
    // Handle complete and incomplete states
    useEffect(() => {
      const isComplete = value.length === length;
      if (isComplete && onComplete) {
        onComplete();
      } else if (!isComplete && onIncomplete) {
        onIncomplete();
      }
    }, [value, length, onComplete, onIncomplete]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
      const newValue = e.target.value.replace(/\D/g, "").slice(0, 1);
      
      // Update internal state
      setValues((prev) => {
        const newValues = [...prev];
        newValues[index] = newValue;
        return newValues;
      });
      
      // Notify parent component
      const combinedValue = [...values];
      combinedValue[index] = newValue;
      onChange?.(combinedValue.join(""));
      
      // Auto-focus next input if there's a value
      if (newValue && index < length - 1) {
        inputRefs.current[index + 1].current?.focus();
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
      // Move focus to previous field on backspace if current field is empty
      if (e.key === "Backspace" && !values[index] && index > 0) {
        inputRefs.current[index - 1].current?.focus();
      }
    };
    
    // If children are provided, use them
    if (children) {
      return <div className={cn("flex items-center gap-2", className)} ref={ref}>{children}</div>;
    }

    // Otherwise generate inputs
    return (
      <div className={cn("flex items-center gap-2", className)} ref={ref}>
        {Array(length)
          .fill(null)
          .map((_, index) => (
            <PinInputField
              key={index}
              ref={inputRefs.current[index]}
              value={values[index]}
              onChange={(e) => handleChange(e, index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              disabled={disabled}
              aria-label={`PIN input ${index + 1}`}
            />
          ))}
      </div>
    );
  }
);

PinInput.displayName = "PinInput";
