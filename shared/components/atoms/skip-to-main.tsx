import { cn } from "../../lib/utils";
import { useCallback } from "react";

interface SkipToMainProps {
  className?: string;
}

const SkipToMain = ({ className }: SkipToMainProps) => {
  const skipToMain = useCallback(() => {
    const main = document.querySelector("main");
    if (main) {
      main.tabIndex = -1;
      main.focus();
      setTimeout(() => {
        main.removeAttribute("tabindex");
      }, 1000);
    }
  }, []);

  return (
    <a
      href="#main"
      className={cn(
        "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-black focus:shadow focus:outline-none",
        className
      )}
      onClick={(e) => {
        e.preventDefault();
        skipToMain();
      }}
    >
      Skip to main content
    </a>
  );
};

export default SkipToMain;
