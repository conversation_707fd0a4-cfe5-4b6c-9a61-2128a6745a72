/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BarDataChart } from './bar';
import * as Utils from './utils';

// Mock recharts components
vi.mock('recharts', () => ({
  Bar: vi.fn(({ children, ...props }) => <div data-testid="recharts-bar" {...props}>{children}</div>),
  BarChart: vi.fn(({ children, ...props }) => <div data-testid="recharts-bar-chart" {...props}>{children}</div>),
  CartesianGrid: vi.fn(props => <div data-testid="recharts-cartesian-grid" {...props} />),
  Cell: vi.fn(props => <div data-testid="recharts-cell" {...props} />),
  XAxis: vi.fn(props => <div data-testid="recharts-x-axis" {...props} />),
  YAxis: vi.fn(props => <div data-testid="recharts-y-axis" {...props} />),
  Legend: vi.fn(props => <div data-testid="recharts-legend" {...props} />),
  Tooltip: vi.fn(props => <div data-testid="recharts-tooltip" {...props} />)
}));

// Mock chart components
vi.mock('../../components/ui/chart', async () => {
  const React = await vi.importActual('react');
  return {
    ChartConfig: vi.fn(props => React.createElement('div', { 'data-testid': 'chart-config', ...props })),
    ChartContainer: vi.fn(({ children, ...props }) => 
      React.createElement('div', { 'data-testid': 'chart-container', ...props }, children)
    ),
    ChartTooltip: vi.fn((props) => {
      if (props && props.content && typeof props.content === 'function') {
        const content = props.content({ 
          payload: [
            { 
              name: 'data.T0', 
              dataKey: 'data.T0',
              value: 100,
              payload: { key: 'usage', timeframe: '2023-01-01' }
            },
            { 
              name: 'data.T1', 
              dataKey: 'data.T1',
              value: 200,
              payload: { key: 'usage', timeframe: '2023-01-01' }
            }
          ], 
          label: 'usage' 
        });
        return content || React.createElement('div', { 'data-testid': 'chart-tooltip-empty' });
      }
      return React.createElement('div', { 'data-testid': 'chart-tooltip', ...props });
    })
  };
});

// Mock translations
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => {
      const translations = {
        'usage.usage_chart_label': 'Usage',
        'usage.compare.usage': 'Usage Comparison',
        'usage.compare.average': 'Average',
        'usage.compare.similar-consumer-city': 'Similar City',
        'usage.compare.similar-consumer-district': 'Similar District'
      };
      return translations[key] || key;
    }
  })
}));

// Mock date locale hook
vi.mock('../../hooks/use-date-locale', () => ({
  useDateLocale: () => ({
    code: 'en-US',
    weekStartsOn: 0
  })
}));

describe('BarDataChart', () => {
  const mockChartData = [
    { timeframe: new Date('2023-01-01'), data: { T0: 100, T1: 200, T2: 300, T3: 400 } },
    { timeframe: new Date('2023-01-02'), data: { T0: 150, T1: 250, T2: 350, T3: 450 } }
  ];

  const mockCompareChartData = [
    { key: 'usage', data: { T0: 100, T1: 200, T2: 300, T3: 400 } },
    { key: 'average', data: { T0: 90, T1: 190, T2: 290, T3: 390 } },
    { key: 'cityEntry', data: { T0: 80, T1: 180, T2: 280, T3: 380 } },
    { key: 'districtEntry', data: { T0: 70, T1: 170, T2: 270, T3: 370 } }
  ];

  beforeEach(() => {
    vi.spyOn(Utils, 'zeroFill').mockReturnValue({
      chartData: mockChartData,
      compareChartData: mockCompareChartData
    });
    
    vi.spyOn(Utils, 'getDateFunctionsForGranularity').mockReturnValue({
      start: vi.fn(),
      add: vi.fn(),
      label: (date) => date instanceof Date ? date.toISOString() : date.toString(),
      amount: 1
    });
  });

  const defaultProps = {
    data: [],
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-01-31'),
    granularity: 'day',
    threeTime: false,
    unit: 'kWh',
    compare: null,
    compareTo: null
  };

  it('renders the chart container', () => {
    const { container } = render(<BarDataChart {...defaultProps} />);
    expect(container.querySelector('[data-testid="chart-container"]')).not.toBeNull();
  });

  it('renders basic bar chart without comparison data', () => {
    const { container } = render(<BarDataChart {...defaultProps} />);
    
    expect(container.querySelector('[data-testid="recharts-bar-chart"]')).not.toBeNull();
    expect(container.querySelector('[data-testid="recharts-x-axis"]')).not.toBeNull();
    expect(container.querySelector('[data-testid="recharts-y-axis"]')).not.toBeNull();
    expect(container.querySelector('[data-testid="recharts-bar"]')).not.toBeNull();
  });

  it('renders with comparison data', () => {
    const compareProps = {
      ...defaultProps,
      compare: {
        usage: { T0: 100 },
        average: { T0: 90 },
        cityEntry: { T0: 80 },
        districtEntry: { T0: 70 }
      }
    };
    
    const { container } = render(<BarDataChart {...compareProps} />);
    
    expect(container.querySelector('[data-testid="recharts-bar-chart"]')).not.toBeNull();
    // When compare data is provided, we should pass compareChartData to BarChart
    expect(Utils.zeroFill).toHaveBeenCalled();
  });

  it('renders with three time segments when enabled', () => {
    const threeTimeProps = {
      ...defaultProps,
      threeTime: true
    };
    
    const { container } = render(<BarDataChart {...threeTimeProps} />);
    
    // Should have multiple bars for T1, T2, T3
    expect(container.querySelectorAll('[data-testid="recharts-bar"]').length).toBeGreaterThan(1);
  });

  it('handles different granularities', () => {
    vi.spyOn(Utils, 'getDateFunctionsForGranularity').mockReturnValueOnce({
      start: vi.fn(),
      add: vi.fn(),
      label: (date) => 'Hour: ' + (date instanceof Date ? date.toISOString() : date),
      amount: 1
    });
    
    const hourlyProps = {
      ...defaultProps,
      granularity: 'hour'
    };
    
    render(<BarDataChart {...hourlyProps} />);
    
    expect(Utils.getDateFunctionsForGranularity).toHaveBeenCalledWith('hour', expect.anything());
  });

  it('does not render three time segments for hourly data', () => {
    vi.spyOn(Utils, 'getDateFunctionsForGranularity').mockReturnValueOnce({
      start: vi.fn(),
      add: vi.fn(),
      label: (date) => 'Hour: ' + (date instanceof Date ? date.toISOString() : date),
      amount: 1
    });
    
    const hourlyProps = {
      ...defaultProps,
      granularity: 'hour',
      threeTime: true  // This should be ignored for hourly data
    };
    
    const { container } = render(<BarDataChart {...hourlyProps} />);
    
    // Should only have one bar for T0
    expect(container.querySelectorAll('[data-testid="recharts-bar"]').length).toBe(1);
  });

  it('renders with expected chart components', () => {
    const { container } = render(<BarDataChart {...defaultProps} />);
    
    expect(container.querySelector('[data-testid="chart-container"]')).not.toBeNull();
    expect(container.querySelector('[data-testid="recharts-bar-chart"]')).not.toBeNull();
    expect(container.querySelector('[data-testid="recharts-bar"]')).not.toBeNull();
    expect(container.querySelector('[data-testid="recharts-x-axis"]')).not.toBeNull();
    expect(container.querySelector('[data-testid="recharts-y-axis"]')).not.toBeNull();
  });
  
  it('passes chart config with correct labels', () => {
    const { container } = render(<BarDataChart {...defaultProps} />);
    
    // Verify that the chart has the correct configuration
    expect(container.querySelector('[data-testid="chart-container"]')).not.toBeNull();
    expect(container.querySelector('[data-testid="recharts-bar-chart"]')).not.toBeNull();
  });

  it('renders multiple bars for three time segments', () => {
    const threeTimeProps = {
      ...defaultProps,
      threeTime: true,
      data: [
        { 
          timeframe: new Date('2023-01-01'), 
          data: { T0: 100, T1: 200, T2: 300, T3: 400 } 
        }
      ]
    };
    
    const { container } = render(<BarDataChart {...threeTimeProps} />);
    
    // For three time segments we should have multiple bars rendered
    expect(container.querySelectorAll('[data-testid="recharts-bar"]').length).toBeGreaterThan(1);
  });
});
