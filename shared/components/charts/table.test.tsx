/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/react';
import { TableChart } from './table';
import * as Utils from './utils';

// Mock the tanstack table component
vi.mock('@tanstack/react-table', () => ({
  getCoreRowModel: vi.fn(() => ({}))
}));

// Update the DataTable mock to preserve columns structure before stringifying
vi.mock('../../components/organisms/table', () => ({
  DataTable: vi.fn(({ children, columns, data, ...props }) => {
    // Store original column structure for tests
    (window as any).__tableTestColumns = columns;
    return (
      <div data-testid="data-table" {...props}>
        <div data-testid="data-table-columns">{JSON.stringify(columns.map((c: { header: any; cell: any; }) => ({
          ...c,
          header: c.header ? 'header-fn' : undefined,
          cell: c.cell ? 'cell-fn' : undefined
        })))}</div>
        <div data-testid="data-table-data">{JSON.stringify(data)}</div>
      </div>
    );
  })
}));

// Mock translations
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

// Mock the date locale hook
vi.mock('../../hooks/use-date-locale', () => ({
  useDateLocale: () => ({
    code: 'en-US',
    weekStartsOn: 0
  })
}));

describe('TableChart', () => {
  // Mock for zeroFill function to return predictable values
  const mockChartData = [
    { timeframe: new Date('2023-01-01'), data: { T0: 100, T1: 200, T2: 300, T3: 400 }, compare: null },
    { timeframe: new Date('2023-01-02'), data: { T0: 150, T1: 250, T2: 350, T3: 450 }, compare: null }
  ];

  const mockCompareChartData = [
    { data: { T0: 100, T1: 200, T2: 300, T3: 400 }, key: 'usage' },
    { data: { T0: 90, T1: 190, T2: 290, T3: 390 }, key: 'average' }
  ];

  beforeEach(() => {
    vi.spyOn(Utils, 'zeroFill').mockReturnValue({
      chartData: mockChartData,
      compareChartData: mockCompareChartData
    });
    
    vi.spyOn(Utils, 'getDateFunctionsForGranularity').mockReturnValue({
      start: vi.fn(),
      add: vi.fn(),
      label: (date: Date) => date.toISOString(),
      amount: 1
    });
  });

  const defaultProps = {
    data: [],
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-01-31'),
    granularity: 'day',
    threeTime: true,
    compare: null,
    compareTo: null,
    unit: ''
  };

  it('renders the data table', () => {
    const { container } = render(<TableChart {...defaultProps} />);
    expect(container.querySelector('[data-testid="data-table"]')).not.toBeNull();
  });

  it('renders table with proper data when no comparison data', () => {
    const { container } = render(<TableChart {...defaultProps} />);
    const dataElement = container.querySelector('[data-testid="data-table-data"]');
    expect(dataElement).not.toBeNull();
    
    // Verify the data passed to the DataTable component is the chart data
    const dataJson = JSON.parse(dataElement!.textContent || '[]');
    
    // JSON.stringify converts Date objects to strings, so we need to check the structure instead of direct equality
    expect(dataJson.length).toBe(mockChartData.length);
    expect(Object.keys(dataJson[0])).toEqual(expect.arrayContaining(['data', 'compare']));
    expect(dataJson[0].data).toEqual(mockChartData[0].data);
  });

  it('should handle comparison data when provided', () => {
    const propsWithCompare = {
      ...defaultProps,
      compare: { 
        usage: { T0: 100, T1: 200, T2: 300, T3: 400 }, 
        average: { T0: 90, T1: 190, T2: 290, T3: 390 }, 
        cityEntry: null, 
        districtEntry: null 
      }
    };
    
    const { container } = render(<TableChart {...propsWithCompare} />);
    const dataElement = container.querySelector('[data-testid="data-table-data"]');
    expect(dataElement).not.toBeNull();
    
    // Verify the data passed to the DataTable component is the compare chart data
    const dataJson = JSON.parse(dataElement!.textContent || '[]');
    expect(dataJson).toEqual(mockCompareChartData);
  });

  it('should handle three time segments when threeTime is true', () => {
    const { container } = render(<TableChart {...defaultProps} />);
    const columnsElement = container.querySelector('[data-testid="data-table-columns"]');
    expect(columnsElement).not.toBeNull();
    
    // Verify that multiple columns are created for the time segments (T0, T1, T2, T3)
    const columnsJson = JSON.parse(columnsElement!.textContent || '[]');
    expect(columnsJson.length).toBeGreaterThan(1); // Should have at least the timeframe column plus T0-T3 columns
  });

  // New tests for improving function coverage

  it('should handle threeTime condition when granularity is hour', () => {
    const hourProps = {
      ...defaultProps,
      granularity: 'hour',
      threeTime: true
    };
    
    const { container } = render(<TableChart {...hourProps} />);
    const columnsElement = container.querySelector('[data-testid="data-table-columns"]');
    const columnsJson = JSON.parse(columnsElement!.textContent || '[]');
    
    // Should only have data column with accessorKey 'data'
    const timeColumns = columnsJson.filter((col: any) => col.accessorKey === 'data');
    expect(timeColumns.length).toBe(1);
  });

  it('should handle threeTime condition when T1/T2/T3 are null', () => {
    // Mock data where T1, T2, T3 are null
    vi.spyOn(Utils, 'zeroFill').mockReturnValueOnce({
      chartData: [
        { timeframe: new Date('2023-01-01'), data: { T0: 100, T1: null, T2: null, T3: null }, compare: null },
      ],
      compareChartData: []
    });

    const { container } = render(<TableChart {...defaultProps} />);
    const columnsElement = container.querySelector('[data-testid="data-table-columns"]');
    const columnsJson = JSON.parse(columnsElement!.textContent || '[]');
    
    // Should only have data column with accessorKey 'data'
    const timeColumns = columnsJson.filter((col: any) => col.accessorKey === 'data');
    expect(timeColumns.length).toBe(1);
  });

  it('should render column cells for comparison data correctly', () => {
    const propsWithCompare = {
      ...defaultProps,
      compare: { 
        usage: { T0: 100, T1: 200, T2: 300, T3: 400 }, 
        average: { T0: 90, T1: 190, T2: 290, T3: 390 },
        cityEntry: { T0: 80, T1: 180, T2: 280, T3: 380 },
        districtEntry: { T0: 70, T1: 170, T2: 270, T3: 370 }
      }
    };
    
    // Render the component
    render(<TableChart {...propsWithCompare} />);
    
    // Get the actual column definitions from our window storage
    const columns = (window as any).__tableTestColumns;
    
    // First column should be for the key (data type)
    expect(columns[0].accessorKey).toBe('key');
    
    // Mock a row for testing cell rendering
    const mockRow = {
      getValue: (key: string) => key === 'key' ? 'cityEntry' : { T0: 100, T1: 200 }
    };
    
    // Test that the cell rendering function works without errors
    const keyCell = columns[0].cell({ row: mockRow });
    expect(keyCell).toBeDefined();
    
    // Test data cell rendering function
    const dataCell = columns[1].cell({ row: mockRow });
    expect(dataCell).toBeDefined();
  });

  it('should render column cells for non-comparison data correctly', () => {
    // Render the component
    render(<TableChart {...defaultProps} />);
    
    // Get the actual column definitions from our window storage
    const columns = (window as any).__tableTestColumns;
    
    // First column should be for the timeframe
    expect(columns[0].accessorKey).toBe('timeframe');
    
    // Mock a row for testing cell rendering
    const mockRow = {
      getValue: (key: string) => key === 'timeframe' ? new Date('2023-01-01') : { T0: 100 }
    };
    
    // Test that the timeframe cell rendering function works without errors
    const timeframeCell = columns[0].cell({ row: mockRow });
    expect(timeframeCell).toBeDefined();
    
    // Test data cell rendering function
    const dataCell = columns[1].cell({ row: mockRow });
    expect(dataCell).toBeDefined();
  });

  it('should handle header rendering functions', () => {
    // Render the component
    render(<TableChart {...defaultProps} />);
    
    // Get the actual column definitions from our window storage
    const columns = (window as any).__tableTestColumns;
    
    // Test that the header rendering functions work
    const timeframeHeader = columns[0].header();
    expect(timeframeHeader).toBeDefined();
    
    const dataHeader = columns[1].header();
    expect(dataHeader).toBeDefined();
  });
});