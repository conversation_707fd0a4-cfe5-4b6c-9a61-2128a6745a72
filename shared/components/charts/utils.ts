import {
  addDays,
  addHours,
  addMonths,
  addYears,
  format,
  Locale,
  startOfDay,
  startOfHour,
  startOfMonth,
  startOfYear,
} from "date-fns";
import { ChartCompare, ChartData, ChartDataPoint } from "../../types/chart";

export const getDateFunctionsForGranularity = (
  granularity: string,
  locale: Locale
) => {
  switch (granularity) {
    case "hour":
      return {
        start: startOfHour,
        add: addHours,
        label: (date: Date) => format(date, "dd MMMM HH:mm", { locale }),
        amount: 1,
      };
    case "day":
      return {
        start: startOfDay,
        add: addDays,
        label: (date: Date) => format(date, "dd MMMM", { locale }),
        amount: 24,
      };
    case "month":
      return {
        start: startOfMonth,
        add: addMonths,
        label: (date: Date) => format(date, "MMMM yyyy", { locale }),
        amount: 30 * 24,
      };
    case "year":
      return {
        start: startOfYear,
        add: addYears,
        label: (date: Date) => format(date, "yyyy", { locale }),
        amount: 365 * 24,
      };
    default:
      throw new Error(`Unknown granularity: ${granularity}`);
  }
};

export const zeroFill = <HasCompare extends boolean>(
  data: ChartDataPoint[],
  compare: HasCompare extends true ? ChartCompare : null,
  startDate: Date,
  endDate: Date,
  granularity: string,
  locale: Locale
) => {
  const { add, start, amount } = getDateFunctionsForGranularity(
    granularity,
    locale
  );
  startDate = start(startDate);
  endDate = start(endDate);
  const zeroFilledData: (ChartDataPoint & { compare: typeof compare })[] = [];

  // find the closest data point to the current date
  const dataGetter = (dataSource: ChartDataPoint[], currentDate: Date) => {
    let closest = amount * 60 * 60 * 1000 * 0.25;
    let closestData: ChartData = { T0: 0, T1: null, T2: null, T3: null };
    for (const point of dataSource) {
      const diff = Math.abs(currentDate.getTime() - point.timeframe.getTime());
      if (diff < closest) {
        closest = diff;
        closestData = point.data;
      }
    }
    return closestData;
  };

  let first = true;
  let currentDate = start(startDate);
  while (currentDate < endDate) {
    currentDate = start(currentDate);
    const existingData = dataGetter(data, currentDate);

    if (!(first && existingData.T0 === 0)) {
      zeroFilledData.push({
        timeframe: currentDate,
        data: existingData,
        compare,
      });
    }
    first = false;
    currentDate = add(currentDate, 1);
  }

  const compareChartData =
    zeroFilledData && zeroFilledData.length > 0
      ? [
          { data: zeroFilledData[0].data, key: "usage" },
          ...Object.entries(zeroFilledData[0]?.compare ?? {}).map(
            ([key, data]) => ({
              data,
              key,
            })
          ),
        ]
      : [];

  return { chartData: zeroFilledData, compareChartData };
};
