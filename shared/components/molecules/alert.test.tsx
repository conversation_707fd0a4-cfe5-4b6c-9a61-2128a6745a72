/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import Alert from './alert';


// Mock the Iconify component
vi.mock('../atoms/Iconify', () => ({
  default: vi.fn((props) => (
    <div 
      data-testid="mock-iconify" 
      data-icon-name={props.name} 
      className={props.className} 
    />
  ))
}));

// Mock the cn utility
vi.mock('../../lib/utils', () => ({
  cn: (...inputs: any[]) => inputs.filter(Boolean).join(' ')
}));

describe('Alert component', () => {
  it('renders with default warning variant', () => {
    const { container } = render(
      <Alert title="Warning Title">
        <p>Warning content</p>
      </Alert>
    );
    
    // Check if title is rendered
    const titleElement = container.querySelector('h3');
    expect(titleElement).not.toBeNull();
    expect(titleElement?.textContent).toBe('Warning Title');
    
    // Check if content is rendered
    const contentElement = container.querySelector('.mt-2');
    expect(contentElement).not.toBeNull();
    expect(contentElement?.textContent).toBe('Warning content');
    
    // Check if icon is present with warning icon name
    const icon = container.querySelector('[data-testid="mock-iconify"]');
    expect(icon).not.toBeNull();
    expect(icon?.getAttribute('data-icon-name')).toBe('untitled:info-octagon');
    
    // Check if the background class is applied
    const alertContainer = container.firstChild as HTMLElement;
    expect(alertContainer).not.toBeNull();
    expect(alertContainer?.className).toContain('bg-yellow-50');
  });

  it('renders with error variant', () => {
    const { container } = render(
      <Alert title="Error Title" variant="error">
        <p>Error content</p>
      </Alert>
    );
    
    // Check if title is rendered with proper class
    const titleElement = container.querySelector('h3');
    expect(titleElement).not.toBeNull();
    expect(titleElement?.textContent).toBe('Error Title');
    expect(titleElement?.className).toContain('text-red-800');
    
    // Check if icon is present with error icon name
    const icon = container.querySelector('[data-testid="mock-iconify"]');
    expect(icon).not.toBeNull();
    expect(icon?.getAttribute('data-icon-name')).toBe('untitled:x-circle');
    
    // Check if the background class is applied
    const alertContainer = container.firstChild as HTMLElement;
    expect(alertContainer).not.toBeNull();
    expect(alertContainer?.className).toContain('bg-red-50');
  });

  it('renders with success variant', () => {
    const { container } = render(
      <Alert title="Success Title" variant="success">
        <p>Success content</p>
      </Alert>
    );
    
    // Check if title is rendered with proper class
    const titleElement = container.querySelector('h3');
    expect(titleElement).not.toBeNull();
    expect(titleElement?.textContent).toBe('Success Title');
    expect(titleElement?.className).toContain('text-green-800');
    
    // Check if icon is present with success icon name
    const icon = container.querySelector('[data-testid="mock-iconify"]');
    expect(icon).not.toBeNull();
    expect(icon?.getAttribute('data-icon-name')).toBe('untitled:check-circle');
    
    // Check if the background class is applied
    const alertContainer = container.firstChild as HTMLElement;
    expect(alertContainer).not.toBeNull();
    expect(alertContainer?.className).toContain('bg-green-50');
  });

  it('renders without icon when iconless is true', () => {
    const { container } = render(
      <Alert title="No Icon Alert" iconless={true}>
        <p>Content without icon</p>
      </Alert>
    );
    
    // Check if title is rendered
    const titleElement = container.querySelector('h3');
    expect(titleElement).not.toBeNull();
    expect(titleElement?.textContent).toBe('No Icon Alert');
    
    // Check that icon is not present
    const icon = container.querySelector('[data-testid="mock-iconify"]');
    expect(icon).toBeNull();
  });

  it('renders with actions', () => {
    const actions = (
      <button className="test-action-button">Action Button</button>
    );
    
    const { container } = render(
      <Alert title="Alert with Actions" actions={actions}>
        <p>Content with actions</p>
      </Alert>
    );
    
    // Check if action button is rendered
    const actionButton = container.querySelector('.test-action-button');
    expect(actionButton).not.toBeNull();
    expect(actionButton?.textContent).toBe('Action Button');
  });
});
