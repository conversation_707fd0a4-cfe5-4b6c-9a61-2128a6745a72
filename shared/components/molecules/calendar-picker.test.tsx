/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import { CalendarPicker } from './calendar-picker';
import { enUS } from 'date-fns/locale';

// Mock date-fns format function
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr, options) => {
    if (date instanceof Date) {
      return `Formatted: ${date.toISOString().split('T')[0]}`;
    }
    return 'Invalid date';
  })
}));

// Mock the Iconify component
vi.mock('../../components/atoms/Iconify', () => ({
  default: vi.fn(({ name, className }) => (
    <span data-testid="mock-iconify" data-icon-name={name} className={className} />
  ))
}));

// Mock UI components
vi.mock('../../components/ui/button', () => ({
  Button: vi.fn(({ children, className, variant }) => (
    <button 
      data-testid="mock-button" 
      className={className}
      data-variant={variant}
    >
      {children}
    </button>
  ))
}));

vi.mock('../../components/ui/popover', () => ({
  Popover: vi.fn(({ children, open, onOpenChange }) => (
    <div 
      data-testid="mock-popover" 
      data-open={open}
      onChange={() => onOpenChange && onOpenChange(!open)}
    >
      {children}
    </div>
  )),
  PopoverTrigger: vi.fn(({ children, asChild }) => (
    <div data-testid="mock-popover-trigger" data-as-child={asChild}>
      {children}
    </div>
  )),
  PopoverContent: vi.fn(({ children, className, align }) => (
    <div 
      data-testid="mock-popover-content" 
      className={className}
      data-align={align}
    >
      {children}
    </div>
  ))
}));

vi.mock('../../components/ui/calendar', () => ({
  Calendar: vi.fn((props) => (
    <div 
      data-testid="mock-calendar"
      data-mode={props.mode}
      data-initial-focus={props.initialFocus}
      data-number-of-months={props.numberOfMonths}
    >
      <button 
        data-testid="mock-calendar-select"
        onClick={() => {
          props.onSelect && props.onSelect({ from: new Date('2025-06-01'), to: new Date('2025-06-05') });
        }}
      >
        Select Range
      </button>
    </div>
  ))
}));

describe('CalendarPicker component', () => {
  const defaultProps = {
    locale: enUS,
    range: [null, null] as [Date | null, Date | null],
    onSelect: vi.fn(),
    placeholder: 'Select date range',
    isOpen: false,
    setIsOpen: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with placeholder when no range is selected', () => {
    const { container } = render(<CalendarPicker {...defaultProps} />);
    
    // Check if button is rendered with placeholder text
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.textContent).toContain('Select date range');
    
    // Check if calendar icon is rendered
    const icon = container.querySelector('[data-testid="mock-iconify"]');
    expect(icon).not.toBeNull();
    expect(icon?.getAttribute('data-icon-name')).toBe('untitled:calendar');
    expect(icon?.className).toContain('size-5');
  });
  
  it('renders with formatted date range when range is selected', () => {
    const range: [Date | null, Date | null] = [new Date('2025-06-01'), new Date('2025-06-05')];
    const { container } = render(<CalendarPicker {...defaultProps} range={range} />);
    
    // Check if button displays formatted date range
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.textContent).toContain('Formatted: 2025-06-01 - Formatted: 2025-06-05');
  });
  
  it('opens popover when isOpen is true', () => {
    const { container } = render(<CalendarPicker {...defaultProps} isOpen={true} />);
    
    // Check if popover is open
    const popover = container.querySelector('[data-testid="mock-popover"]');
    expect(popover).not.toBeNull();
    expect(popover?.getAttribute('data-open')).toBe('true');
    
    // Check if calendar is rendered
    const calendar = container.querySelector('[data-testid="mock-calendar"]');
    expect(calendar).not.toBeNull();
  });
  
  it('selects date range and closes popover when range is complete', () => {
    const setIsOpen = vi.fn();
    const onSelect = vi.fn();
    
    const { container } = render(
      <CalendarPicker 
        {...defaultProps} 
        isOpen={true} 
        setIsOpen={setIsOpen} 
        onSelect={onSelect} 
      />
    );
    
    // Simulate selecting a date range
    const selectButton = container.querySelector('[data-testid="mock-calendar-select"]');
    expect(selectButton).not.toBeNull();
    if (selectButton) {
      fireEvent.click(selectButton);
      
      // Check if onSelect was called with correct range
      expect(onSelect).toHaveBeenCalledTimes(1);
      expect(onSelect).toHaveBeenCalledWith([
        expect.any(Date),
        expect.any(Date)
      ]);
      
      // Check if setIsOpen was called to close popover
      expect(setIsOpen).toHaveBeenCalledTimes(1);
      expect(setIsOpen).toHaveBeenCalledWith(false);
    }
  });
  
  it('applies correct styling and alignment to popover content', () => {
    const { container } = render(<CalendarPicker {...defaultProps} isOpen={true} />);
    
    const popoverContent = container.querySelector('[data-testid="mock-popover-content"]');
    expect(popoverContent).not.toBeNull();
    expect(popoverContent?.className).toContain('w-auto');
    expect(popoverContent?.className).toContain('p-0');
    expect(popoverContent?.getAttribute('data-align')).toBe('end');
  });
});
