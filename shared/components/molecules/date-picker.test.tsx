/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import { DatePicker } from './date-picker';

// Mock date-fns format function
vi.mock('date-fns', () => ({
  format: vi.fn(() => 'January 1, 2025')
}));

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key === 'date_picker_placeholder' ? 'Select a date' : key,
  })
}));

// Mock useDateLocale hook
vi.mock('../../hooks/use-date-locale', () => ({
  useDateLocale: () => ({ code: 'en-US' })
}));

// Mock UI components
vi.mock('../../components/ui/button', () => ({
  Button: vi.fn(({ children, className, onClick }) => (
    <button 
      className={className}
      onClick={onClick}
      data-testid="mock-button"
    >
      {children}
    </button>
  ))
}));

vi.mock('lucide-react', () => ({
  CalendarIcon: vi.fn(() => <span data-testid="mock-calendar-icon" />)
}));

vi.mock('../../components/ui/popover', () => ({
  Popover: vi.fn(({ children }) => <div data-testid="mock-popover">{children}</div>),
  PopoverTrigger: vi.fn(({ children }) => <div data-testid="mock-popover-trigger">{children}</div>),
  PopoverContent: vi.fn(({ children, className }) => (
    <div data-testid="mock-popover-content" className={className}>
      {children}
    </div>
  ))
}));

vi.mock('../../components/ui/calendar', () => ({
  Calendar: vi.fn((props) => (
    <div 
      data-testid="mock-calendar" 
      data-mode={props.mode}
      data-selected={props.selected?.toISOString()}
    >
      <button 
        data-testid="mock-calendar-day"
        onClick={() => props.onSelect && props.onSelect(new Date('2025-01-01'))}
      >
        Click to select date
      </button>
    </div>
  ))
}));

describe('DatePicker component', () => {
  it('renders with placeholder when no date is selected', () => {
    const setDate = vi.fn();
    const { container } = render(<DatePicker value={undefined} setValue={setDate} />);
    
    // Check if placeholder text is displayed
    const buttonElement = container.querySelector('[data-testid="mock-button"]');
    expect(buttonElement).not.toBeNull();
    expect(buttonElement?.textContent).toContain('Select a date');
    
    // Check if calendar icon is rendered
    const calendarIcon = container.querySelector('[data-testid="mock-calendar-icon"]');
    expect(calendarIcon).not.toBeNull();
  });
  
  it('renders with formatted date when date is provided', () => {
    const setDate = vi.fn();
    const testDate = new Date('2025-01-01');
    const { container } = render(<DatePicker value={testDate} setValue={setDate} />);
    
    // Check if formatted date is displayed
    const buttonElement = container.querySelector('[data-testid="mock-button"]');
    expect(buttonElement).not.toBeNull();
    expect(buttonElement?.textContent).toContain('January 1, 2025');
  });
  
  it('opens calendar popover and selects a date', () => {
    const setDate = vi.fn();
    const { container } = render(<DatePicker value={undefined} setValue={setDate} />);
    
    // Check if popover components are rendered
    const popover = container.querySelector('[data-testid="mock-popover"]');
    expect(popover).not.toBeNull();
    
    const popoverTrigger = container.querySelector('[data-testid="mock-popover-trigger"]');
    expect(popoverTrigger).not.toBeNull();
    
    const popoverContent = container.querySelector('[data-testid="mock-popover-content"]');
    expect(popoverContent).not.toBeNull();
    
    // Check if calendar is rendered in the popover
    const calendar = container.querySelector('[data-testid="mock-calendar"]');
    expect(calendar).not.toBeNull();
    
    // Simulate selecting a date
    const calendarDay = container.querySelector('[data-testid="mock-calendar-day"]');
    expect(calendarDay).not.toBeNull();
    if (calendarDay) {
      fireEvent.click(calendarDay);
      
      // Check if setValue function was called with a new date
      expect(setDate).toHaveBeenCalledTimes(1);
      expect(setDate).toHaveBeenCalledWith(expect.any(Date));
    }
  });
  
  it('applies correct styling to the button', () => {
    const setDate = vi.fn();
    const { container } = render(<DatePicker value={undefined} setValue={setDate} />);
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    if (button) {
      expect(button.className).toContain('w-full');
      expect(button.className).toContain('justify-start');
      expect(button.className).toContain('text-left');
    }
  });
});
