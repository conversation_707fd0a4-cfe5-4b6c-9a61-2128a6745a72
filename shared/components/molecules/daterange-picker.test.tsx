/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/react';
import { DateRangePicker } from './daterange-picker';

// Mock date-fns format
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr, options) => {
    if (date instanceof Date) {
      return `Formatted: ${date.toISOString().split('T')[0]}`;
    }
    return 'Invalid date';
  })
}));

// Mock lucide-react
vi.mock('lucide-react', () => ({
  CalendarIcon: vi.fn(() => <span data-testid="mock-calendar-icon" />)
}));

// Mock react-day-picker
vi.mock('react-day-picker', () => ({
  DateRange: class DateRange {}
}));

// Mock UI components
vi.mock('../../components/ui/button', () => ({
  Button: vi.fn(({ children, className, variant, id }) => (
    <button 
      data-testid="mock-button" 
      className={className}
      data-variant={variant}
      id={id}
    >
      {children}
    </button>
  ))
}));

vi.mock('../../components/ui/popover', () => ({
  Popover: vi.fn(({ children }) => <div data-testid="mock-popover">{children}</div>),
  PopoverTrigger: vi.fn(({ children, asChild }) => (
    <div data-testid="mock-popover-trigger" data-as-child={asChild}>
      {children}
    </div>
  )),
  PopoverContent: vi.fn(({ children, className, align }) => (
    <div 
      data-testid="mock-popover-content" 
      className={className}
      data-align={align}
    >
      {children}
    </div>
  ))
}));

vi.mock('../../components/ui/calendar', () => ({
  Calendar: vi.fn((props) => (
    <div 
      data-testid="mock-calendar"
      data-mode={props.mode}
      data-initial-focus={props.initialFocus}
      data-number-of-months={props.numberOfMonths}
    >
      Calendar Component
    </div>
  ))
}));

// Mock hooks
vi.mock('../../hooks/use-date-locale', () => ({
  useDateLocale: () => ({ code: 'en-US' })
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key === 'date_picker_placeholder' ? 'Select a date range' : key,
  })
}));

describe('DateRangePicker component', () => {
  const defaultProps = {
    setValue: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with placeholder when no date range is selected', () => {
    const { container } = render(<DateRangePicker {...defaultProps} />);
    
    // Check if button is rendered with placeholder text
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.textContent).toBe('Select a date range');
    
    // Check if calendar icon is rendered
    const calendarIcon = container.querySelector('[data-testid="mock-calendar-icon"]');
    expect(calendarIcon).not.toBeNull();
    
    // Check button styling
    expect(button?.className).toContain('w-[300px]');
    expect(button?.className).toContain('justify-start');
    expect(button?.className).toContain('text-left');
    expect(button?.className).toContain('text-muted-foreground');
    expect(button?.getAttribute('data-variant')).toBe('outline');
    expect(button?.getAttribute('id')).toBe('date');
  });
  
  it('renders with formatted date when from date is selected', () => {
    const value = { from: new Date('2025-06-01') };
    const { container } = render(<DateRangePicker {...defaultProps} value={value} />);
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.textContent).toContain('Formatted: 2025-06-01');
    // The component might include a hyphen in its content, so let's not assert this
  });
  
  it('renders with formatted date range when both dates are selected', () => {
    const value = { from: new Date('2025-06-01'), to: new Date('2025-06-05') };
    const { container } = render(<DateRangePicker {...defaultProps} value={value} />);
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.textContent).toContain('Formatted: 2025-06-01 - Formatted: 2025-06-05');
  });
  
  it('renders calendar when popover is opened', () => {
    const { container } = render(<DateRangePicker {...defaultProps} />);
    
    // Check if popover components are rendered
    const popover = container.querySelector('[data-testid="mock-popover"]');
    expect(popover).not.toBeNull();
    
    const popoverTrigger = container.querySelector('[data-testid="mock-popover-trigger"]');
    expect(popoverTrigger).not.toBeNull();
    
    const popoverContent = container.querySelector('[data-testid="mock-popover-content"]');
    expect(popoverContent).not.toBeNull();
    
    // Check calendar props
    const calendar = container.querySelector('[data-testid="mock-calendar"]');
    expect(calendar).not.toBeNull();
    expect(calendar?.getAttribute('data-mode')).toBe('range');
    expect(calendar?.getAttribute('data-initial-focus')).toBe('true');
    expect(calendar?.getAttribute('data-number-of-months')).toBe('2');
  });
  
  it('applies custom className when provided', () => {
    const { container } = render(<DateRangePicker {...defaultProps} className="custom-class" />);
    
    // Find the container div with the grid class
    const divContainer = container.querySelector('.grid');
    expect(divContainer).not.toBeNull();
    expect(divContainer?.className).toContain('custom-class');
    expect(divContainer?.className).toContain('grid');
    expect(divContainer?.className).toContain('gap-2');
  });
  
  it('applies correct styling and alignment to popover content', () => {
    const { container } = render(<DateRangePicker {...defaultProps} />);
    
    const popoverContent = container.querySelector('[data-testid="mock-popover-content"]');
    expect(popoverContent).not.toBeNull();
    expect(popoverContent?.className).toContain('w-auto');
    expect(popoverContent?.className).toContain('p-0');
    expect(popoverContent?.getAttribute('data-align')).toBe('start');
  });
});
