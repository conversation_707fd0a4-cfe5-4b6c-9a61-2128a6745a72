/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import { LanguageSwitcher } from './language-switcher';

// Mock UI components
vi.mock('../ui/select', () => {
  const SelectMock = ({ children, value, onValueChange }: any) => (
    <div data-testid="mock-select" data-value={value}>
      {typeof children === 'function' ? children({ open: false }) : children}
      <button 
        data-testid="mock-select-change" 
        onClick={() => onValueChange && onValueChange('tr')}
      >
        Change Language
      </button>
    </div>
  );
  
  const SelectContentMock = ({ children }: any) => (
    <div data-testid="mock-select-content">{children}</div>
  );
  
  const SelectTriggerMock = ({ children, className }: any) => (
    <div data-testid="mock-select-trigger" className={className}>
      {children}
    </div>
  );
  
  const SelectValueMock = ({ children, placeholder }: any) => (
    <div data-testid="mock-select-value" data-placeholder={placeholder}>
      {children}
    </div>
  );
  
  const SelectItemMock = ({ children, value }: any) => (
    <div data-testid="mock-select-item" data-value={value}>
      {children}
    </div>
  );
  
  return {
    Select: SelectMock,
    SelectContent: SelectContentMock,
    SelectTrigger: SelectTriggerMock,
    SelectValue: SelectValueMock,
    SelectItem: SelectItemMock
  };
});

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        'selectLangTitle': 'Select Language',
        'setLanguageEnglish': 'English',
        'setLanguageTurkish': 'Turkish'
      };
      return translations[key] || key;
    },
    i18n: {
      language: 'en'
    }
  })
}));

describe('LanguageSwitcher component', () => {
  it('renders with current language selected', () => {
    const changeLanguage = vi.fn();
    const { container } = render(<LanguageSwitcher changeLanguage={changeLanguage} />);
    
    // Check if select component is rendered with correct value
    const select = container.querySelector('[data-testid="mock-select"]');
    expect(select).not.toBeNull();
    expect(select?.getAttribute('data-value')).toBe('en');
    
    // Check if SelectTrigger has the correct width
    const trigger = container.querySelector('[data-testid="mock-select-trigger"]');
    expect(trigger).not.toBeNull();
    expect(trigger?.className).toContain('w-[180px]');
    
    // Check if placeholder is displayed
    const value = container.querySelector('[data-testid="mock-select-value"]');
    expect(value).not.toBeNull();
    expect(value?.getAttribute('data-placeholder')).toBe('Select Language');
  });
  
  it('calls changeLanguage when language is selected', () => {
    const changeLanguage = vi.fn();
    const { container } = render(<LanguageSwitcher changeLanguage={changeLanguage} />);
    
    // Trigger language change
    const changeButton = container.querySelector('[data-testid="mock-select-change"]');
    expect(changeButton).not.toBeNull();
    if (changeButton) {
      fireEvent.click(changeButton);
      
      // Check if changeLanguage was called with correct value
      expect(changeLanguage).toHaveBeenCalledTimes(1);
      expect(changeLanguage).toHaveBeenCalledWith('tr');
    }
  });
  
  it('renders language options', () => {
    const changeLanguage = vi.fn();
    const { container } = render(<LanguageSwitcher changeLanguage={changeLanguage} />);
    
    // Check if Select Content is rendered
    const content = container.querySelector('[data-testid="mock-select-content"]');
    expect(content).not.toBeNull();
    
    // Check if language options are rendered
    const items = container.querySelectorAll('[data-testid="mock-select-item"]');
    expect(items.length).toBe(2);
    
    // Check English option
    expect(items[0].getAttribute('data-value')).toBe('en');
    expect(items[0].textContent).toBe('English');
    
    // Check Turkish option
    expect(items[1].getAttribute('data-value')).toBe('tr');
    expect(items[1].textContent).toBe('Turkish');
  });
});
