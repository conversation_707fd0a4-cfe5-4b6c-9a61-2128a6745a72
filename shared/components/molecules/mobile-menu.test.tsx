/**
 * @vitest-environment jsdom
 */
import { fireEvent, render } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MobileMenuButton } from './mobile-menu';

// Mock UI components
vi.mock('../ui/button', () => {
  const ButtonMock = ({ children, className, onClick, variant, size, 'aria-label': ariaLabel, ...props }: any) => (
    <button 
      data-testid="mock-button" 
      className={className}
      data-variant={variant}
      data-size={size}
      aria-label={ariaLabel}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
  
  return {
    Button: ButtonMock
  };
});

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  Menu: vi.fn(({ className }: any) => <span data-testid="mock-menu-icon" className={className} />)
}));

// Mock useSidebar hook - using a separate variable to allow mocking different return values
const toggleSidebarMock = vi.fn();
const useSidebarMock = vi.fn().mockReturnValue({
  toggleSidebar: toggleSidebarMock,
  isMobile: true // Default to mobile view for most tests
});

vi.mock('../ui/sidebar', () => ({
  useSidebar: () => useSidebarMock()
}));

describe('MobileMenuButton component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset default mock behavior
    useSidebarMock.mockReturnValue({
      toggleSidebar: toggleSidebarMock,
      isMobile: true
    });
  });
  
  it('renders correctly on mobile screens', () => {
    const { container } = render(<MobileMenuButton />);
    
    // Check if button is rendered
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    
    // Check button properties
    expect(button?.getAttribute('data-variant')).toBe('ghost');
    expect(button?.getAttribute('data-size')).toBe('icon');
    expect(button?.getAttribute('aria-label')).toBe('Toggle mobile menu');
    expect(button?.className).toContain('md:hidden');
    
    // Check if menu icon is rendered
    const menuIcon = container.querySelector('[data-testid="mock-menu-icon"]');
    expect(menuIcon).not.toBeNull();
    expect(menuIcon?.className).toContain('h-5');
    expect(menuIcon?.className).toContain('w-5');
  });
  
  it('applies additional className when provided', () => {
    const { container } = render(<MobileMenuButton className="test-class" />);
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.className).toContain('test-class');
    expect(button?.className).toContain('h-9');
    expect(button?.className).toContain('w-9');
    expect(button?.className).toContain('md:hidden');
  });
  
  it('calls toggleSidebar when clicked', () => {
    const { container } = render(<MobileMenuButton />);
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    if (button) {
      fireEvent.click(button);
      
      // Check if toggleSidebar was called
      expect(toggleSidebarMock).toHaveBeenCalledTimes(1);
    }
  });
  
  it('does not render on non-mobile screens', () => {
    // Override the mock to simulate non-mobile view
    useSidebarMock.mockReturnValueOnce({
      toggleSidebar: toggleSidebarMock,
      isMobile: false
    });
    
    const { container } = render(<MobileMenuButton />);
    
    // Component should not render anything
    expect(container.firstChild).toBeNull();
  });
  
  it('passes additional props to Button component', () => {
    const { container } = render(<MobileMenuButton data-custom-attr="test-value" id="test-id" />);
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.getAttribute('data-custom-attr')).toBe('test-value');
    expect(button?.getAttribute('id')).toBe('test-id');
  });
});
