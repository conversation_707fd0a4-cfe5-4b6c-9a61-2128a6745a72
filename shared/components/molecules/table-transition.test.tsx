/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import { AnimatedTransition } from './table-transition';
import React from 'react';

// Mock motion/react - using functional components instead of vi.fn()
vi.mock('motion/react', () => {
  // In React, key is not an actual prop that gets passed to the component
  // It's a special identifier used by React internally
  const MockMotionDiv = (props: any) => {
    const { children, initial, animate, exit, variants, transition, className } = props;
    
    // We need to manually extract what element we're rendering from context
    let testId = 'mock-motion-div-unknown';
    
    // Check the children or other props to determine what's being rendered
    if (props.children && typeof props.children?.props?.['data-testid'] === 'string') {
      if (props.children.props['data-testid'] === 'skeleton-content') {
        testId = 'mock-motion-div-skeleton';
      } else if (props.children.props['data-testid'] === 'child-content') {
        testId = 'mock-motion-div-data';
      }
    }
    
    return (
      <div 
        data-testid={testId}
        data-initial={initial}
        data-animate={animate}
        data-exit={exit}
        data-variants={JSON.stringify(variants || {})}
        data-transition={JSON.stringify(transition || {})}
        className={className}
      >
        {children}
      </div>
    );
  };
  
  return {
    AnimatePresence: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="mock-animate-presence">{children}</div>
    ),
    motion: {
      div: MockMotionDiv
    }
  };
});

describe('AnimatedTransition component', () => {
  it('renders skeleton when loading is true', () => {
    const skeletonContent = <div data-testid="skeleton-content">Loading...</div>;
    const childContent = <div data-testid="child-content">Loaded Content</div>;
    
    const { container } = render(
      <AnimatedTransition 
        loading={true} 
        skeleton={skeletonContent}
      >
        {childContent}
      </AnimatedTransition>
    );
    
    // Check if AnimatePresence is rendered
    const animatePresence = container.querySelector('[data-testid="mock-animate-presence"]');
    expect(animatePresence).not.toBeNull();
    
    // Check if skeleton motion.div is rendered
    const skeletonDiv = container.querySelector('[data-testid="mock-motion-div-skeleton"]');
    expect(skeletonDiv).not.toBeNull();
    expect(skeletonDiv?.getAttribute('data-initial')).toBe('hidden');
    expect(skeletonDiv?.getAttribute('data-animate')).toBe('visible');
    expect(skeletonDiv?.getAttribute('data-exit')).toBe('exit');
    
    // Check if skeleton content is rendered
    const skeletonContentEl = container.querySelector('[data-testid="skeleton-content"]');
    expect(skeletonContentEl).not.toBeNull();
    
    const childContentEl = container.querySelector('[data-testid="child-content"]');
    expect(childContentEl).toBeNull();
    
    // Check if classes are applied
    expect(skeletonDiv?.className).toContain('h-full');
    expect(skeletonDiv?.className).toContain('w-full');
  });
  
  it('renders children when loading is false', () => {
    const skeletonContent = <div data-testid="skeleton-content">Loading...</div>;
    const childContent = <div data-testid="child-content">Loaded Content</div>;
    
    const { container } = render(
      <AnimatedTransition 
        loading={false} 
        skeleton={skeletonContent}
      >
        {childContent}
      </AnimatedTransition>
    );
    
    // Check if AnimatePresence is rendered
    const animatePresence = container.querySelector('[data-testid="mock-animate-presence"]');
    expect(animatePresence).not.toBeNull();
    
    // Check if data motion.div is rendered
    const dataDiv = container.querySelector('[data-testid="mock-motion-div-data"]');
    expect(dataDiv).not.toBeNull();
    expect(dataDiv?.getAttribute('data-initial')).toBe('hidden');
    expect(dataDiv?.getAttribute('data-animate')).toBe('visible');
    expect(dataDiv?.getAttribute('data-exit')).toBe('exit');
    
    // Check if child content is rendered
    const childContentEl = container.querySelector('[data-testid="child-content"]');
    expect(childContentEl).not.toBeNull();
    
    const skeletonContentEl = container.querySelector('[data-testid="skeleton-content"]');
    expect(skeletonContentEl).toBeNull();
    
    // Check if classes are applied
    expect(dataDiv?.className).toContain('h-full');
    expect(dataDiv?.className).toContain('w-full');
  });
  
  it('applies correct animation variants', () => {
    const skeletonContent = <div data-testid="skeleton-content">Loading...</div>;
    const childContent = <div data-testid="child-content">Loaded Content</div>;
    
    const { container } = render(
      <AnimatedTransition 
        loading={true} 
        skeleton={skeletonContent}
      >
        {childContent}
      </AnimatedTransition>
    );
    
    const skeletonDiv = container.querySelector('[data-testid="mock-motion-div-skeleton"]');
    expect(skeletonDiv).not.toBeNull();
    
    const skeletonVariants = JSON.parse(skeletonDiv?.getAttribute('data-variants') || '{}');
    
    // Check if skeletonVariants are correctly applied
    expect(skeletonVariants).toHaveProperty('hidden');
    expect(skeletonVariants).toHaveProperty('visible');
    expect(skeletonVariants).toHaveProperty('exit');
    expect(skeletonVariants.hidden).toEqual({ opacity: 0, y: 0 });
    expect(skeletonVariants.visible).toEqual({ opacity: 1, y: 0 });
    expect(skeletonVariants.exit).toEqual({ opacity: 0, y: -10 });
    
    // Check if transition is correctly applied
    const transition = JSON.parse(skeletonDiv?.getAttribute('data-transition') || '{}');
    expect(transition).toHaveProperty('duration', 0.2);
    expect(transition).toHaveProperty('ease');
  });
});
