/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import { AuthAlert } from './alert';

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  AlertCircle: vi.fn(({ className }) => (
    <div data-testid="mock-alert-circle-icon" className={className} />
  ))
}));

describe('AuthAlert component', () => {
  const defaultProps = {
    title: 'Error Title',
    description: 'Error description message'
  };

  it('renders with correct title and description', () => {
    const { container } = render(<AuthAlert {...defaultProps} />);
    
    // Check if the component renders
    const alertComponent = container.firstChild;
    expect(alertComponent).not.toBeNull();
    
    // Check if title is rendered correctly
    const titleElement = container.querySelector('h2');
    expect(titleElement).not.toBeNull();
    expect(titleElement?.textContent).toBe(defaultProps.title);
    
    // Check if description is rendered correctly
    const descriptionElement = container.querySelector('p');
    expect(descriptionElement).not.toBeNull();
    expect(descriptionElement?.textContent).toBe(defaultProps.description);
  });
  
  it('renders AlertCircle icon', () => {
    const { container } = render(<AuthAlert {...defaultProps} />);
    
    // Check if the AlertCircle icon is rendered
    const iconElement = container.querySelector('[data-testid="mock-alert-circle-icon"]');
    expect(iconElement).not.toBeNull();
    expect(iconElement?.className).toContain('text-red-400');
    expect(iconElement?.className).toContain('h-5');
    expect(iconElement?.className).toContain('w-5');
  });
  
  it('applies correct styling to alert container', () => {
    const { container } = render(<AuthAlert {...defaultProps} />);
    
    // Check container styling
    const alertContainer = container.firstChild as HTMLElement;
    expect(alertContainer).not.toBeNull();
    expect(alertContainer.className).toContain('rounded-lg');
    expect(alertContainer.className).toContain('border');
    expect(alertContainer.className).toContain('bg-white');
    expect(alertContainer.className).toContain('shadow-sm');
    expect(alertContainer.className).toContain('my-6');
    
    // Check icon container animation
    const animatedContainer = container.querySelector('.animate-pulse');
    expect(animatedContainer).not.toBeNull();
    expect(animatedContainer?.className).toContain('border-red-300');
  });
});