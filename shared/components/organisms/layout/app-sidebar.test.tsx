/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach, beforeEach } from 'vitest';
import { render, cleanup, fireEvent } from '@testing-library/react';
import { AppSidebar } from './app-sidebar';
import React from 'react';

const mockSetOpenMobile = vi.fn();

// Create a variable to hold the current mock implementation
let mockUseSidebarReturn = {
  state: 'expanded',
  isMobile: false,
  setOpenMobile: mockSetOpenMobile,
};

vi.mock('../../ui/sidebar', () => ({
  Sidebar: ({ children, ...props }: any) => (
    <div data-testid="sidebar" {...props}>
      {children}
    </div>
  ),
  SidebarHeader: ({ children, ...props }: any) => (
    <div data-testid="sidebar-header" {...props}>
      {children}
    </div>
  ),
  SidebarContent: ({ children, ...props }: any) => (
    <div data-testid="sidebar-content" {...props}>
      {children}
    </div>
  ),
  SidebarFooter: ({ children, ...props }: any) => (
    <div data-testid="sidebar-footer" {...props}>
      {children}
    </div>
  ),
  SidebarTrigger: ({ children, ...props }: any) => (
    <button data-testid="sidebar-trigger" {...props}>
      {children}
    </button>
  ),
  useSidebar: () => mockUseSidebarReturn,
}));

vi.mock('../../atoms/logo', () => ({
  default: () => <div data-testid="logo">Logo</div>,
}));

vi.mock('../../atoms/Iconify', () => ({
  default: ({ icon }: any) => <span data-testid="iconify">{icon}</span>,
}));

// Mock the IconNames type
vi.mock('./types', async () => {
  const actual = await vi.importActual('./types');
  return {
    ...actual,
    // Override the type checking for tests
    __esModule: true,
  };
});

vi.mock('../../ui/button', () => ({
  Button: ({ children, ...props }: any) => (
    <button data-testid="button" {...props}>
      {children}
    </button>
  ),
}));

vi.mock('../../organisms/layout/nav-group', () => ({
  NavGroup: ({ items }: any) => <div data-testid="nav-group">{items?.length} items</div>,
}));

vi.mock('./nav-user', () => ({
  NavUser: ({ user }: any) => <div data-testid="nav-user">{user?.name}</div>,
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe('AppSidebar component', () => {
  // Reset mock implementation before each test
  beforeEach(() => {
    mockUseSidebarReturn = {
      state: 'expanded',
      isMobile: false,
      setOpenMobile: mockSetOpenMobile,
    };
  });

  // Clean up after each test
  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });
  
  // Using type assertion to bypass type checking for testing purposes
  const mockSidebarData = {
    navGroups: [
      {
        titleKey: 'Main',
        items: [
          { titleKey: 'Dashboard', url: '/dashboard', icon: 'mass:dashboard' },
          { titleKey: 'Users', url: '/users', icon: 'mass:users' },
        ]
      }
    ]
  } as any;

  const mockUser = {
    name: 'Test User',
    email: '<EMAIL>',
    avatar: 'avatar.jpg',
  };

  const mockApi = vi.fn();

  it('renders the sidebar with all components', () => {
    const { container } = render(
      <AppSidebar sidebarData={mockSidebarData} user={mockUser} api={mockApi} />
    );
    
    // Test that all main components are rendered
    const sidebar = container.querySelector('[data-testid="sidebar"]');
    const sidebarHeader = container.querySelector('[data-testid="sidebar-header"]');
    const sidebarContent = container.querySelector('[data-testid="sidebar-content"]');
    const logo = container.querySelector('[data-testid="logo"]');
    const navGroup = container.querySelector('[data-testid="nav-group"]');
    
    expect(sidebar).not.toBeNull();
    expect(sidebarHeader).not.toBeNull();
    expect(sidebarContent).not.toBeNull();
    expect(logo).not.toBeNull();
    expect(navGroup).not.toBeNull();
  });

  it('renders user component when user is provided', () => {
    const { container } = render(
      <AppSidebar sidebarData={mockSidebarData} user={mockUser} api={mockApi} />
    );
    
    const navUser = container.querySelector('[data-testid="nav-user"]');
    expect(navUser).not.toBeNull();
    expect(navUser?.textContent).toBe(mockUser.name);
  });

  it('handles array of sidebar data', () => {
    const multipleData = [
      mockSidebarData, 
      { ...mockSidebarData }
    ] as any[];
    
    const { container } = render(
      <AppSidebar sidebarData={multipleData} user={mockUser} api={mockApi} />
    );
    
    const sidebar = container.querySelector('[data-testid="sidebar"]');
    const sidebarContent = container.querySelector('[data-testid="sidebar-content"]');
    const navGroups = container.querySelectorAll('[data-testid="nav-group"]');
    
    expect(sidebar).not.toBeNull();
    expect(sidebarContent).not.toBeNull();
    expect(navGroups.length).toBeGreaterThan(1); // Should have multiple nav groups
  });

  it('renders close button on mobile and handles close action', () => {
    // Mock mobile state
    mockUseSidebarReturn = {
      state: 'expanded',
      isMobile: true,
      setOpenMobile: mockSetOpenMobile,
    };

    const { container } = render(
      <AppSidebar sidebarData={mockSidebarData} user={mockUser} api={mockApi} />
    );
    
    // Find the close button (X button)
    const closeButton = container.querySelector('button[data-testid="button"]');
    expect(closeButton).not.toBeNull();
    
    // Click the close button
    if (closeButton) {
      fireEvent.click(closeButton);
    }
    
    // Verify setOpenMobile was called with false
    expect(mockSetOpenMobile).toHaveBeenCalledWith(false);
  });

  it('handles nested navigation items correctly', () => {
    const nestedSidebarData = {
      navGroups: [
        {
          titleKey: 'Main',
          items: [
            { 
              titleKey: 'Dashboard', 
              url: '/dashboard', 
              icon: 'mass:dashboard' 
            },
            { 
              titleKey: 'Settings',
              icon: 'mass:settings',
              items: [
                { titleKey: 'General', url: '/settings/general' },
                { titleKey: 'Security', url: '/settings/security' }
              ]
            },
          ]
        }
      ]
    } as any;

    const { container } = render(
      <AppSidebar sidebarData={nestedSidebarData} user={mockUser} api={mockApi} />
    );
    
    const navGroup = container.querySelector('[data-testid="nav-group"]');
    expect(navGroup).not.toBeNull();
    expect(navGroup?.textContent).toContain('2 items');
  });

  it('handles collapsed state correctly', () => {
    // Mock collapsed state
    mockUseSidebarReturn = {
      state: 'collapsed',
      isMobile: false,
      setOpenMobile: mockSetOpenMobile,
    };

    const { container } = render(
      <AppSidebar sidebarData={mockSidebarData} user={mockUser} api={mockApi} />
    );
    
    const logo = container.querySelector('[data-testid="logo"]');
    expect(logo).not.toBeNull();
    
    // Should not show close button when not mobile
    const closeButton = container.querySelector('button[data-testid="button"]');
    expect(closeButton).toBeNull();
  });

  it('does not render user footer when user is not provided', () => {
    const { container } = render(
      <AppSidebar sidebarData={mockSidebarData} api={mockApi} />
    );
    
    const sidebarFooter = container.querySelector('[data-testid="sidebar-footer"]');
    const navUser = container.querySelector('[data-testid="nav-user"]');
    
    expect(sidebarFooter).toBeNull();
    expect(navUser).toBeNull();
  });

  it('handles navigation items with badges and disabled states', () => {
    const sidebarDataWithBadges = {
      navGroups: [
        {
          titleKey: 'Main',
          items: [
            { 
              titleKey: 'Dashboard', 
              url: '/dashboard', 
              icon: 'mass:dashboard',
              badge: '5',
              disabledKey: 'feature.dashboard'
            },
          ]
        }
      ]
    } as any;

    const { container } = render(
      <AppSidebar sidebarData={sidebarDataWithBadges} user={mockUser} api={mockApi} />
    );
    
    const navGroup = container.querySelector('[data-testid="nav-group"]');
    expect(navGroup).not.toBeNull();
  });
});
