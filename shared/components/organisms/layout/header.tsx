import type { IconNames } from "../../atoms/Iconify";
import Iconify from "../../atoms/Iconify";
import { cn } from "../../../lib/utils";
import { Link, useNavigate } from "@tanstack/react-router";
import React from "react";
import { useTranslation } from "react-i18next";
import { SidebarTrigger, useSidebar } from "../../ui/sidebar";
import { Button } from "../../ui/button";

export interface BreadcrumbItem {
  title?: React.ReactNode;
  icon?: IconNames;
  path?: string;
}

interface SidebarItem {
  icon?: IconNames;
  titleKey: string;
  url?: string;
  items?: SidebarItem[];
}

interface SidebarNavGroup {
  title?: string;
  items: SidebarItem[];
}

interface SidebarDataType {
  navGroups: SidebarNavGroup[];
}

interface HeaderProps extends Omit<React.HTMLAttributes<HTMLElement>, "title"> {
  title: React.ReactNode;
  description?: React.ReactNode;
  breadcrumb?: BreadcrumbItem[];
  subContent?: React.ReactNode;
  fixed?: boolean;
  sidebarData: SidebarDataType | SidebarDataType[] | undefined;
  ref?: React.Ref<HTMLElement>;
  backTo?: string;
  responsiveBreadcrumb?: boolean;
}

export function Header({
  title,
  description,
  breadcrumb,
  className,
  fixed,
  subContent,
  children,
  sidebarData,
  backTo,
  responsiveBreadcrumb = true,
  ...props
}: HeaderProps) {
  const [offset, setOffset] = React.useState(0);
  const { t } = useTranslation("common");
  const { toggleSidebar } = useSidebar();
  const navigate = useNavigate();

  React.useEffect(() => {
    const onScroll = () => {
      setOffset(document.body.scrollTop || document.documentElement.scrollTop);
    };

    document.addEventListener("scroll", onScroll, { passive: true });

    return () => document.removeEventListener("scroll", onScroll);
  }, []);

  const currentPath = window.location.pathname;

  const typedSidebarData = sidebarData as unknown as SidebarDataType;

  const sidebarBreadcrumbItem = typedSidebarData.navGroups
    .flatMap((group) => group.items)
    .find((item) => item.url === currentPath);

  let submenuBreadcrumb: { parent: SidebarItem, child: SidebarItem } | null = null;

  if (!sidebarBreadcrumbItem) {
    typedSidebarData.navGroups.forEach(group => {
      group.items.forEach(item => {
        if (item.items) {
          const childItem = item.items.find(subItem => subItem.url === currentPath);
          if (childItem) {
            submenuBreadcrumb = {
              parent: item as SidebarItem,
              child: childItem as SidebarItem
            };
          }
        }
      });
    });
  }

  return (
    <header
      className={cn(
        "flex items-center gap-3 bg-background sm:gap-4",
        fixed && "header-fixed peer/header fixed z-50 w-[inherit] rounded-md",
        offset > 10 && fixed ? "shadow" : "shadow-none",
        className
      )}
      {...props}
    >
      <div className="flex flex-col w-full gap-4 pt-12 px-6 container">
        <div className="w-full flex justify-between">
          <div className={cn(
            "flex gap-1 items-center text-sm text-gray-600 font-semibold",
            responsiveBreadcrumb && "flex-wrap"
          )}>

          <SidebarTrigger onClick={toggleSidebar} className="mr-4 hidden md:flex" />

          <Button
            variant="ghost"
            size="icon"
            className="md:hidden h-9 w-9 mr-2"
            onClick={toggleSidebar}
            aria-label="Toggle mobile menu"
          >
            <Iconify name="untitled:menu-01" className="h-5 w-5" />
          </Button>

          {backTo && (
            <Button
              variant="ghost"
              size="sm"
              className="flex gap-2 items-center mr-2"
              onClick={() => navigate({ to: backTo })}
              aria-label="Go back"
            >
              <Iconify name="untitled:arrow-left" className="h-4 w-4" />
              {t("go_back")}
            </Button>
          )}
            {breadcrumb ? (
              <>
                {breadcrumb.map((item, index) => {
                  // On mobile: hide all breadcrumb items except the last one when there's more than one item
                  // On desktop: show all items
                  const isMobileHidden = responsiveBreadcrumb && breadcrumb.length > 1 && index < breadcrumb.length - 1;
                  
                  return (
                    <React.Fragment key={index}>
                      {/* First breadcrumb item should always be visible in all screens if only one item exists */}
                      {index === 0 && breadcrumb.length > 1 && responsiveBreadcrumb && (
                        <div className="md:hidden flex items-center">
                          {item.icon && (
                            <Iconify
                              name={item.icon}
                              className="w-5 h-5 text-gray-500"
                            />
                          )}
                          {item.path ? (
                            <Link to={item.path} className="hover:underline">
                              <div className="font-semibold py-1 px-2">
                                {item.title}
                              </div>
                            </Link>
                          ) : (
                            <div className="font-semibold py-1 px-2">
                              {item.title}
                            </div>
                          )}
                          <Iconify
                            name="untitled:chevron-right"
                            className="w-4 h-4 text-gray-400"
                          />
                        </div>
                      )}

                      {/* Regular display for desktop or all items on mobile if responsiveBreadcrumb is false */}
                      <div className={cn(
                        isMobileHidden ? "hidden md:flex md:items-center" : "flex items-center"
                      )}>
                        {item.icon && (
                          <Iconify
                            name={item.icon}
                            className="w-5 h-5 text-gray-500"
                          />
                        )}

                        {index < breadcrumb.length - 1 && (
                          <>
                            {item.path ? (
                              <Link to={item.path} className="hover:underline">
                                <div className="font-semibold py-1 px-2">
                                  {item.title}
                                </div>
                              </Link>
                            ) : (
                              <div className="font-semibold py-1 px-2">
                                {item.title}
                              </div>
                            )}

                            <Iconify
                              name="untitled:chevron-right"
                              className="w-4 h-4 text-gray-400"
                            />
                          </>
                        )}

                        {index === breadcrumb.length - 1 && (
                          <div className="font-semibold py-1 px-2">
                            {item.title}
                          </div>
                        )}
                      </div>
                    </React.Fragment>
                  );
                })}
              </>
            ) : submenuBreadcrumb ? (
              <>
                {/* Parent item - hide on mobile when responsiveBreadcrumb is true */}
                <div className={cn(
                  "flex items-center",
                  responsiveBreadcrumb ? "hidden md:flex" : ""
                )}>
                  <Iconify
                    name={(submenuBreadcrumb as {parent: SidebarItem}).parent.icon as IconNames}
                    className="w-5 h-5 text-gray-500"
                  />

                  <div className="font-semibold py-1 px-2">
                    {t((submenuBreadcrumb as {parent: SidebarItem}).parent.titleKey)}
                  </div>
                  
                  <Iconify
                    name="untitled:chevron-right"
                    className="w-4 h-4 text-gray-400"
                  />
                </div>

                {/* Parent item for mobile - simplified version */}
                {responsiveBreadcrumb && (
                  <div className="md:hidden flex items-center">
                    <Iconify
                      name={(submenuBreadcrumb as {parent: SidebarItem}).parent.icon as IconNames}
                      className="w-5 h-5 text-gray-500"
                    />
                    <Iconify
                      name="untitled:chevron-right"
                      className="w-4 h-4 text-gray-400 mx-1"
                    />
                  </div>
                )}
                
                {/* Child item - always visible */}
                <div className="font-semibold py-1 px-2">
                  {t((submenuBreadcrumb as {child: SidebarItem}).child.titleKey)}
                </div>
              </>
            ) : sidebarBreadcrumbItem ? (
              <>
                <Iconify
                  name={sidebarBreadcrumbItem.icon as IconNames}
                  className="w-5 h-5 text-gray-500"
                />

                <Iconify
                  name="untitled:chevron-right"
                  className="w-4 h-4 text-gray-400"
                />

                <div className="font-semibold py-1 px-2">
                  {t(sidebarBreadcrumbItem.titleKey)}
                </div>
              </>
            ) : null}
          </div>
        </div>

        <div className="font-semibold text-5xl leading-[32px] items-center flex gap-2">{title}</div>
        {subContent || (
          <div className="max-w-4xl pb-4 text-base leading-6 text-gray-600">
            {description}
          </div>
        )}
      </div>

      {children}
    </header>
  );
}

Header.displayName = "Header";