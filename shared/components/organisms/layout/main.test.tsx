/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { Main } from './main';
import React from 'react';

// Mock motion/react
vi.mock('motion/react', () => {
  return {
    motion: {
      main: ({ className, children, ...props }: any) => (
        <main className={className} {...props}>
          {children}
        </main>
      ),
    }
  };
});

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/test-path'
  }
});

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Main component', () => {
  it('renders the main component with children', () => {
    const testContent = <div data-testid="test-content">Test Content</div>;
    
    const { container } = render(<Main>{testContent}</Main>);
    
    // Check that the children are rendered
    const content = container.querySelector('[data-testid="test-content"]');
    expect(content).not.toBeNull();
    expect(content?.textContent).toBe('Test Content');
  });

  it('renders with custom className', () => {
    const customClassName = 'custom-class';
    const testContent = <div>Test Content</div>;
    
    const { container } = render(
      <Main className={customClassName}>{testContent}</Main>
    );
    
    // The custom class should be applied to the component
    const mainElement = container.querySelector('main');
    expect(mainElement).not.toBeNull();
    expect(mainElement?.className).toContain(customClassName);
  });

  it('applies default className when no custom class is provided', () => {
    const testContent = <div>Test Content</div>;
    
    const { container } = render(<Main>{testContent}</Main>);
    
    // The default classes should be applied
    const mainElement = container.querySelector('main');
    expect(mainElement).not.toBeNull();
    expect(mainElement?.className).toContain('flex');
    expect(mainElement?.className).toContain('flex-col');
  });

  it('passes additional props to the main element', () => {
    const testContent = <div>Test Content</div>;
    const testId = 'test-main';
    
    const { container } = render(<Main data-testid={testId}>{testContent}</Main>);
    
    // The additional props should be applied
    const main = container.querySelector(`[data-testid="${testId}"]`);
    expect(main).not.toBeNull();
  });
});
