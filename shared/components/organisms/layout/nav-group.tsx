import type { ReactNode } from "react";
import type { IconNames } from "../../atoms/Iconify";
import type { NavCollapsible, NavGroupType, NavItem, NavLink } from "./types";
import { cn } from "../../../lib/utils";
import { Badge } from "../../ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../../ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "../../ui/sidebar";
import { Link, useLocation } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import Iconify from "../../atoms/Iconify";
import React from "react";
import { DocumentList } from "./document-list";
import { useManyGlobalSettings } from "../../../hooks/use-global-settings";

function BadgeRenderer(props: { elem: { badge?: string | React.FC } }) {
  const badge = props.elem.badge;
  if (!badge) return null;
  const data = typeof badge === "string" ? badge : badge({});
  if (!data || data === "0" || data instanceof Promise) return null;

  return (
    <div className="flex flex-1 items-center justify-end">
      <NavBadge>{data}</NavBadge>
    </div>
  );
}

export function NavGroup({
  title,
  titleKey,
  items,
  api,
}: NavGroupType & { title?: string } & { api: any }) {
  // todo: any
  const { state } = useSidebar();
  const href = useLocation({ select: (location) => location.href });

  const { data: disabledData } = useManyGlobalSettings(
    items
      .map((item) => item.disabledKey)
      .filter((item): item is string => !!item)
      .map((item) => `disabled.${item}`),
    api
  );

  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title || titleKey}</SidebarGroupLabel>
      <SidebarMenu>
        {items
          .filter(
            (item) =>
              disabledData?.[("disabled." + item.disabledKey) as string] !==
              "true"
          )
          .map((item) => {
            const key = `${item.titleKey}-${item.url}`;

            if (!item.items)
              return <SidebarMenuLink key={key} item={item} href={href} />;

            if (state === "collapsed") {
              return (
                <SidebarMenuCollapsedDropdown
                  key={key}
                  item={item}
                  href={href}
                />
              );
            }

            return <SidebarMenuCollapsible key={key} item={item} href={href} />;
          })}
      </SidebarMenu>
    </SidebarGroup>
  );
}

function NavBadge({ children }: { children: ReactNode }) {
  return (
    <Badge
      className="rounded-full px-2 py-0 text-xs"
      variant={"outline-primary"}
    >
      {children}
    </Badge>
  );
}

function SidebarMenuLink({ item, href }: { item: NavLink; href: string }) {
  const { setOpenMobile, isMobile } = useSidebar();
  const { t } = useTranslation("common");

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={checkIsActive(href, item)}
        className={cn(
          "w-full overflow-hidden",
          isMobile && "py-4 px-5"
        )}
        tooltip={t(item.titleKey)}
      >
        <Link to={item.url} onClick={() => setOpenMobile(false)}>
          {item.icon && <Iconify name={item.icon as IconNames} className={cn(isMobile && "size-6")} />}
          <span className={cn("truncate whitespace-nowrap", isMobile && "text-base")}>{t(item.titleKey)}</span>
          {item.badge && <BadgeRenderer elem={item} />}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

function SidebarMenuCollapsible({
  item,
  href,
}: {
  item: NavCollapsible;
  href: string;
}) {
  const { setOpenMobile, isMobile } = useSidebar();
  const { t } = useTranslation("common");
  const isHelpCenter = item.titleKey === "sidebar.items.help_support";

  return (
    <Collapsible
      asChild
      defaultOpen={checkIsActive(href, item, true)}
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton
            tooltip={t(item.titleKey)}
            isActive={checkIsActive(href, item, true)}
            className={cn(
              "overflow-hidden w-full",
              isMobile && "py-4 px-5"
            )}
          >
            {item.icon && <Iconify name={item.icon as IconNames} className={cn(isMobile && "size-6")} />}
            <span className={cn("truncate whitespace-nowrap", isMobile && "text-base")}>
              {t(item.titleKey)}
            </span>
            {item.badge && <BadgeRenderer elem={item} />}
            <Iconify
              name="untitled:chevron-down"
              className={cn(
                "text-gray-400 ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:-rotate-90",
                isMobile ? "size-5" : "size-4"
              )}
            />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className="CollapsibleContent">
          <SidebarMenuSub className={isMobile ? "px-5 py-2" : ""}>
            {item.items.map((subItem) => (
              <SidebarMenuSubItem key={subItem.titleKey}>
                <SidebarMenuSubButton
                  asChild
                  isActive={false}
                  className={cn(
                    checkIsActive(href, subItem) ? "bg-gray-100 text-gray-700" : "",
                    isMobile && "py-3 px-2"
                  )}
                >
                  <Link to={subItem.url} onClick={() => setOpenMobile(false)}>
                    {subItem.icon && (
                      <Iconify 
                        name={subItem.icon as IconNames}
                        className={isMobile ? "size-5" : "size-4"}
                      />
                    )}
                    <span className={isMobile ? "text-sm" : ""}>{t(subItem.titleKey)}</span>
                    {subItem.badge && <BadgeRenderer elem={subItem} />}
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            ))}

            {isHelpCenter && (
              <>
                <React.Suspense
                  fallback={
                    <SidebarMenuSubItem>
                      <div className="flex items-center justify-center py-2">
                        <div className="animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent"></div>
                      </div>
                    </SidebarMenuSubItem>
                  }
                >
                  <DocumentList />
                </React.Suspense>
              </>
            )}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
}

function SidebarMenuCollapsedDropdown({
  item,
  href,
}: {
  item: NavCollapsible;
  href: string;
}) {
  const { t } = useTranslation("common");
  const { isMobile } = useSidebar();
  const isHelpCenter = item.titleKey === "sidebar.items.help_support";

  return (
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            tooltip={t(item.titleKey)}
            isActive={checkIsActive(href, item)}
            className={cn(
              isMobile && "py-4 px-5"
            )}
          >
            {item.icon && <Iconify name={item.icon as IconNames} className={cn(isMobile && "size-6")} />}

            <span className={cn(isMobile && "text-base")}>{t(item.titleKey)}</span>
            {item.badge && <BadgeRenderer elem={item} />}
            <Iconify
              name="untitled:chevron-down"
              className={cn(
                "text-gray-400 ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:-rotate-90",
                isMobile ? "size-5" : "size-4"
              )}
            />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side={isMobile ? "bottom" : "right"}
          align={isMobile ? "center" : "start"}
          sideOffset={isMobile ? 8 : 4}
          className={cn("list-none", isMobile && "w-[calc(100vw-32px)] max-h-[50vh] overflow-auto")}
        >
          <DropdownMenuLabel>
            {t(item.titleKey)} {item.badge ? `(${item.badge})` : ""}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {item.items.map((sub) => (
            <DropdownMenuItem
              key={`${sub.titleKey}-${sub.url}`}
              asChild
              className="before:content-none"
            >
              <Link
                to={sub.url}
                className={`${checkIsActive(href, sub) ? "bg-gray-100 text-gray-700" : ""}`}
              >
                {sub.icon && <Iconify name={sub.icon as IconNames} />}

                <span className="max-w-52 text-wrap">{t(sub.titleKey)}</span>
                {sub.badge && <BadgeRenderer elem={sub} />}
              </Link>
            </DropdownMenuItem>
          ))}

          {isHelpCenter && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuLabel className="mt-2">
                {t("sidebar.items.documents.title")}
              </DropdownMenuLabel>
              <React.Suspense
                fallback={
                  <DropdownMenuItem disabled className="before:content-none">
                    <div className="flex items-center justify-center py-1 w-full">
                      <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                    </div>
                  </DropdownMenuItem>
                }
              >
                <div className="py-1 list-none">
                  <DocumentList />
                </div>
              </React.Suspense>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  );
}

function checkIsActive(href: string | undefined, item: NavItem, mainNav = false) {
  if (!href || typeof href !== 'string') {
    return false;
  }
  
  return (
    href === item.url ||
    href.split("?")[0] === item.url ||
    !!item?.items?.filter((i) => i.url === href).length ||
    (mainNav &&
      href.split("/")[1] !== "" &&
      href.split("/")[1] === item?.url?.split("/")[1]) ||
    (item.url &&
      typeof item.url === "string" &&
      href.startsWith(item.url) &&
      (href.length === item.url.length || href[item.url.length] === "/"))
  );
}
