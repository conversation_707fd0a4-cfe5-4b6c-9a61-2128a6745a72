/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';
import { NavUser } from './nav-user';
import React from 'react';

expect.extend(matchers);


vi.mock('../../atoms/Iconify', () => ({
  default: ({ icon }: any) => <span data-testid="iconify">{icon}</span>,
}));

vi.mock('../../ui/button', () => ({
  Button: ({ children, ...props }: any) => (
    <button data-testid="button" {...props}>
      {children}
    </button>
  ),
}));

vi.mock('../../ui/avatar', () => ({
  Avatar: ({ children, ...props }: any) => (
    <div data-testid="avatar" {...props}>
      {children}
    </div>
  ),
  AvatarFallback: ({ children, ...props }: any) => (
    <div data-testid="avatar-fallback" {...props}>
      {children}
    </div>
  ),
  AvatarImage: ({ src, ...props }: any) => (
    <img data-testid="avatar-image" src={src} {...props} alt="avatar" />
  ),
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));


vi.mock('../../ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: any) => <div data-testid="dropdown-menu">{children}</div>,
  DropdownMenuContent: ({ children }: any) => <div data-testid="dropdown-content">{children}</div>,
  DropdownMenuGroup: ({ children }: any) => <div data-testid="dropdown-group">{children}</div>,
  DropdownMenuItem: ({ children, asChild, ...props }: any) => {
    if (asChild && children?.type) {
      
      return React.cloneElement(children, { ...props, "data-testid": "dropdown-item" });
    }
    return <div data-testid="dropdown-item" {...props}>{children}</div>;
  },
  DropdownMenuLabel: ({ children }: any) => <div data-testid="dropdown-label">{children}</div>,
  DropdownMenuSeparator: () => <hr data-testid="dropdown-separator" />,
  DropdownMenuTrigger: ({ children, asChild }: any) => {
    if (asChild && children?.type) {
      
      return React.cloneElement(children, { "data-testid": "dropdown-trigger" });
    }
    return <div data-testid="dropdown-trigger">{children}</div>;
  },
}));


vi.mock('@tanstack/react-router', () => ({
  Link: ({ to, children, ...props }: any) => (
    <a href={to} data-testid="router-link" {...props}>{children}</a>
  )
}));


vi.mock('../../ui/sidebar', () => ({
  useSidebar: () => ({
    state: 'collapsed',  
    isMobile: false,
    setOpenMobile: vi.fn(),
    mobileOpen: false,
    toggleSidebar: vi.fn()
  }),
  SidebarMenu: ({ children, ...props }: any) => (
    <div data-testid="sidebar-menu" {...props}>
      {children}
    </div>
  ),
  SidebarMenuItem: ({ children, ...props }: any) => (
    <div data-testid="sidebar-menu-item" {...props}>
      {children}
    </div>
  ),
  SidebarMenuButton: ({ children, ...props }: any) => (
    <div data-testid="sidebar-menu-button" {...props}>
      {children}
    </div>
  ),
  SidebarProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="sidebar-provider">{children}</div>
  )
}));

describe('NavUser component', () => {
  beforeEach(() => {
    
    cleanup();
  });
  
  const mockUser = {
    name: 'Test User',
    email: '<EMAIL>',
    avatar: 'avatar.jpg',
  };

  it('renders the user component', () => {
    const { container } = render(<NavUser user={mockUser} />);
    
    
    const dropdownTriggers = container.querySelectorAll('[data-testid="dropdown-trigger"]');
    expect(dropdownTriggers.length).toBeGreaterThan(0);
    
    
    const avatars = container.querySelectorAll('[data-testid="avatar"]');
    expect(avatars.length).toBeGreaterThan(0);
  });

  it('renders without errors when no avatar is provided', () => {
    const userWithoutAvatar = { ...mockUser, avatar: '' };
    const { container } = render(<NavUser user={userWithoutAvatar} />);
    
    
    const dropdownTriggers = container.querySelectorAll('[data-testid="dropdown-trigger"]');
    expect(dropdownTriggers.length).toBeGreaterThan(0);
    
    
    const avatarImages = container.querySelectorAll('[data-testid="avatar-image"]');
    expect(avatarImages.length).toBeGreaterThan(0);
    expect(avatarImages[0]).not.toHaveAttribute('src', 'avatar.jpg');
  });
});
