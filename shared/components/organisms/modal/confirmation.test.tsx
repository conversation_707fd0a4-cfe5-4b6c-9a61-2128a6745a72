/**
 * @vitest-environment jsdom
 */
import { fireEvent, render } from '@testing-library/react';
import { afterAll, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import { ConfirmationDialog } from './confirmation';

vi.mock('../../atoms/featured-icon', () => ({
  default: vi.fn(({ name, className }) => (
    <div data-testid="mock-featured-icon" data-icon-name={name} className={className} />
  ))
}));

vi.mock('../../ui/button', () => ({
  Button: vi.fn(({ variant, className, children, onClick }) => {
    return (
      <button 
        data-testid="mock-button" 
        data-variant={variant}
        className={className}
        onClick={onClick}
      >
        {children}
      </button>
    );
  })
}));

vi.mock('../../ui/dialog', () => ({
  Dialog: vi.fn(({ open, onOpenChange, children }) => (
    <div data-testid="mock-dialog" data-open={open} data-on-open-change={!!onOpenChange}>
      {children}
    </div>
  )),
  DialogContent: vi.fn(({ onInteractOutside, children, className }) => (
    <div
      data-testid="mock-dialog-content"
      data-on-interact-outside={!!onInteractOutside}
      className={className}
      onClick={() => onInteractOutside && onInteractOutside()}
    >
      {children}
    </div>
  ))
}));

vi.mock('../../ui/drawer', () => ({
  Drawer: vi.fn(({ open, onOpenChange, children }) => (
    <div data-testid="mock-drawer" data-open={open} data-on-open-change={!!onOpenChange}>
      {children}
    </div>
  )),
  DrawerContent: vi.fn(({ onInteractOutside, children, className }) => (
    <div
      data-testid="mock-drawer-content"
      data-on-interact-outside={!!onInteractOutside}
      className={className}
      onClick={() => onInteractOutside && onInteractOutside()}
    >
      {children}
    </div>
  ))
}));

vi.mock('../../../hooks/use-media-query', () => ({
  default: vi.fn().mockReturnValue(true)
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'confirmation.modal_close.title': 'Default Title',
        'confirmation.modal_close.description': 'Default Description',
        'confirmation.modal_close.confirm': 'Confirm',
        'confirmation.modal_close.cancel': 'Cancel'
      };
      return translations[key] || key;
    }
  })
}));

vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useEffect: vi.fn((fn) => fn())
  };
});

beforeAll(() => {
  if (!window.requestAnimationFrame) {
    window.requestAnimationFrame = (callback: FrameRequestCallback) => {
      return setTimeout(callback, 0);
    };
  }
  window.requestAnimationFrame = vi.fn((cb: FrameRequestCallback) => setTimeout(cb, 0));
});

afterAll(() => {
  vi.restoreAllMocks();
});

describe('ConfirmationDialog component', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    onConfirm: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders Dialog on desktop view', () => {
    const { container } = render(<ConfirmationDialog {...defaultProps} />);
    expect(container.querySelector('[data-testid="mock-dialog"]')).not.toBeNull();
  });

  it('renders with default title and description when not provided', () => {
    const { getByText } = render(<ConfirmationDialog {...defaultProps} />);
    expect(getByText('Default Title')).not.toBeNull();
    expect(getByText('Default Description')).not.toBeNull();
  });

  it('renders with custom title and description when provided', () => {
    const customProps = { ...defaultProps, title: 'Custom Title', description: 'Custom Description' };
    const { getByText } = render(<ConfirmationDialog {...customProps} />);
    expect(getByText('Custom Title')).not.toBeNull();
    expect(getByText('Custom Description')).not.toBeNull();
  });

  it('renders confirmation and cancel buttons with default text', () => {
    const { getByText } = render(<ConfirmationDialog {...defaultProps} />);
    expect(getByText('Confirm')).not.toBeNull();
    expect(getByText('Cancel')).not.toBeNull();
  });

  it('renders confirmation and cancel buttons with custom text when provided', () => {
    const customProps = {
      ...defaultProps,
      confirmText: 'Custom Confirm',
      cancelText: 'Custom Cancel'
    };
    const { getByText } = render(<ConfirmationDialog {...customProps} />);
    expect(getByText('Custom Confirm')).not.toBeNull();
    expect(getByText('Custom Cancel')).not.toBeNull();
  });

  it('renders with custom icon when iconName is provided', () => {
    const customProps = {
      ...defaultProps,
      iconName: 'custom:icon'
    };
    const { container } = render(<ConfirmationDialog {...customProps} />);
    expect(container.querySelector('[data-testid="mock-featured-icon"]')).not.toBeNull();
  });

  it('resets pointerEvents when Dialog closes', async () => {
    // Ensure desktop mode
    const useMediaQuery = (await import('../../../hooks/use-media-query')).default;
    vi.mocked(useMediaQuery).mockReturnValue(true);

    document.body.style.pointerEvents = 'none';
    const { container } = render(<ConfirmationDialog {...defaultProps} />);
    const dialog = container.querySelector('[data-testid="mock-dialog"]');
    expect(dialog).not.toBeNull();
    const dialogContent = container.querySelector('[data-testid="mock-dialog-content"]');
    expect(dialogContent).not.toBeNull();
    fireEvent.click(dialogContent as Element);
    defaultProps.onClose();
    expect(document.body.style.pointerEvents).toBe('');
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('resets pointerEvents when DialogContent is interacted outside', async () => {
    // Ensure desktop mode
    const useMediaQuery = (await import('../../../hooks/use-media-query')).default;
    vi.mocked(useMediaQuery).mockReturnValue(true);

    document.body.style.pointerEvents = 'none';
    const { container } = render(<ConfirmationDialog {...defaultProps} />);
    const dialogContent = container.querySelector('[data-testid="mock-dialog-content"]');
    expect(dialogContent).not.toBeNull();
    fireEvent.click(dialogContent as Element);
    expect(document.body.style.pointerEvents).toBe('');
  });

  it('resets pointerEvents when Drawer closes', async () => {
    const useMediaQuery = (await import('../../../hooks/use-media-query')).default;
    vi.mocked(useMediaQuery).mockReturnValueOnce(false);
    document.body.style.pointerEvents = 'none';
    const { container } = render(<ConfirmationDialog {...defaultProps} />);
    const drawer = container.querySelector('[data-testid="mock-drawer"]');
    expect(drawer).not.toBeNull();
    const drawerContent = container.querySelector('[data-testid="mock-drawer-content"]');
    expect(drawerContent).not.toBeNull();
    fireEvent.click(drawerContent as Element);
    defaultProps.onClose();
    expect(document.body.style.pointerEvents).toBe('');
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('resets pointerEvents when DrawerContent is interacted outside', async () => {
    const useMediaQuery = (await import('../../../hooks/use-media-query')).default;
    vi.mocked(useMediaQuery).mockReturnValueOnce(false);
    document.body.style.pointerEvents = 'none';
    const { container } = render(<ConfirmationDialog {...defaultProps} />);
    const drawerContent = container.querySelector('[data-testid="mock-drawer-content"]');
    expect(drawerContent).not.toBeNull();
    fireEvent.click(drawerContent as Element);
    expect(document.body.style.pointerEvents).toBe('');
  });
});