// filepath: /Users/<USER>/Desktop/development/frontend/shared/components/organisms/modal/header.test.tsx
/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import React from 'react';
import { render, cleanup } from '@testing-library/react';
import ModalHeader from './header';

// Mock UI components
vi.mock('../../ui/dialog', () => ({
  DialogHeader: vi.fn(({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="mock-dialog-header" className={className}>{children}</div>
  )),
  DialogTitle: vi.fn(({ children }: { children: React.ReactNode }) => (
    <div data-testid="mock-dialog-title">{children}</div>
  )),
  DialogDescription: vi.fn(({ children }: { children: React.ReactNode }) => (
    <div data-testid="mock-dialog-description">{children}</div>
  ))
}));

vi.mock('../../ui/drawer', () => ({
  DrawerHeader: vi.fn(({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="mock-drawer-header" className={className}>{children}</div>
  )),
  DrawerTitle: vi.fn(({ children }: { children: React.ReactNode }) => (
    <div data-testid="mock-drawer-title">{children}</div>
  )),
  DrawerDescription: vi.fn(({ children }: { children: React.ReactNode }) => (
    <div data-testid="mock-drawer-description">{children}</div>
  ))
}));

// Mock hooks - this is the critical part!
const mockUseMediaQuery = vi.fn().mockImplementation(() => true);
vi.mock('../../../hooks/use-media-query', () => ({
  __esModule: true,
  default: () => mockUseMediaQuery()
}));

describe('ModalHeader component', () => {
  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });

  it('renders the dialog header with title', () => {
    const { container } = render(
      <ModalHeader 
        title="Test Title" 
      />
    );
    
    const dialogHeader = container.querySelector('[data-testid="mock-dialog-header"]');
    const dialogTitle = container.querySelector('[data-testid="mock-dialog-title"]');
    expect(dialogHeader).not.toBeNull();
    expect(dialogTitle).not.toBeNull();
    expect(dialogTitle?.textContent).toBe('Test Title');
  });

  it('renders the dialog header with description when provided', () => {
    const { container } = render(
      <ModalHeader 
        title="Test Title" 
        description="Test Description"
      />
    );
    
    const dialogDescription = container.querySelector('[data-testid="mock-dialog-description"]');
    expect(dialogDescription).not.toBeNull();
    expect(dialogDescription?.textContent).toBe('Test Description');
  });

  it('uses drawer components on mobile', () => {
    mockUseMediaQuery.mockImplementation(() => false);
    
    const { container } = render(
      <ModalHeader 
        title="Test Title" 
      />
    );
    
    const drawerHeader = container.querySelector('[data-testid="mock-drawer-header"]');
    const drawerTitle = container.querySelector('[data-testid="mock-drawer-title"]');
    expect(drawerHeader).not.toBeNull();
    expect(drawerTitle).not.toBeNull();
  });
  
  // Alternative test approach (skipped for now)
  it.skip('uses drawer components on mobile (alternative approach)', () => {
    // This test is skipped until we can properly mock the useMediaQuery hook
    // The issue is that the module can't be found in the test environment
  });
});
