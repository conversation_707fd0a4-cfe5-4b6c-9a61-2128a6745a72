/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';
import { ModalContainer } from './index';
import React from 'react';

expect.extend(matchers);

vi.mock('../../ui/dialog', () => ({
  Dialog: ({ children, open = true, onOpenChange, ...props }: any) => {
    React.useEffect(() => {
     
      const dialogEl = document.querySelector('[data-testid="dialog"]');
      if (dialogEl) {
        (dialogEl as any).__onOpenChange = onOpenChange;
      }
    }, [onOpenChange]);
    
    return open ? (
      <div 
        data-testid="dialog" 
        data-state={open ? "open" : "closed"} 
        {...props}
      >
        {children}
      </div>
    ) : null;
  },
  DialogContent: ({ children, onPointerDownOutside, onInteractOutside, className, ...props }: any) => {
    React.useEffect(() => {
     
      const contentEl = document.querySelector('[data-testid="dialog-content"]');
      if (contentEl) {
        (contentEl as any).__onPointerDownOutside = onPointerDownOutside;
        (contentEl as any).__onInteractOutside = onInteractOutside;
      }
    }, [onPointerDownOutside, onInteractOutside]);
    
    return (
      <div 
        data-testid="dialog-content" 
        className={className}
        {...props}
      >
        {children}
      </div>
    );
  }
}));

vi.mock('../../ui/drawer', () => ({
  Drawer: ({ children, open, onOpenChange, ...props }: any) => {
    React.useEffect(() => {
      const drawerEl = document.querySelector('[data-testid="drawer"]');
      if (drawerEl) {
        (drawerEl as any).__onOpenChange = onOpenChange;
      }
    }, [onOpenChange]);
    
    return open ? (
      <div 
        data-testid="drawer" 
        data-state={open ? "open" : "closed"}
        {...props}
      >
        {children}
      </div>
    ) : null;
  },
  DrawerContent: ({ children, onPointerDownOutside, onInteractOutside, className, ...props }: any) => {
    React.useEffect(() => {
      const contentEl = document.querySelector('[data-testid="drawer-content"]');
      if (contentEl) {
        (contentEl as any).__onPointerDownOutside = onPointerDownOutside;
        (contentEl as any).__onInteractOutside = onInteractOutside;
      }
    }, [onPointerDownOutside, onInteractOutside]);
    
    return (
      <div 
        data-testid="drawer-content"
        className={className}
        {...props}
      >
        {children}
      </div>
    );
  }
}));

// Mock hooks
const mockRemove = vi.fn();
const mockSafeTryClose = vi.fn();
const mockPatch = vi.fn();

let mockModals: any[] = [];
let mockIsDesktop = true;

vi.mock('./provider', () => ({
  useModal: () => ({
    modals: mockModals,
    remove: mockRemove,
    safeTryClose: mockSafeTryClose,
    patch: mockPatch,
  }),
}));

vi.mock('../../../hooks/use-media-query', () => ({
  default: () => mockIsDesktop,
}));

vi.mock('./header', () => ({
  default: ({ title, ...props }: any) => (
    <div data-testid="modal-header" {...props}>
      {title}
    </div>
  ),
}));

describe('ModalContainer', () => {
  beforeEach(() => {
    mockRemove.mockClear();
    mockSafeTryClose.mockClear();
    mockPatch.mockClear();
    document.body.style.pointerEvents = '';
    mockIsDesktop = true;
    mockModals = [
      {
        id: '1',
        name: 'test-modal',
        component: ({ show, onHide }: any) => (
          <div data-testid="modal-content">
            Modal Content
            <button data-testid="close-button" onClick={onHide}>Close</button>
          </div>
        ),
        props: {
          header: {
            title: 'Test Modal',
            description: 'Test Description',
          },
        },
        show: true,
      }
    ];
  });

  afterEach(() => {
    document.body.style.pointerEvents = '';
  });

  it('renders nothing when no modals are present', () => {
    mockModals = [];
    const { container } = render(<ModalContainer />);
    expect(container.firstChild).toBeNull();
  });

  it('renders the modal container', () => {
    const { container } = render(<ModalContainer />);
    const dialogElement = container.querySelector('[data-testid="dialog"]');
    expect(dialogElement).toBeInTheDocument();
  });

  it('patches modals without confirmation props', () => {
    render(<ModalContainer />);
    expect(mockPatch).toHaveBeenCalledWith({
      name: 'test-modal',
      confirmationProps: expect.objectContaining({
        title: expect.any(String),
        description: expect.any(String),
        confirmText: expect.any(String),
        cancelText: expect.any(String),
        iconName: expect.any(String),
      }),
    });
  });

  it('does not patch modals with existing confirmation props', () => {
    const modalWithConfirmClose = { 
      ...mockModals[0], 
      props: { ...mockModals[0].props, confirmClose: true } 
    };
    mockModals[0] = modalWithConfirmClose;
    
    render(<ModalContainer />);
    expect(mockPatch).not.toHaveBeenCalled();
  });

  it('renders on mobile view with drawer components', () => {
    vi.doMock('../../../hooks/use-media-query', () => ({
      default: () => false,
    }));
    
    render(<ModalContainer />);
   
   
  });

  it('handles escape key to close modal', () => {
    render(<ModalContainer />);
    
    const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
    document.dispatchEvent(escapeEvent);
    
    expect(mockSafeTryClose).toHaveBeenCalledWith('test-modal');
  });

  it('does not close modal on escape when disableClose is true', () => {
    mockModals[0].props.disableClose = true;
    render(<ModalContainer />);
    
    const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
    document.dispatchEvent(escapeEvent);
    
    expect(mockSafeTryClose).not.toHaveBeenCalled();
  });

  it('resets body pointer events when modals are closed', () => {
    document.body.style.pointerEvents = 'none';
    mockModals = [];
    render(<ModalContainer />);
    
    expect(document.body.style.pointerEvents).toBe('');
  });

  it('handles visibility change event', () => {
    document.body.style.pointerEvents = 'none';
    const { rerender } = render(<ModalContainer />);
    
    mockModals = [];
    rerender(<ModalContainer />);
    
    const visibilityEvent = new Event('visibilitychange');
    window.dispatchEvent(visibilityEvent);
    
    expect(document.body.style.pointerEvents).toBe('');
  });

  it('renders with different modal sizes', () => {
    mockModals[0].props.size = 'large';
    const { container } = render(<ModalContainer />);
    
    const dialogContent = container.querySelector('[data-testid="dialog-content"]');
    expect(dialogContent?.className).toContain('sm:max-w-[900px]');
  });

  it('handles overflow hidden prop correctly', () => {
    mockModals[0].props.overflowHidden = false;
    const { container } = render(<ModalContainer />);
    
    const dialogContent = container.querySelector('[data-testid="dialog-content"]');
    expect(dialogContent?.className).not.toContain('overflow-hidden');
  });

  it('renders drawer on mobile view', () => {
    mockIsDesktop = false;
    const { container } = render(<ModalContainer />);
    
    expect(container.querySelector('[data-testid="drawer"]')).toBeInTheDocument();
    expect(container.querySelector('[data-testid="dialog"]')).not.toBeInTheDocument();
  });

  it('calls onHide when modal close button is clicked', () => {
    const { container } = render(<ModalContainer />);
    
   
    const closeButton = container.querySelector('[data-testid="close-button"]');
    expect(closeButton).toBeInTheDocument();
    fireEvent.click(closeButton!);
    
    expect(mockSafeTryClose).toHaveBeenCalledWith('test-modal');
  });

  it('handles dialog open change', async () => {
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const dialog = document.querySelector('[data-testid="dialog"]');
      expect(dialog).toBeInTheDocument();
    });
    
    const dialog = document.querySelector('[data-testid="dialog"]') as any;
    const onOpenChange = dialog.__onOpenChange;
    if (onOpenChange) {
      onOpenChange(false);
    }
    
    expect(mockSafeTryClose).toHaveBeenCalledWith('test-modal');
  });

  it('prevents closing when clicking on popper content', async () => {
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const dialogContent = document.querySelector('[data-testid="dialog-content"]');
      expect(dialogContent).toBeInTheDocument();
    });
    
    const mockPreventDefault = vi.fn();
    const mockTarget = document.createElement('div');
    mockTarget.setAttribute('data-radix-popper-content-wrapper', 'true');
    
    const event = {
      target: mockTarget,
      preventDefault: mockPreventDefault,
    };
    
    const dialogContent = document.querySelector('[data-testid="dialog-content"]') as any;
    const onPointerDownOutside = dialogContent.__onPointerDownOutside;
    if (onPointerDownOutside) {
      onPointerDownOutside(event);
    }
    
    expect(mockPreventDefault).toHaveBeenCalled();
  });

  it('resets pointer events on interact outside', async () => {
    document.body.style.pointerEvents = 'none';
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const dialogContent = document.querySelector('[data-testid="dialog-content"]');
      expect(dialogContent).toBeInTheDocument();
    });
    
    const dialogContent = document.querySelector('[data-testid="dialog-content"]') as any;
    const onInteractOutside = dialogContent.__onInteractOutside;
    if (onInteractOutside) {
      onInteractOutside();
    }
    
    expect(document.body.style.pointerEvents).toBe('');
  });

  it('handles multiple modals closing', () => {
    mockModals = [
      {
        id: '1',
        name: 'modal-1',
        component: () => <div>Modal 1</div>,
        props: {},
        show: true,
      },
      {
        id: '2',
        name: 'modal-2',
        component: () => <div>Modal 2</div>,
        props: {},
        show: true,
      },
    ];
    
    render(<ModalContainer />);
    
    const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
    document.dispatchEvent(escapeEvent);
    
    expect(mockSafeTryClose).toHaveBeenCalledWith('modal-1');
    expect(mockSafeTryClose).toHaveBeenCalledWith('modal-2');
  });

  it('cleans up event listeners on unmount', () => {
    const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');
    const windowRemoveEventListenerSpy = vi.spyOn(window, 'removeEventListener');
    
    const { unmount } = render(<ModalContainer />);
    unmount();
    
    expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function), { capture: true });
    expect(windowRemoveEventListenerSpy).toHaveBeenCalledWith('visibilitychange', expect.any(Function));
  });

  it('does not patch modals with alwaysConfirmClose prop', () => {
    mockModals[0].props.alwaysConfirmClose = true;
    render(<ModalContainer />);
    expect(mockPatch).not.toHaveBeenCalled();
  });

  it('does not patch modals with confirmationProps', () => {
    mockModals[0].props.confirmationProps = { title: 'Custom' };
    render(<ModalContainer />);
    expect(mockPatch).not.toHaveBeenCalled();
  });

  it('handles drawer open change on mobile', async () => {
    mockIsDesktop = false;
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const drawer = document.querySelector('[data-testid="drawer"]');
      expect(drawer).toBeInTheDocument();
    });
    
    const drawer = document.querySelector('[data-testid="drawer"]') as any;
    const onOpenChange = drawer.__onOpenChange;
    if (onOpenChange) {
      onOpenChange(false);
    }
    
    expect(mockSafeTryClose).toHaveBeenCalledWith('test-modal');
  });

  it('prevents closing drawer when clicking on popper content', async () => {
    mockIsDesktop = false;
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const drawerContent = document.querySelector('[data-testid="drawer-content"]');
      expect(drawerContent).toBeInTheDocument();
    });
    
    const mockPreventDefault = vi.fn();
    const mockTarget = document.createElement('div');
    mockTarget.setAttribute('data-radix-popper-content-wrapper', 'true');
    
    const event = {
      target: mockTarget,
      preventDefault: mockPreventDefault,
    };
    
    const drawerContent = document.querySelector('[data-testid="drawer-content"]') as any;
    const onPointerDownOutside = drawerContent.__onPointerDownOutside;
    if (onPointerDownOutside) {
      onPointerDownOutside(event);
    }
    
    expect(mockPreventDefault).toHaveBeenCalled();
  });

  it('resets pointer events on drawer interact outside', async () => {
    mockIsDesktop = false;
    document.body.style.pointerEvents = 'none';
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const drawerContent = document.querySelector('[data-testid="drawer-content"]');
      expect(drawerContent).toBeInTheDocument();
    });
    
    const drawerContent = document.querySelector('[data-testid="drawer-content"]') as any;
    const onInteractOutside = drawerContent.__onInteractOutside;
    if (onInteractOutside) {
      onInteractOutside();
    }
    
    expect(document.body.style.pointerEvents).toBe('');
  });

  it('does not close dialog when disableClose is true', async () => {
    mockModals[0].props.disableClose = true;
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const dialog = document.querySelector('[data-testid="dialog"]');
      expect(dialog).toBeInTheDocument();
    });
    
    const dialog = document.querySelector('[data-testid="dialog"]') as any;
    const onOpenChange = dialog.__onOpenChange;
    if (onOpenChange) {
      onOpenChange(false);
    }
    
    expect(mockSafeTryClose).not.toHaveBeenCalled();
  });

  it('renders with small size class by default', () => {
    const { container } = render(<ModalContainer />);
    
   
    const dialogContent = container.querySelector('[data-testid="dialog-content"]');
    expect(dialogContent).toBeInTheDocument();
    expect(dialogContent?.className).toContain('sm:max-w-[425px]');
  });

  it('handles visibility change when body pointer events are none', () => {
    document.body.style.pointerEvents = 'none';
    render(<ModalContainer />);
    
    const visibilityEvent = new Event('visibilitychange');
    window.dispatchEvent(visibilityEvent);
    
   
    expect(document.body.style.pointerEvents).toBe('none');
  });

  it('clears body pointer events when closing all modals', async () => {
    document.body.style.pointerEvents = 'none';
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const dialog = document.querySelector('[data-testid="dialog"]');
      expect(dialog).toBeInTheDocument();
    });
    
    const dialog = document.querySelector('[data-testid="dialog"]') as any;
    const onOpenChange = dialog.__onOpenChange;
    if (onOpenChange) {
      onOpenChange(false);
    }
    
    expect(document.body.style.pointerEvents).toBe('');
  });

  it('handles pointer down outside without radix popper', async () => {
    render(<ModalContainer />);
    
   
    await waitFor(() => {
      const dialogContent = document.querySelector('[data-testid="dialog-content"]') as any;
      expect(dialogContent.__onPointerDownOutside).toBeDefined();
    });
    
    const mockPreventDefault = vi.fn();
    const mockTarget = document.createElement('div');
   
    
    const event = {
      target: mockTarget,
      preventDefault: mockPreventDefault,
    };
    
    const dialogContent = document.querySelector('[data-testid="dialog-content"]') as any;
    const onPointerDownOutside = dialogContent.__onPointerDownOutside;
    if (onPointerDownOutside) {
      onPointerDownOutside(event);
    }
    
    expect(mockPreventDefault).not.toHaveBeenCalled();
  });

  it('handles escape key with stopPropagation', () => {
    render(<ModalContainer />);
    
    const mockStopPropagation = vi.fn();
    const escapeEvent = new KeyboardEvent('keydown', { 
      key: 'Escape',
      bubbles: true,
      cancelable: true
    });
    Object.defineProperty(escapeEvent, 'stopPropagation', {
      value: mockStopPropagation,
      writable: true
    });
    
    document.dispatchEvent(escapeEvent);
    
    expect(mockStopPropagation).toHaveBeenCalled();
    expect(mockSafeTryClose).toHaveBeenCalledWith('test-modal');
  });

  it('uses dialogRef for content', () => {
    const { container } = render(<ModalContainer />);
    
   
    const dialogContent = container.querySelector('[data-testid="dialog-content"]');
    expect(dialogContent).toBeInTheDocument();
  });

  it('handles modal with no header', () => {
    mockModals[0].props.header = undefined;
    const { container } = render(<ModalContainer />);
    
   
    const modalHeader = container.querySelector('[data-testid="modal-header"]');
    expect(modalHeader).toBeInTheDocument();
   
    expect(modalHeader?.textContent).toBe('');
  });

  it('handles closest method returning null', async () => {
    render(<ModalContainer />);
    
    await waitFor(() => {
      const dialogContent = document.querySelector('[data-testid="dialog-content"]');
      expect(dialogContent).toBeInTheDocument();
    });
    
    const mockPreventDefault = vi.fn();
    const mockTarget = {
      closest: vi.fn().mockReturnValue(null)
    };
    
    const event = {
      target: mockTarget,
      preventDefault: mockPreventDefault,
    };
    
    const dialogContent = document.querySelector('[data-testid="dialog-content"]') as any;
    const onPointerDownOutside = dialogContent.__onPointerDownOutside;
    if (onPointerDownOutside) {
      onPointerDownOutside(event);
    }
    
    expect(mockTarget.closest).toHaveBeenCalledWith('[data-radix-popper-content-wrapper]');
    expect(mockPreventDefault).not.toHaveBeenCalled();
  });
});
