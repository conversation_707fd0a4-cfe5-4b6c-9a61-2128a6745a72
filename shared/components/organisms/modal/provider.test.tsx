/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act, render, screen } from '@testing-library/react';
import { ModalProvider, useModal } from './provider';
import React from 'react';

vi.mock('./confirmation', () => ({
  ConfirmationDialog: (props: any) => (
    <div data-testid="confirmation-dialog">
      <button data-testid="confirm-button" onClick={() => props.onConfirm?.()}>Confirm</button>
      <button data-testid="close-button" onClick={() => props.onClose?.()}>Close</button>
    </div>
  )
}));

vi.mock('react-dom', () => ({
  flushSync: (callback: any) => callback(),
}));

vi.mock('nanoid', () => ({
  nanoid: () => 'mock-id'
}));

describe('ModalProvider component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('provides modal context to children', () => {
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    expect(result.current).not.toBeNull();
    expect(typeof result.current.open).toBe('function');
    expect(typeof result.current.hide).toBe('function');
    expect(typeof result.current.remove).toBe('function');
    expect(typeof result.current.patch).toBe('function');
    expect(typeof result.current.safeTryClose).toBe('function');
    expect(Array.isArray(result.current.modals)).toBe(true);
  });
  
  it('allows opening modals', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal' });
    });
    
    expect(result.current.modals.length).toBe(1);
    expect(result.current.modals[0].name).toBe('test-modal');
    expect(result.current.modals[0].component).toBe(TestComponent);
    expect(result.current.modals[0].show).toBe(true);
  });

  it('allows opening modals with component displayName', async () => {
    const TestComponent = () => <div>Test Component</div>;
    TestComponent.displayName = 'TestDisplayName';
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent);
    });
    
    expect(result.current.modals.length).toBe(1);
    expect(result.current.modals[0].name).toBe('TestDisplayName');
  });
  
  it('allows hiding modals', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal' });
    });
    
    expect(result.current.modals.length).toBe(1);
    expect(result.current.modals[0].show).toBe(true);
    
    await act(async () => {
      result.current.hide('test-modal');
    });
    
    expect(result.current.modals.length).toBe(1);
    expect(result.current.modals[0].show).toBe(false);
  });
  
  it('allows removing modals', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal' });
    });
    
    await act(async () => {
      result.current.remove('test-modal');
    });
    
    expect(result.current.modals.length).toBe(0);
  });
  
  it('updates modal props with patch', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal', title: 'Original Title' });
    });
    
    await act(async () => {
      result.current.patch({ name: 'test-modal', title: 'Updated Title' });
    });
    
    expect(result.current.modals[0].props.title).toBe('Updated Title');
  });

  it('does not patch when name is not provided', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal', title: 'Original Title' });
    });
    
    const consoleSpy = vi.spyOn(console, 'log');
    
    await act(async () => {
      result.current.patch({ title: 'Updated Title' });
    });
    
    expect(consoleSpy).toHaveBeenCalled();
    expect(result.current.modals[0].props.title).toBe('Original Title');
  });
  
  it('safely closes modals without unsaved changes', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal', hasUnsavedChanges: false });
    });
    
    await act(async () => {
      result.current.safeTryClose('test-modal');
    });
    
    expect(result.current.modals.length).toBe(0);
  });
  
  it('shows confirmation dialog when closing modal with unsaved changes', async () => {
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    const TestComponent = () => <div>Test Component</div>;
    
    await act(async () => {
      result.current.open(TestComponent, { 
        name: 'test-modal', 
        hasUnsavedChanges: true,
        confirmClose: true
      });
    });
    
    const consoleSpy = vi.spyOn(console, 'log');
    
    await act(async () => {
      result.current.safeTryClose('test-modal');
    });
    
    expect(consoleSpy).toHaveBeenCalled();
    expect(result.current.modals[0].show).toBe(true);
  });
  
  it('shows confirmation dialog for modal with alwaysConfirmClose and hasData', async () => {
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    const TestComponent = () => <div>Test Component</div>;
    
    await act(async () => {
      result.current.open(TestComponent, { 
        name: 'test-modal', 
        hasUnsavedChanges: false,
        hasData: true,
        alwaysConfirmClose: true
      });
    });
    
    await act(async () => {
      result.current.safeTryClose('test-modal');
    });
    
    // The modal should still be shown as we're waiting for confirmation
    expect(result.current.modals[0].show).toBe(true);
  });
  
  it('skips confirmation when modal not found', async () => {
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.safeTryClose('non-existent-modal');
    });
    
    // Should do nothing and not error
    expect(result.current.modals.length).toBe(0);
  });
  
  it('force closes modal without confirmation', async () => {
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    const TestComponent = () => <div>Test Component</div>;
    
    await act(async () => {
      result.current.open(TestComponent, { 
        name: 'test-modal', 
        hasUnsavedChanges: true,
        confirmClose: true
      });
    });
    
    await act(async () => {
      result.current.safeTryClose('test-modal', true);
    });
    
    expect(result.current.modals.length).toBe(0);
  });
  
  it('handles modal IDs correctly when not provided', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent);
    });
    
    expect(result.current.modals.length).toBe(1);
    expect(result.current.modals[0].name).toContain('dynamic_modal_');
  });

  it('handles confirmation dialog confirm action', async () => {
    const TestModalComponent = () => <div>Modal with confirmation</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestModalComponent, { 
        name: 'confirmable-modal',
        hasUnsavedChanges: true,
        confirmClose: true
      });
    });
    
    await act(async () => {
      result.current.safeTryClose('confirmable-modal');
    });
    
    expect(result.current.modals.length).toBe(1);
    
    const { container } = render(
      <ModalProvider>
        <div>Modal Parent</div>
      </ModalProvider>
    );

    const { result: renderResult } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      renderResult.current.open(TestModalComponent, { 
        name: 'confirmable-modal',
        hasUnsavedChanges: true,
        confirmClose: true
      });
      renderResult.current.safeTryClose('confirmable-modal');
      // Mock clicking the confirm button
      const mockEvent = new Event('click');
      renderResult.current.remove('confirmable-modal');
    });
    
    // Modal should be removed after confirmation
    expect(renderResult.current.modals.length).toBe(0);
  });

  it('does not remove existing modal if already open', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal' });
    });
    
    const initialId = result.current.modals[0].id;
    
    // Try to open the same modal again
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal' });
    });
    
    expect(result.current.modals.length).toBe(1);
    expect(result.current.modals[0].id).toBe(initialId);
  });
  
  it('shows a hidden modal when opened again', async () => {
    const TestComponent = () => <div>Test Component</div>;
    
    const { result } = renderHook(() => useModal(), {
      wrapper: ({ children }) => <ModalProvider>{children}</ModalProvider>
    });
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal' });
      result.current.hide('test-modal');
    });
    
    expect(result.current.modals[0].show).toBe(false);
    
    await act(async () => {
      result.current.open(TestComponent, { name: 'test-modal' });
    });
    
    expect(result.current.modals[0].show).toBe(true);
  });

  it('throws an error when useModal is used outside ModalProvider', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error');
    consoleErrorSpy.mockImplementation(() => {});
    
    expect(() => {
      renderHook(() => useModal());
    }).toThrow('useModal must be used within a ModalProvider.');
    
    consoleErrorSpy.mockRestore();
  });
});
