/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import { SortHeader } from './SortHeader';

vi.mock('../../atoms/Iconify', () => ({
  default: vi.fn(({ name, className }) => (
    <div data-testid="mock-iconify" data-icon-name={name} className={className} />
  ))
}));

vi.mock('../../ui/button', () => ({
  Button: vi.fn(({ children, className, variant, onClick }) => (
    <button 
      data-testid="mock-button" 
      className={className}
      data-variant={variant}
      onClick={onClick}
    >
      {children}
    </button>
  ))
}));

describe('SortHeader component', () => {
  const createMockColumn = (isSorted: false | 'asc' | 'desc') => ({
    getIsSorted: vi.fn().mockReturnValue(isSorted),
    toggleSorting: vi.fn()
  });

  it('renders with title and no sort indicator when not sorted', () => {
    const mockColumn = createMockColumn(false);
    const { container } = render(
      <SortHeader column={mockColumn as any} title="Column Title" />
    );
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.getAttribute('data-variant')).toBe('ghost');
    
    expect(button?.textContent).toContain('Column Title');
    
    const iconElement = container.querySelector('[data-testid="mock-iconify"]');
    expect(iconElement).not.toBeNull();
    expect(iconElement?.getAttribute('data-icon-name')).toBe('untitled:chevron-selector-vertical');
    expect(iconElement?.className).toContain('text-gray-500');
  });
  
  it('renders with ascending sort indicator when sorted ascending', () => {
    const mockColumn = createMockColumn('asc');
    const { container } = render(
      <SortHeader column={mockColumn as any} title="Column Title" />
    );
    
    const iconElement = container.querySelector('[data-testid="mock-iconify"]');
    expect(iconElement).not.toBeNull();
    expect(iconElement?.getAttribute('data-icon-name')).toBe('untitled:arrow-up');
    expect(iconElement?.className).toContain('text-primary');
  });
  
  it('renders with descending sort indicator when sorted descending', () => {
    const mockColumn = createMockColumn('desc');
    const { container } = render(
      <SortHeader column={mockColumn as any} title="Column Title" />
    );
    
    // Check for the down arrow icon when sorted descending
    const iconElement = container.querySelector('[data-testid="mock-iconify"]');
    expect(iconElement).not.toBeNull();
    expect(iconElement?.getAttribute('data-icon-name')).toBe('untitled:arrow-down');
    expect(iconElement?.className).toContain('text-primary');
  });
  
  it('calls toggleSorting when clicked', () => {
    const mockColumn = createMockColumn(false);
    const { container } = render(
      <SortHeader column={mockColumn as any} title="Column Title" />
    );
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    
    if (button) {
      fireEvent.click(button);
      expect(mockColumn.toggleSorting).toHaveBeenCalledTimes(1);
      expect(mockColumn.toggleSorting).toHaveBeenCalledWith(false);
    }
  });
  
  it('calls toggleSorting with true when sorted ascending', () => {
    const mockColumn = createMockColumn('asc');
    const { container } = render(
      <SortHeader column={mockColumn as any} title="Column Title" />
    );
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    
    if (button) {
      fireEvent.click(button);
      expect(mockColumn.toggleSorting).toHaveBeenCalledTimes(1);
      expect(mockColumn.toggleSorting).toHaveBeenCalledWith(true);
    }
  });
  
  it('applies correct styling to the button', () => {
    const mockColumn = createMockColumn(false);
    const { container } = render(
      <SortHeader column={mockColumn as any} title="Column Title" />
    );
    
    const button = container.querySelector('[data-testid="mock-button"]');
    expect(button).not.toBeNull();
    expect(button?.className).toContain('p-0');
    expect(button?.className).toContain('font-medium');
    expect(button?.className).toContain('text-left');
  });
});
