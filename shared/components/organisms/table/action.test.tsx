/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, cleanup } from '@testing-library/react';
import Action from './action';
import React from 'react';

// Mock dependencies
vi.mock('../../ui/dropdown-menu', () => ({
  DropdownMenu: ({ children, open, onOpenChange }: any) => (
    <div data-testid="dropdown-menu" data-open={open}>{children}</div>
  ),
  DropdownMenuTrigger: ({ children, className }: any) => (
    <button data-testid="dropdown-trigger" className={className}>{children}</button>
  ),
  DropdownMenuContent: ({ children, align, className }: any) => (
    <div data-testid="dropdown-content" data-align={align} className={className}>{children}</div>
  ),
}));

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Action component', () => {
  it('renders dropdown components', () => {
    const { container } = render(
      <Action action={<button data-testid="action-button">Action</button>}>
        <div data-testid="dropdown-children">Content</div>
      </Action>
    );
    
    const menu = container.querySelector('[data-testid="dropdown-menu"]');
    const trigger = container.querySelector('[data-testid="dropdown-trigger"]');
    const content = container.querySelector('[data-testid="dropdown-content"]');
    const action = container.querySelector('[data-testid="action-button"]');
    const children = container.querySelector('[data-testid="dropdown-children"]');
    
    expect(menu).not.toBeNull();
    expect(trigger).not.toBeNull();
    expect(content).not.toBeNull();
    expect(action).not.toBeNull();
    expect(children).not.toBeNull();
  });

  it('passes open and onOpenChange props to DropdownMenu', () => {
    const handleOpenChange = vi.fn();
    
    const { container } = render(
      <Action 
        action={<button>Action</button>} 
        open={true}
        onOpenChange={handleOpenChange}
      >
        Content
      </Action>
    );
    
    const menu = container.querySelector('[data-testid="dropdown-menu"]');
    expect(menu).not.toBeNull();
    expect(menu?.getAttribute('data-open')).toBe('true');
    
    // Check content alignment and styling
    const content = container.querySelector('[data-testid="dropdown-content"]');
    expect(content?.getAttribute('data-align')).toBe('end');
    expect(content?.className).toContain('w-full');
    expect(content?.className).toContain('max-w-[220px]');
  });
});
