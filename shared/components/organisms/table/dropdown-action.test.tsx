/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, cleanup, screen } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';
import Action from './action';

expect.extend(matchers);

vi.mock('../../ui/dropdown-menu', () => ({
  DropdownMenu: ({ children, open, onOpenChange }: any) => (
    <div data-testid="dropdown-menu" data-open={open} data-on-open-change={onOpenChange ? "true" : undefined}>
      {children}
    </div>
  ),
  DropdownMenuTrigger: ({ children, className }: any) => (
    <button data-testid="dropdown-trigger" className={className}>
      {children}
    </button>
  ),
  DropdownMenuContent: ({ children, align, className }: any) => (
    <div data-testid="dropdown-content" data-align={align} className={className}>
      {children}
    </div>
  ),
}));

describe('Action dropdown component', () => {
  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });
  
  it('renders the dropdown menu with trigger and content', () => {
    const actionButton = <button>Action</button>;
    const dropdownContent = <div>Dropdown Content</div>;
    
    const { container } = render(
      <Action action={actionButton}>
        {dropdownContent}
      </Action>
    );
    
    // Check that all components are rendered
    const dropdownMenu = container.querySelector('[data-testid="dropdown-menu"]');
    const dropdownTrigger = container.querySelector('[data-testid="dropdown-trigger"]');
    const contentElement = container.querySelector('[data-testid="dropdown-content"]');
    
    expect(dropdownMenu).not.toBeNull();
    expect(dropdownTrigger).not.toBeNull();
    expect(contentElement).not.toBeNull();
    
    // Check that content is rendered correctly
    expect(container.textContent).toContain('Action');
    expect(container.textContent).toContain('Dropdown Content');
  });
  
  it('passes open state to dropdown menu', () => {
    const actionButton = <button>Action</button>;
    
    const { container } = render(
      <Action action={actionButton} open={true}>
        <div>Content</div>
      </Action>
    );
    
    // Check that open state is passed to dropdown menu
    const dropdownMenu = container.querySelector('[data-testid="dropdown-menu"]');
    expect(dropdownMenu).toHaveAttribute('data-open', 'true');
  });
  
  it('passes onOpenChange callback to dropdown menu', () => {
    const actionButton = <button>Action</button>;
    const handleOpenChange = vi.fn();
    
    const { container } = render(
      <Action action={actionButton} onOpenChange={handleOpenChange}>
        <div>Content</div>
      </Action>
    );
    
    // The onOpenChange handler should be passed to the dropdown menu
    const dropdownMenu = container.querySelector('[data-testid="dropdown-menu"]');
    expect(dropdownMenu).toHaveAttribute('data-on-open-change');
  });
});
