/**
 * @vitest-environment jsdom
 */
import * as matchers from '@testing-library/jest-dom/matchers';
import { cleanup, fireEvent, render } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

expect.extend(matchers);

const createDefaultTableMock = (): any => ({
  getState: () => ({
    pagination: { pageIndex: 0, pageSize: 10 },
    sorting: [],
    columnFilters: [],
    columnVisibility: {},
    rowSelection: {},
  }),
  getRowModel: () => ({
    rows: [
      {
        id: '1',
        original: { id: 1, name: 'Test 1' },
        getIsSelected: () => false,
        getVisibleCells: () => [
          {
            id: 'cell1-1',
            column: {
              id: 'id',
              columnDef: {
                cell: () => 'Cell 1-1',
                header: 'ID',
                accessorKey: 'id',
                meta: {}
              }
            },
            getContext: () => ({})
          },
          {
            id: 'cell1-2',
            column: {
              id: 'name',
              columnDef: {
                cell: () => 'Cell 1-2',
                header: 'Name',
                accessorKey: 'name',
                meta: {}
              }
            },
            getContext: () => ({})
          }
        ]
      },
      {
        id: '2',
        original: { id: 2, name: 'Test 2' },
        getIsSelected: () => false,
        getVisibleCells: () => [
          {
            id: 'cell2-1',
            column: {
              id: 'id',
              columnDef: {
                cell: () => 'Cell 2-1',
                header: 'ID',
                accessorKey: 'id',
                meta: {}
              }
            },
            getContext: () => ({})
          },
          {
            id: 'cell2-2',
            column: {
              id: 'name',
              columnDef: {
                cell: () => 'Cell 2-2',
                header: 'Name',
                accessorKey: 'name',
                meta: {}
              }
            },
            getContext: () => ({})
          }
        ]
      },
    ],
  }),
  getHeaderGroups: () => ([
    {
      headers: [
        {
          id: 'id',
          column: {
            id: 'id',
            columnDef: {
              header: 'ID',
              accessorKey: 'id',
              cell: () => 'Cell ID',
              size: 100,
              meta: {}
            }
          },
          getContext: () => ({ header: { id: 'id', column: { columnDef: { header: 'ID' } } } })
        },
        {
          id: 'name',
          column: {
            id: 'name',
            columnDef: {
              header: 'Name',
              accessorKey: 'name',
              meta: {}
            }
          },
          getContext: () => ({ header: { id: 'name', column: { columnDef: { header: 'Name' } } } })
        },
      ],
    },
  ]),
  getCanPreviousPage: () => false,
  getCanNextPage: () => true,
  previousPage: vi.fn(),
  nextPage: vi.fn(),
  getPageCount: () => 3,
  setPageIndex: vi.fn(),
  getPrePaginationRowModel: () => ({ rows: Array(20).fill(null).map((_, i) => ({ id: `${i}` })) }),
  setPageSize: vi.fn(),
});

vi.mock('@tanstack/react-table', () => {
  const React = require('react');
  return {
    flexRender: (_component: any, props: any) => React.createElement('div', props, 'Mocked Cell Content'),
    getCoreRowModel: () => () => ({
      rows: [], flatRows: [], headerGroups: [],
      getRowModel: () => ({ rows: [] }),
      getHeaderGroups: () => [],
    }),
    getFilteredRowModel: () => () => ({}),
    getPaginationRowModel: () => () => ({}),
    getSortedRowModel: () => () => ({}),
    useReactTable: vi.fn(),
  };
});

import * as ReactTable from '@tanstack/react-table';
import { DataTable } from './index';

// Create the default mock instance
const defaultTableMock = createDefaultTableMock();

// Alias the mocked useReactTable for easier usage
const mockedUseReactTable = (ReactTable.useReactTable as unknown as ReturnType<typeof vi.fn>);

// Set default return value after import
mockedUseReactTable.mockReturnValue(defaultTableMock);

vi.mock('../../ui/table', () => ({
  Table: ({ children, ...props }: any) => <div data-testid="table" {...props}>{children}</div>,
  TableBody: ({ children, ...props }: any) => <div data-testid="table-body" {...props}>{children}</div>,
  TableCell: ({ children, ...props }: any) => <div data-testid="table-cell" {...props}>{children}</div>,
  TableHead: ({ children, ...props }: any) => <div data-testid="table-head" {...props}>{children}</div>,
  TableHeader: ({ children, ...props }: any) => <div data-testid="table-header" {...props}>{children}</div>,
  TableRow: ({ children, ...props }: any) => <div data-testid="table-row" {...props}>{children}</div>,
}));

vi.mock('../../ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button data-testid="button" onClick={onClick} disabled={disabled} {...props}>{children}</button>
  ),
}));

vi.mock('lucide-react', () => ({
  ChevronLeft: () => <span data-testid="chevron-left">←</span>,
  ChevronRight: () => <span data-testid="chevron-right">→</span>,
}));

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

vi.mock('../../molecules/table-transition', () => {
  return {
    AnimatedTransition: ({ children, loading, skeleton, ...props }: any) => (
      <div data-testid="animated-transition" {...props}>
        {loading ? skeleton : children}
      </div>
    )
  };
});

vi.mock('./table-skeleton', () => ({
  TableSkeleton: ({ columns, rows }: { columns: number, rows: number, roundless?: boolean }) => (
    <div data-testid="table-skeleton">Loading... {columns} columns, {rows} rows</div>
  ),
}));

describe('Enhanced DataTable Tests', () => {
  beforeEach(() => {
    // Clean up before each test to ensure isolation
    cleanup();
    // Reset all mocks
    vi.clearAllMocks();
    // Reset to default mock
    mockedUseReactTable.mockClear();
    mockedUseReactTable.mockReturnValue(createDefaultTableMock());
  });

  const columns = [
    { accessorKey: 'id', header: 'ID' },
    { accessorKey: 'name', header: 'Name' },
  ];

  const data = [
    { id: 1, name: 'Test 1' },
    { id: 2, name: 'Test 2' },
  ];

  it('renders custom empty state when provided and no data', () => {
    mockedUseReactTable.mockReturnValueOnce({
      ...createDefaultTableMock(),
      getRowModel: () => ({ rows: [] }),
    });

    const customEmpty = <div data-testid="custom-empty">No data available</div>;
    const { getByTestId } = render(<DataTable columns={columns} data={[]} empty={customEmpty} />);

    const emptyElement = getByTestId('custom-empty');
    expect(emptyElement).toBeInTheDocument();
    expect(emptyElement).toHaveTextContent('No data available');
  });

  // Test disabling pagination
  it('does not render pagination when isPaginated is false', () => {
    const { queryByText } = render(<DataTable columns={columns} data={data} isPaginated={false} />);

    expect(queryByText('table.pagination.previous')).not.toBeInTheDocument();
    expect(queryByText('table.pagination.next')).not.toBeInTheDocument();
  });

  // Test sorting behavior with function updater
  it('handles sorting state with function updater', () => {
    const onSortingChange = vi.fn();
    render(<DataTable columns={columns} data={data} onSortingChange={onSortingChange} />);

    // Get the onSortingChange handler from the mock call
    const onSortingChangeHandler = mockedUseReactTable.mock.calls[0][0].onSortingChange;

    // Call the handler with a function updater
    const updaterFn = () => [{ id: 'name', desc: true }];
    onSortingChangeHandler(updaterFn);

    // This should call our onSortingChange prop with the updater function
    expect(onSortingChange).toHaveBeenCalledWith(updaterFn);
  });

  // Test sorting behavior with direct value
  it('handles sorting state with direct value', () => {
    const onSortingChange = vi.fn();
    render(<DataTable columns={columns} data={data} onSortingChange={onSortingChange} />);

    // Get the onSortingChange handler from the mock call
    const onSortingChangeHandler = mockedUseReactTable.mock.calls[0][0].onSortingChange;

    // Call the handler with a direct value
    const directValue = [{ id: 'name', desc: true }];
    onSortingChangeHandler(directValue);

    // This should call our onSortingChange prop with the direct value
    expect(onSortingChange).toHaveBeenCalledWith(directValue);
  });

  // Test the useEffect for navigating to the last valid page when current page is empty
  it('handles page change when current page is invalid and data is loading', () => {

    // Set up mock with high page index
    mockedUseReactTable.mockReturnValueOnce({
      ...createDefaultTableMock(),
      getState: () => ({
        ...createDefaultTableMock().getState(),
        pagination: {
          pageIndex: 3, // 4th page
          pageSize: 10
        }
      }),
      getPageCount: () => 5,
      getRowModel: () => ({ rows: [] }), // Empty rows
    });

    // Render with loading=true
    const onPageChange = vi.fn();
    render(
      <DataTable
        columns={columns}
        data={[]}
        onPageChange={onPageChange}
        isLoading={true}
      />
    );

    expect(onPageChange).not.toHaveBeenCalled();
  });

  it('handles sorting state with function updater', () => {
    const onSortingChange = vi.fn();
    render(<DataTable columns={columns} data={data} onSortingChange={onSortingChange} />);

    // Extract the useReactTable mock to access the onSortingChange handler
    const onSortingChangeHandler = mockedUseReactTable.mock.calls[0][0].onSortingChange;

    // Call the handler with a function updater
    const updaterFn = () => [{ id: 'name', desc: true }];
    onSortingChangeHandler(updaterFn);

    // This should call our onSortingChange prop with the updater function
    expect(onSortingChange).toHaveBeenCalled();
    expect(onSortingChange.mock.calls[0][0]).toBe(updaterFn);
  });

  // Test sorting behavior with direct value
  it('handles sorting state with direct value', () => {
    const onSortingChange = vi.fn();
    render(<DataTable columns={columns} data={data} onSortingChange={onSortingChange} />);

    // Extract the useReactTable mock to access the onSortingChange handler
    const onSortingChangeHandler = mockedUseReactTable.mock.calls[0][0].onSortingChange;

    // Call the handler with a direct value
    const directValue = [{ id: 'name', desc: true }];
    onSortingChangeHandler(directValue);

    // This should call our onSortingChange prop with the direct value
    expect(onSortingChange).toHaveBeenCalled();
    expect(onSortingChange.mock.calls[0][0]).toBe(directValue);
  });

  // Test the useEffect for navigating to the last valid page when current page is empty
  it('handles page change when current page is invalid and data is loading', () => {
    const onPageChange = vi.fn();

    // Set up tableOptions with a non-zero page index
    const tableOptions = {
      state: {
        pagination: {
          pageIndex: 3, // 4th page
          pageSize: 10
        }
      },
      pageCount: 5 // 5 total pages
    };

    // Render with loading=true
    render(
      <DataTable
        columns={columns}
        data={[]} // Empty data
        onPageChange={onPageChange}
        tableOptions={tableOptions}
        isLoading={true} // Loading state
      />
    );

    // The useEffect should not trigger when loading is true
    expect(onPageChange).not.toHaveBeenCalled();
  });

  // Test explicitly with table meta
  it('passes meta property to the table', () => {
    const meta = { customSetting: true };
    render(<DataTable columns={columns} data={data} meta={meta} />);

    // Check that the meta was passed to useReactTable
    expect(mockedUseReactTable.mock.calls[0][0].meta).toBe(meta);
  });

  describe('Enhanced DataTablePagination Tests', () => {
    // Test pagination with few pages (<=7)
    it('renders correct page numbers when total pages <= 7', () => {
      const { container } = render(
        <DataTable
          columns={columns}
          data={data}
          isPaginated
        />
      );

      // This would render all 3 page buttons (1-3) with no ellipsis
      const buttons = Array.from(container.querySelectorAll('[data-testid="button"]'))
        .filter(btn => btn.textContent && /^\d+$/.test(btn.textContent));

      // Should have page buttons for pages 1-3 (from defaultTableMock.getPageCount() = 3)
      expect(buttons.length).toBe(3);
    });

    // Test pagination with many pages and current page in the middle
    it('renders page numbers with ellipsis when total pages > 7 and current page in middle', () => {
      // Override the table mock for this test
      mockedUseReactTable.mockReturnValueOnce({
        ...createDefaultTableMock(),
        getPageCount: () => 20, // 20 pages total
        getState: () => ({ pagination: { pageIndex: 10 } }), // Current page is 11 (0-indexed)
        getCanPreviousPage: () => true,
        getCanNextPage: () => true,
        setPageIndex: vi.fn()
      });

      const { container } = render(
        <DataTable
          columns={columns}
          data={data}
          isPaginated
        />
      );

      // Should show ellipsis in the pagination
      const paginationDiv = container.querySelector('.flex.items-center.space-x-1');
      if (paginationDiv?.textContent) {
        expect(paginationDiv.textContent).toContain('...');
      } else {
        // If pagination structure is different, just verify it renders
        expect(container.textContent).toContain('table.pagination');
      }
    });

    // Test pagination with many pages and current page near the beginning
    it('renders page numbers correctly when current page is near the beginning', () => {
      // Override the table mock for this test
      mockedUseReactTable.mockReturnValueOnce({
        ...createDefaultTableMock(),
        getPageCount: () => 20, // 20 pages total
        getState: () => ({ pagination: { pageIndex: 2 } }), // Current page is 3 (0-indexed)
        getCanPreviousPage: () => true,
        getCanNextPage: () => true,
        setPageIndex: vi.fn()
      });

      const { container } = render(
        <DataTable
          columns={columns}
          data={data}
          isPaginated
        />
      );

      // Should only show one ellipsis at the end
      const paginationContent = container.querySelector('.flex.items-center.space-x-1')?.textContent;
      if (paginationContent) {
        expect(paginationContent).toContain('...');

        // Count number of ellipses - should be 1 for pages near beginning
        const ellipsisMatches = paginationContent.match(/\.\.\./g);
        expect(ellipsisMatches?.length).toBe(1);
      } else {
        // Fallback verification
        expect(container.textContent).toContain('table.pagination');
      }
    });

    // Test pagination with many pages and current page near the end
    it('renders page numbers correctly when current page is near the end', () => {
      // Override the table mock for this test
      mockedUseReactTable.mockReturnValueOnce({
        ...createDefaultTableMock(),
        getPageCount: () => 20, // 20 pages total
        getState: () => ({ pagination: { pageIndex: 17 } }), // Current page is 18 (0-indexed)
        getCanPreviousPage: () => true,
        getCanNextPage: () => true,
        setPageIndex: vi.fn()
      });

      const { container } = render(
        <DataTable
          columns={columns}
          data={data}
          isPaginated
        />
      );

      // Should only show one ellipsis at the beginning
      const paginationContent = container.querySelector('.flex.items-center.space-x-1')?.textContent;
      if (paginationContent) {
        expect(paginationContent).toContain('...');

        // Count number of ellipses - should be 1 for pages near end
        const ellipsisMatches = paginationContent.match(/\.\.\./g);
        expect(ellipsisMatches?.length).toBe(1);
      } else {
        // Fallback verification
        expect(container.textContent).toContain('table.pagination');
      }
    });

    // Test handling page change with custom callback
    it('calls onPageChange callback with correct page number when page changes', () => {
      const onPageChange = vi.fn();

      const { container } = render(
        <DataTable
          columns={columns}
          data={data}
          isPaginated
          onPageChange={onPageChange}
        />
      );

      // Find all page number buttons
      const pageButtons = Array.from(container.querySelectorAll('[data-testid="button"]'))
        .filter(btn => btn.textContent && /^\d+$/.test(btn.textContent));

      // Click on page 2 button if available
      if (pageButtons.length > 1) {
        fireEvent.click(pageButtons[1]);

        // Should call onPageChange with page 2 (page index + 1)
        expect(onPageChange).toHaveBeenCalledWith(2);
      } else {
        // If no page 2 button, click next button
        const nextButton = Array.from(container.querySelectorAll('[data-testid="button"]'))
          .find(btn => btn.textContent?.includes('table.pagination.next'));

        if (nextButton) {
          fireEvent.click(nextButton);
          expect(onPageChange).toHaveBeenCalledWith(2);
        }
      }
    });
    // Test when isLoading is true
    it('does not render pagination when isLoading is true', () => {
      const { queryByText } = render(
        <DataTable
          columns={columns}
          data={data}
          isPaginated
          isLoading={true}
        />
      );

      // Check that pagination text is not in the document
      expect(queryByText('table.pagination.previous')).toBeNull();
      expect(queryByText('table.pagination.next')).toBeNull();
    });

    // Test pagination buttons disabled states
    it('disables previous/next buttons appropriately', () => {
      // Override the table mock to test button disabled states
      const tableMock = mockedUseReactTable;
      tableMock.mockReturnValueOnce({
        ...createDefaultTableMock(),
        getPageCount: () => 3,
        getState: () => ({ pagination: { pageIndex: 0, pageSize: 10 } }), // First page
        getCanPreviousPage: () => false, // Can't go back
        getCanNextPage: () => true,   // Can go forward
        setPageIndex: vi.fn()
      });

      const { container } = render(
        <DataTable
          columns={columns}
          data={data}
          isPaginated
        />
      );

      // Find prev and next buttons
      const prevButton = Array.from(container.querySelectorAll('[data-testid="button"]'))
        .find(btn => btn.textContent?.includes('table.pagination.previous'));
      const nextButton = Array.from(container.querySelectorAll('[data-testid="button"]'))
        .find(btn => btn.textContent?.includes('table.pagination.next'));

      // Previous should be disabled, next should be enabled
      expect(prevButton).toHaveAttribute('disabled');
      expect(nextButton).not.toHaveAttribute('disabled');
    });

    // Test the current page button is disabled
    it('disables the current page button', () => {
      // Override the table mock to test current page button disabled
      const tableMock = mockedUseReactTable;
      tableMock.mockReturnValueOnce({
        ...createDefaultTableMock(),
        getPageCount: () => 5,
        getState: () => ({ pagination: { pageIndex: 2, pageSize: 10 } }), // Third page (0-indexed)
        getCanPreviousPage: () => true,
        getCanNextPage: () => true,
        setPageIndex: vi.fn()
      });

      const { container } = render(
        <DataTable
          columns={columns}
          data={data}
          isPaginated
        />
      );

      // All buttons with numbers
      const pageButtons = Array.from(container.querySelectorAll('[data-testid="button"]'))
        .filter(btn => btn.textContent && /^\d+$/.test(btn.textContent));

      // Find button with "3" (third page)
      const currentPageButton = pageButtons.find(btn => btn.textContent === '3');

      // Current page button should be disabled
      expect(currentPageButton).toHaveAttribute('disabled');
    });
  });
});
