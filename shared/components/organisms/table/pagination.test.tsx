/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';
import { DataTablePagination } from './index';
import React from 'react';

expect.extend(matchers);

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

vi.mock('../../ui/button', () => {
  const React = require('react');
  type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & { children?: React.ReactNode };
  return {
    Button: React.forwardRef((props: ButtonProps, ref: React.Ref<HTMLButtonElement>) => (
      <button 
        ref={ref}
        data-testid="button" 
        {...props}
      >
        {props.children}
      </button>
    )),
  };
});

vi.mock('lucide-react', () => ({
  ChevronLeft: () => <span data-testid="chevron-left">←</span>,
  ChevronRight: () => <span data-testid="chevron-right">→</span>,
}));

describe('DataTablePagination', () => {
  // Test that pagination is not rendered when there is only one page
  it('returns null when there is only one page', () => {
    const mockTable = {
      getState: vi.fn().mockReturnValue({ 
        pagination: { pageIndex: 0, pageSize: 10 } 
      }),
      getPageCount: vi.fn().mockReturnValue(1), // Only one page
      getCanPreviousPage: vi.fn().mockReturnValue(false),
      getCanNextPage: vi.fn().mockReturnValue(false),
      setPageIndex: vi.fn(),
    };
    
    const { container } = render(
      // @ts-ignore
      <DataTablePagination table={mockTable} />
    );
    
    // The component should return null
    expect(container.firstChild).toBeNull();
  });

  // Test that pagination is not rendered when isLoading is true
  it('returns null when isLoading is true', () => {
    const mockTable = {
      getState: vi.fn().mockReturnValue({ 
        pagination: { pageIndex: 0, pageSize: 10 } 
      }),
      getPageCount: vi.fn().mockReturnValue(5),
      getCanPreviousPage: vi.fn().mockReturnValue(true),
      getCanNextPage: vi.fn().mockReturnValue(true),
      setPageIndex: vi.fn(),
    };
    
    const { container } = render(
      // @ts-ignore
      <DataTablePagination table={mockTable} isLoading={true} />
    );
    
    // The component should return null
    expect(container.firstChild).toBeNull();
  });

  // Test that the current page button is disabled
  it('disables the current page button', () => {
    const mockTable = {
      getState: vi.fn().mockReturnValue({ 
        pagination: { pageIndex: 1, pageSize: 10 } 
      }),
      getPageCount: vi.fn().mockReturnValue(3),
      getCanPreviousPage: vi.fn().mockReturnValue(true),
      getCanNextPage: vi.fn().mockReturnValue(true),
      setPageIndex: vi.fn(),
    };
    
    // @ts-ignore
    const { container } = render(<DataTablePagination table={mockTable} />);
    
    // Component should render pagination
    expect(container.firstChild).not.toBeNull();
    
    // Use more flexible button selector
    const buttons = container.querySelectorAll('button');
    expect(buttons.length).toBeGreaterThan(0);
    
    const pageButtons = Array.from(buttons).filter(btn => /^\d+$/.test(btn.textContent || ''));
    
    // Find the button with "2" as text (index 1 + 1)
    const page2Button = pageButtons.find(btn => btn.textContent === '2');
    
    // Page 2 button should be disabled (current page)
    expect(page2Button).toHaveAttribute('disabled');
  });

  // Test getVisiblePageNumbers function for few pages
  it('shows all page numbers when total pages <= 7', () => {
    const mockTable = {
      getState: vi.fn().mockReturnValue({ 
        pagination: { pageIndex: 1, pageSize: 10 } 
      }),
      getPageCount: vi.fn().mockReturnValue(5),
      getCanPreviousPage: vi.fn().mockReturnValue(true),
      getCanNextPage: vi.fn().mockReturnValue(true),
      setPageIndex: vi.fn(),
    };
    
    // @ts-ignore
    const { container } = render(<DataTablePagination table={mockTable} />);
    
    // Component should render pagination
    expect(container.firstChild).not.toBeNull();
    
    // Use more flexible button selector
    const allButtons = container.querySelectorAll('button');
    expect(allButtons.length).toBeGreaterThan(0);
    
    const pageButtons = Array.from(allButtons).filter(btn => /^\d+$/.test(btn.textContent || ''));
    
    expect(pageButtons.length).toBe(5);
    
    const pageNumbers = new Set(pageButtons.map(btn => btn.textContent));
    expect([...pageNumbers].sort()).toEqual(['1', '2', '3', '4', '5']);
  });

  it('shows ellipsis when total pages > 7', () => {
    const mockTable = {
      getState: vi.fn().mockReturnValue({ 
        pagination: { pageIndex: 10, pageSize: 10 } 
      }),
      getPageCount: vi.fn().mockReturnValue(20),
      getCanPreviousPage: vi.fn().mockReturnValue(true),
      getCanNextPage: vi.fn().mockReturnValue(true),
      setPageIndex: vi.fn(),
    };
    
    // @ts-ignore
    const { container } = render(<DataTablePagination table={mockTable} />);
    
    // Component should render pagination
    expect(container.firstChild).not.toBeNull();
    
    const ellipsis = container.querySelectorAll('.px-2');
    expect(ellipsis.length).toBeGreaterThan(0);
    expect(ellipsis[0].textContent).toBe('...');
    
    // Use more flexible button selector
    const pageButtons = Array.from(container.querySelectorAll('button'))
      .filter(btn => /^\d+$/.test(btn.textContent || ''));
    
    const page11Button = pageButtons.find(btn => btn.textContent === '11');
    expect(page11Button).toHaveAttribute('disabled');
  });

  it('calls onPageChange when a page button is clicked', () => {
    const onPageChange = vi.fn();
    const setPageIndex = vi.fn();
    
    const mockTable = {
      getState: vi.fn(() => ({ pagination: { pageIndex: 0, pageSize: 10 } })),
      getPageCount: vi.fn(() => 5),
      getCanPreviousPage: vi.fn(() => false),
      getCanNextPage: vi.fn(() => true),
      setPageIndex,
    } as any;

    const { container } = render(<DataTablePagination table={mockTable} onPageChange={onPageChange} />);
    
    // Component should render pagination
    expect(container.firstChild).not.toBeNull();
    
    // Use more flexible button selector
    const pageButtons = Array.from(container.querySelectorAll('button'));
    expect(pageButtons.length).toBeGreaterThan(0);
    
    const nextPageButton = pageButtons.find(btn => btn.textContent === '2');
    expect(nextPageButton).toBeDefined();
    
    fireEvent.click(nextPageButton!);
    expect(onPageChange).toHaveBeenCalledWith(2);
    expect(setPageIndex).toHaveBeenCalledWith(1);
  });
});