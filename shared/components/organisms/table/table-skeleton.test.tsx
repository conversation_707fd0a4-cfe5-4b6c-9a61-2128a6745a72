/**
 * @vitest-environment jsdom
 */
import { render } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { TableSkeleton } from './table-skeleton';

// Mock UI components
vi.mock('../../ui/table', () => ({
  Table: vi.fn(({ children }) => <table data-testid="mock-table">{children}</table>),
  TableBody: vi.fn(({ children }) => <tbody data-testid="mock-table-body">{children}</tbody>),
  TableCell: vi.fn(({ children }) => <td data-testid="mock-table-cell">{children}</td>),
  TableHead: vi.fn(({ children }) => <th data-testid="mock-table-head">{children}</th>),
  TableHeader: vi.fn(({ children }) => <thead data-testid="mock-table-header">{children}</thead>),
  TableRow: vi.fn(({ children }) => <tr data-testid="mock-table-row">{children}</tr>)
}));

describe('TableSkeleton component', () => {
  it('renders with default props', () => {
    const { container } = render(<TableSkeleton columns={5} rows={10} />);
    
    // Check if container has correct classes
    const mainDiv = container.firstChild as HTMLElement;
    expect(mainDiv).not.toBeNull();
    expect(mainDiv.className).toContain('flex');
    expect(mainDiv.className).toContain('flex-col');
    expect(mainDiv.className).toContain('w-full');
    expect(mainDiv.className).toContain('h-full');
    
    // Check if table border container has rounded corners by default
    const tableContainer = container.querySelector('.overflow-hidden');
    expect(tableContainer).not.toBeNull();
    expect(tableContainer?.className).toContain('rounded-xl');
    expect(tableContainer?.className).not.toContain('border-x-0');
    
    // Check table structure
    const table = container.querySelector('[data-testid="mock-table"]');
    expect(table).not.toBeNull();
    
    const tableHeader = container.querySelector('[data-testid="mock-table-header"]');
    expect(tableHeader).not.toBeNull();
    
    const tableBody = container.querySelector('[data-testid="mock-table-body"]');
    expect(tableBody).not.toBeNull();
    
    // Check if headers are rendered correctly
    const headerRow = tableHeader?.querySelector('[data-testid="mock-table-row"]');
    expect(headerRow).not.toBeNull();
    
    const headerCells = tableHeader?.querySelectorAll('[data-testid="mock-table-head"]');
    expect(headerCells?.length).toBe(5); // default columns
    
    // Check if rows are rendered correctly
    const rows = tableBody?.querySelectorAll('[data-testid="mock-table-row"]');
    expect(rows?.length).toBe(10); // default rows
    
    // Check if the first row has the correct number of cells
    const firstRowCells = rows?.[0]?.querySelectorAll('[data-testid="mock-table-cell"]');
    expect(firstRowCells?.length).toBe(5);
    
    // Check if pulse animation divs are rendered
    const pulseDivs = container.querySelectorAll('.animate-pulse');
    expect(pulseDivs.length).toBe(5 + (5 * 10)); // headers + (columns * rows)
  });
  
  it('renders with custom columns and rows', () => {
    const { container } = render(<TableSkeleton columns={3} rows={2} />);
    
    // Check header cells count
    const headerCells = container.querySelectorAll('[data-testid="mock-table-head"]');
    expect(headerCells?.length).toBe(3);
    
    // Check rows count
    const rows = container.querySelectorAll('[data-testid="mock-table-body"] [data-testid="mock-table-row"]');
    expect(rows?.length).toBe(2);
    
    // Check cells in first row
    const firstRowCells = rows[0]?.querySelectorAll('[data-testid="mock-table-cell"]');
    expect(firstRowCells?.length).toBe(3);
  });
  
  it('applies roundless style when specified', () => {
    const { container } = render(<TableSkeleton columns={5} rows={10} roundless={true} />);
    
    // Check if table container has the roundless classes
    const tableContainer = container.querySelector('.overflow-hidden');
    expect(tableContainer).not.toBeNull();
    expect(tableContainer?.className).toContain('border-x-0');
    expect(tableContainer?.className).toContain('border-b-0');
    expect(tableContainer?.className).not.toContain('rounded-xl');
  });
  
  it('applies custom className when provided', () => {
    const { container } = render(<TableSkeleton columns={5} rows={10} className="custom-class" />);
    
    // Check if custom class is applied
    const mainDiv = container.firstChild as HTMLElement;
    expect(mainDiv).not.toBeNull();
    expect(mainDiv.className).toBe('custom-class');
  });
  
  it('renders skeleton cells with varying widths', () => {
    const { container } = render(<TableSkeleton columns={1} rows={1} />);
    
    // Get the cell div
    const pulseDiv = container.querySelector('[data-testid="mock-table-cell"] .animate-pulse');
    expect(pulseDiv).not.toBeNull();
    
    // Check if it has a style attribute with width
    const style = pulseDiv?.getAttribute('style');
    expect(style).not.toBeNull();
    expect(style).toContain('width:');
    expect(style).toContain('%');
    expect(style).toContain('animation-delay:');
  });
});