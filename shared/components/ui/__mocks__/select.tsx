import React from 'react';

export const Select = ({ children, ...props }: any) => (
  <div data-testid="select-root" {...props}>
    {children}
  </div>
);

export const SelectGroup = ({ children, ...props }: any) => (
  <div data-testid="select-group" {...props}>
    {children}
  </div>
);

export const SelectValue = ({ children, placeholder, ...props }: any) => (
  <div data-testid="select-value" {...props}>
    {children || placeholder}
  </div>
);

export const SelectTrigger = ({ children, ...props }: any) => (
  <button data-testid="select-trigger" {...props}>
    {children}
  </button>
);

export const SelectContent = ({ children, ...props }: any) => (
  <div data-testid="select-content" {...props}>
    {children}
  </div>
);

export const SelectLabel = ({ children, ...props }: any) => (
  <div data-testid="select-label" {...props}>
    {children}
  </div>
);

export const SelectItem = ({ children, value, ...props }: any) => (
  <div data-testid={`select-item-${value}`} data-value={value} {...props}>
    {children}
  </div>
);
