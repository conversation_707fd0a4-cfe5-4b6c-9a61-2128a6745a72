import React from 'react';
import { vi } from 'vitest';

export const useSidebar = () => ({
  state: 'expanded',
  isMobile: false,
  setOpenMobile: vi.fn(),
  mobileOpen: false,
  isCollapsed: false,
  isOpen: true,
  setCollapsed: vi.fn(),
  setIsOpen: vi.fn()
});

export const SidebarMenu = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <div data-testid="sidebar-menu" {...props}>
    {children}
  </div>
);

export const SidebarMenuItem = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <div data-testid="sidebar-menu-item" {...props}>
    {children}
  </div>
);

export const SidebarGroup = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <div data-testid="sidebar-group" {...props}>
    {children}
  </div>
);

export const SidebarGroupLabel = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <div data-testid="sidebar-group-label" {...props}>
    {children}
  </div>
);

export const SidebarTrigger = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <button data-testid="sidebar-trigger" {...props}>
    {children}
  </button>
);

export const SidebarProvider = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="sidebar-provider">
    {children}
  </div>
);

export const SidebarMenuButton = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <button data-testid="sidebar-menu-button" {...props}>
    {children}
  </button>
);

export const SidebarMenuSub = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <div data-testid="sidebar-menu-sub" {...props}>
    {children}
  </div>
);

export const SidebarMenuSubButton = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <button data-testid="sidebar-menu-sub-button" {...props}>
    {children}
  </button>
);

export const SidebarMenuSubItem = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <div data-testid="sidebar-menu-sub-item" {...props}>
    {children}
  </div>
);

export const SidebarContent = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <div data-testid="sidebar-content" {...props}>
    {children}
  </div>
);

export const SidebarHeader = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
  <div data-testid="sidebar-header" {...props}>
    {children}
  </div>
);
