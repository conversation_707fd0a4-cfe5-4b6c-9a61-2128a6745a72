/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './accordion';
import React from 'react';

// Define mock for Radix UI Accordion
vi.mock('@radix-ui/react-accordion', () => {
  return {
    Root: ({ children, className, ...props }: any) => (
      <div data-orientation="vertical" className={className} {...props}>
        {children}
      </div>
    ),
    Item: ({ children, className, ...props }: any) => (
      <div data-orientation="vertical" data-state="closed" className={className} {...props}>
        {children}
      </div>
    ),
    Header: ({ children, className }: any) => (
      <h3 data-orientation="vertical" data-state="closed" className={className}>
        {children}
      </h3>
    ),
    Trigger: ({ children, className, ...props }: any) => (
      <button
        data-orientation="vertical"
        data-state="closed"
        data-radix-collection-item=""
        aria-expanded="false"
        className={className}
        type="button"
        id="radix-r0"
        aria-controls="radix-r1"
        {...props}
      >
        {children}
      </button>
    ),
    Content: ({ children, className, ...props }: any) => (
      <div
        data-orientation="vertical"
        data-state="closed"
        style={{
          '--radix-accordion-content-height': 'var(--radix-collapsible-content-height)',
          '--radix-accordion-content-width': 'var(--radix-collapsible-content-width)',
        }}
        className={className}
        id="radix-r1"
        role="region"
        aria-labelledby="radix-r0"
        hidden
        {...props}
      >
        {children}
      </div>
    ),
  };
});

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Accordion component', () => {
  it('renders accordion with items', () => {
    const { container } = render(
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger>Section 1</AccordionTrigger>
          <AccordionContent>Content for section 1</AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger>Section 2</AccordionTrigger>
          <AccordionContent>Content for section 2</AccordionContent>
        </AccordionItem>
      </Accordion>
    );

    // Check that the accordion items are rendered
    const buttons = container.querySelectorAll('button');
    expect(buttons.length).toBe(2);
    expect(buttons[0]?.textContent).toContain('Section 1');
    expect(buttons[1]?.textContent).toContain('Section 2');
  });

  it('renders with className', () => {
    const { container } = render(
      <Accordion className="test-class" type="single" collapsible>
        <AccordionItem value="item-1" className="item-class">
          <AccordionTrigger className="trigger-class">Section 1</AccordionTrigger>
          <AccordionContent className="content-class">Content</AccordionContent>
        </AccordionItem>
      </Accordion>
    );

    // Verify classes are applied correctly
    const accordionRoot = container.querySelector('[data-orientation="vertical"]');
    const accordionItem = container.querySelector('.item-class');
    const trigger = container.querySelector('.trigger-class');
    const content = container.querySelector('.content-class');
    
    expect(accordionRoot).not.toBeNull();
    expect(accordionRoot?.className).toContain('test-class');
    
    expect(accordionItem).not.toBeNull();
    expect(accordionItem?.className).toContain('item-class');
    
    expect(trigger).not.toBeNull();
    expect(trigger?.textContent).toContain('Section 1');
    
    expect(content?.parentElement?.textContent).toContain('Content');
  });

  it('renders multiple type accordion', () => {
    const { container } = render(
      <Accordion type="multiple">
        <AccordionItem value="item-1">
          <AccordionTrigger>Section 1</AccordionTrigger>
          <AccordionContent>Content for section 1</AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger>Section 2</AccordionTrigger>
          <AccordionContent>Content for section 2</AccordionContent>
        </AccordionItem>
      </Accordion>
    );

    // Check that multiple accordion items are rendered
    const accordionRoot = container.querySelector('[data-orientation="vertical"]');
    const buttons = container.querySelectorAll('button');
    
    expect(accordionRoot).not.toBeNull();
    expect(accordionRoot?.getAttribute('type')).toBe('multiple');
    
    expect(buttons.length).toBe(2);
    expect(buttons[0]?.textContent).toContain('Section 1');
    expect(buttons[1]?.textContent).toContain('Section 2');
  });
});
