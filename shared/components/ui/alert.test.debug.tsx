/**
 * @vitest-environment jsdom
 */
import { describe, it } from 'vitest';
import { render } from '@testing-library/react';
import { Alert, AlertDescription, AlertTitle } from './alert';
import React from 'react';

describe('Alert component debug', () => {
  it('debug element structure', () => {
    const { container } = render(
      <Alert>
        <AlertTitle>Alert Title</AlertTitle>
        <AlertDescription>This is an alert description</AlertDescription>
      </Alert>
    );

    // Log the HTML structure
    console.log('HTML STRUCTURE:', container.innerHTML);
  });
});
