/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, cleanup } from '@testing-library/react';
import { Alert, AlertDescription, AlertTitle } from './alert';
import React from 'react';

// Mock class-variance-authority
vi.mock('class-variance-authority', () => {
  return {
    cva: () => (props: any) => {
      const variant = props?.variant || 'default';
      let className = 'relative w-full rounded-lg border px-4 py-3 text-sm';
      
      if (variant === 'default') {
        className += ' bg-background text-foreground';
      } else if (variant === 'destructive') {
        className += ' border-destructive/50 text-destructive';
      }
      
      return className;
    }
  };
});

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Alert component', () => {
  it('renders alert with title and description', () => {
    const { container } = render(
      <Alert data-testid="test-alert">
        <AlertTitle data-testid="test-title">Alert Title</AlertTitle>
        <AlertDescription data-testid="test-description">This is an alert description</AlertDescription>
      </Alert>
    );

    const alertElem = container.querySelector('[data-testid="test-alert"]');
    const title = container.querySelector('[data-testid="test-title"]');
    const description = container.querySelector('[data-testid="test-description"]');
    
    expect(alertElem).not.toBeNull();
    expect(title).not.toBeNull();
    expect(title?.textContent).toBe('Alert Title');
    expect(description).not.toBeNull();
    expect(description?.textContent).toBe('This is an alert description');
  });

  it('renders with custom variant', () => {
    const { container } = render(
      <Alert variant="destructive" data-testid="test-alert">
        <AlertTitle data-testid="test-title">Destructive Alert</AlertTitle>
        <AlertDescription>This is a destructive alert</AlertDescription>
      </Alert>
    );

    const alertElem = container.querySelector('[data-testid="test-alert"]');
    const title = container.querySelector('[data-testid="test-title"]');
    
    expect(alertElem).not.toBeNull();
    expect(alertElem?.className).toContain('border-destructive/50');
    expect(alertElem?.className).toContain('text-destructive');
    expect(title?.textContent).toBe('Destructive Alert');
  });

  it('applies custom className', () => {
    const { container } = render(
      <Alert className="test-alert-class" data-testid="test-alert">
        <AlertTitle className="test-title-class" data-testid="test-title">Test Title</AlertTitle>
        <AlertDescription className="test-desc-class" data-testid="test-desc">Test Description</AlertDescription>
      </Alert>
    );

    const alertElem = container.querySelector('[data-testid="test-alert"]');
    const title = container.querySelector('[data-testid="test-title"]');
    const description = container.querySelector('[data-testid="test-desc"]');
    
    expect(alertElem).not.toBeNull();
    expect(alertElem?.className).toContain('test-alert-class');
    expect(alertElem?.getAttribute('role')).toBe('alert');
    
    expect(title).not.toBeNull();
    expect(title?.className).toContain('test-title-class');
    expect(title?.textContent).toBe('Test Title');
    
    expect(description).not.toBeNull();
    expect(description?.className).toContain('test-desc-class');
    expect(description?.textContent).toBe('Test Description');
  });
});
