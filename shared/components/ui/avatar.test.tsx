/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { Avatar, AvatarFallback, AvatarImage } from './avatar';
import React from 'react';

// Mock Radix UI Avatar components
vi.mock('@radix-ui/react-avatar', () => {
  return {
    Root: ({ className, children, ...props }: any) => (
      <span className={className} {...props}>
        {children}
      </span>
    ),
    Image: ({ className, src, alt, onLoadingStatusChange, ...props }: any) => {
      React.useEffect(() => {
        if (src === 'invalid-src') {
          onLoadingStatusChange?.('error');
        } else {
          onLoadingStatusChange?.('loaded');
        }
      }, [src, onLoadingStatusChange]);
      
      return src !== 'invalid-src' ? (
        <img className={className} src={src} alt={alt} {...props} />
      ) : null;
    },
    Fallback: ({ className, children, ...props }: any) => (
      <span className={className} {...props}>
        {children}
      </span>
    ),
  };
});

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Avatar component', () => {
  it('renders avatar with image', () => {
    const { container } = render(
      <Avatar>
        <AvatarImage src="https://example.com/avatar.jpg" alt="User avatar" />
        <AvatarFallback>JD</AvatarFallback>
      </Avatar>
    );

    const image = container.querySelector('img');
    expect(image).not.toBeNull();
    expect(image?.getAttribute('src')).toBe('https://example.com/avatar.jpg');
    expect(image?.getAttribute('alt')).toBe('User avatar');
  });

  it('shows fallback when image is not loaded', () => {
    const { container } = render(
      <Avatar>
        <AvatarImage src="invalid-src" alt="Invalid avatar" />
        <AvatarFallback>JD</AvatarFallback>
      </Avatar>
    );

    // Our mock handles this automatically based on src="invalid-src"
    const fallback = container.querySelector('.flex.h-full.w-full');
    expect(fallback).not.toBeNull();
    expect(fallback?.textContent).toBe('JD');
  });

  it('applies custom className', () => {
    const { container } = render(
      <Avatar className="test-avatar-class">
        <AvatarImage className="test-image-class" src="test.jpg" alt="Test" />
        <AvatarFallback className="test-fallback-class">AB</AvatarFallback>
      </Avatar>
    );

    const avatarRoot = container.querySelector('.test-avatar-class');
    const image = container.querySelector('.test-image-class');
    
    expect(avatarRoot).not.toBeNull();
    expect(avatarRoot?.className).toContain('test-avatar-class');
    
    expect(image).not.toBeNull();
    expect(image?.className).toContain('test-image-class');
    expect(image?.getAttribute('src')).toBe('test.jpg');
  });
});
