/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, afterEach } from 'vitest';
import { render, cleanup } from '@testing-library/react';
import { Badge } from './badge';
import React from 'react';

describe('Badge component', () => {
  afterEach(() => {
    cleanup();
  });
  
  it('renders with default variant', () => {
    const { container } = render(<Badge>Default Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.tagName).toBe('DIV');
    expect(badge.textContent).toBe('Default Badge');
    expect(badge.className).toContain('inline-flex');
    expect(badge.className).toContain('rounded-md');
  });

  it('renders with default variant (primary)', () => {
    const { container } = render(<Badge variant="default">Primary Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Primary Badge');
    expect(badge.className).toContain('bg-primary');
  });

  it('renders with secondary variant', () => {
    const { container } = render(<Badge variant="secondary">Secondary Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Secondary Badge');
    expect(badge.className).toContain('bg-secondary');
  });

  it('renders with outline variant', () => {
    const { container } = render(<Badge variant="outline">Outline Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Outline Badge');
    expect(badge.className).toContain('border');
  });

  it('renders with destructive variant', () => {
    const { container } = render(<Badge variant="destructive">Destructive Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Destructive Badge');
    expect(badge.className).toContain('bg-destructive');
  });

  it('applies custom className correctly', () => {
    const { container } = render(<Badge className="custom-class">Custom Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Custom Badge');
    expect(badge.className).toContain('custom-class');
  });

  it('renders with red color', () => {
    const { container } = render(<Badge color="red">Red Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Red Badge');
    expect(badge.className).toContain('bg-red-500');
  });

  it('renders with green color', () => {
    const { container } = render(<Badge color="green">Green Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Green Badge');
    expect(badge.className).toContain('bg-green-500');
  });

  it('renders with outline variant and color', () => {
    const { container } = render(<Badge variant="outline" color="blue">Blue Outline Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Blue Outline Badge');
    expect(badge.className).toContain('border-blue-200');
    expect(badge.className).toContain('bg-blue-50');
    expect(badge.className).toContain('text-blue-500');
  });
  
  it('renders with outline-primary variant', () => {
    const { container } = render(<Badge variant="outline-primary">Outline Primary Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Outline Primary Badge');
    expect(badge.className).toContain('text-primary');
    expect(badge.className).toContain('border-primary/50');
  });
  
  it('renders with yellow color', () => {
    const { container } = render(<Badge color="yellow">Yellow Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Yellow Badge');
    expect(badge.className).toContain('bg-yellow-500');
    expect(badge.className).toContain('text-black');
  });
  
  it('renders with purple color', () => {
    const { container } = render(<Badge color="purple">Purple Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Purple Badge');
    expect(badge.className).toContain('bg-purple-500');
  });
  
  it('renders with gray color', () => {
    const { container } = render(<Badge color="gray">Gray Badge</Badge>);

    const badge = container.firstChild as HTMLElement;
    expect(badge).not.toBeNull();
    expect(badge.textContent).toBe('Gray Badge');
    expect(badge.className).toContain('bg-gray-500');
  });
});
