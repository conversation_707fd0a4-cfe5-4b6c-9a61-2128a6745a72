/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { Button } from './button';
import React from 'react';

// Mock Radix UI Slot and class-variance-authority
vi.mock('@radix-ui/react-slot', () => {
  return {
    Slot: ({ children, className, ...props }: any) => {
      const child = React.Children.only(children);
      return React.cloneElement(child, { className, ...props });
    }
  };
});

vi.mock('class-variance-authority', () => {
  return {
    cva: () => (props: any) => {
      const variant = props?.variant || 'default';
      const size = props?.size || 'default';
      const customClass = props?.className || '';
      
      let className = 'inline-flex items-center justify-center';
      
      switch (variant) {
        case 'default':
          className += ' bg-primary text-primary-foreground';
          break;
        case 'destructive':
          className += ' bg-destructive text-destructive-foreground';
          break;
        case 'outline':
          className += ' border-[1px] border-gray-300 bg-background';
          break;
        case 'secondary':
          className += ' bg-secondary text-secondary-foreground';
          break;
        case 'ghost':
          className += ' hover:bg-accent hover:text-accent-foreground';
          break;
        case 'link':
          className += ' text-primary underline-offset-4';
          break;
      }
      
      switch (size) {
        case 'default':
          className += ' h-9 px-4 py-2 rounded-[10px]';
          break;
        case 'sm':
          className += ' h-8 rounded-lg px-3 text-xs';
          break;
        case 'lg':
          className += ' h-10 rounded-lg px-8';
          break;
        case 'icon':
          className += ' h-9 w-9';
          break;
      }
      
      return `${className} ${customClass}`;
    }
  };
});

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Button component', () => {
  it('should render button with default variant', () => {
    const { container } = render(<Button>Click me</Button>);
    const button = container.querySelector('button');
    expect(button).toBeDefined();
    expect(button?.className).toContain('bg-primary');
  });

  it('should render button with custom variant', () => {
    const { container } = render(<Button variant="destructive">Delete</Button>);
    const button = container.querySelector('button');
    expect(button).toBeDefined();
    expect(button?.className).toContain('bg-destructive');
  });

  it('should render button with custom size', () => {
    const { container } = render(<Button size="sm">Small</Button>);
    const button = container.querySelector('button');
    expect(button).toBeDefined();
    expect(button?.className).toContain('h-8');
  });

  it('should render as child when asChild is true', () => {
    const { container } = render(
      <Button asChild>
        <a href="#">Link Button</a>
      </Button>
    );
    const link = container.querySelector('a');
    expect(link).toBeDefined();
    expect(link?.tagName).toBe('A');
  });

  it('should pass additional props to the button element', () => {
    const { container } = render(<Button disabled>Disabled</Button>);
    const button = container.querySelector('button');
    expect(button).toBeDefined();
    expect(button?.hasAttribute('disabled')).toBe(true);
  });

  it('should apply custom className alongside default styles', () => {
    const { container } = render(<Button className="test-class">Custom Class</Button>);
    const button = container.querySelector('button');
    expect(button).toBeDefined();
    expect(button?.className).toContain('test-class');
    expect(button?.className).toContain('bg-primary'); // Should still have default styles
  });
});
