/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, afterEach } from 'vitest';
import { render, cleanup } from '@testing-library/react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from './card';
import React from 'react';

describe('Card components', () => {
  afterEach(() => {
    cleanup();
  });

  // Test the Card component
  describe('Card', () => {
    it('should render correctly with default props', () => {
      const { container } = render(<Card data-testid="test-card">Card Content</Card>);
      const card = container.querySelector('[data-testid="test-card"]');
      expect(card).not.toBeNull();
      expect(card?.className).toContain('rounded-xl');
      expect(card?.className).toContain('border');
      expect(card?.className).toContain('bg-card');
      expect(card?.textContent).toBe('Card Content');
    });

    it('should apply custom className', () => {
      const { container } = render(<Card data-testid="test-card" className="test-class">Card Content</Card>);
      const card = container.querySelector('[data-testid="test-card"]');
      expect(card?.className).toContain('test-class');
    });
  });

  // Test the CardHeader component
  describe('CardHeader', () => {
    it('should render correctly with default props', () => {
      const { container } = render(<CardHeader data-testid="test-header">Header Content</CardHeader>);
      const header = container.querySelector('[data-testid="test-header"]');
      expect(header).not.toBeNull();
      expect(header?.className).toContain('flex');
      expect(header?.className).toContain('p-6');
      expect(header?.textContent).toBe('Header Content');
    });

    it('should apply custom className', () => {
      const { container } = render(<CardHeader data-testid="test-header" className="test-class">Header Content</CardHeader>);
      const header = container.querySelector('[data-testid="test-header"]');
      expect(header?.className).toContain('test-class');
    });
  });

  // Test the CardTitle component
  describe('CardTitle', () => {
    it('should render correctly with default props', () => {
      const { container } = render(<CardTitle data-testid="test-title">Title Content</CardTitle>);
      const title = container.querySelector('[data-testid="test-title"]');
      expect(title).not.toBeNull();
      expect(title?.className).toContain('font-semibold');
      expect(title?.textContent).toBe('Title Content');
    });

    it('should apply custom className', () => {
      const { container } = render(<CardTitle data-testid="test-title" className="test-class">Title Content</CardTitle>);
      const title = container.querySelector('[data-testid="test-title"]');
      expect(title?.className).toContain('test-class');
    });
  });

  // Test the CardDescription component
  describe('CardDescription', () => {
    it('should render correctly with default props', () => {
      const { container } = render(<CardDescription data-testid="test-desc">Description Content</CardDescription>);
      const desc = container.querySelector('[data-testid="test-desc"]');
      expect(desc).not.toBeNull();
      expect(desc?.className).toContain('text-sm');
      expect(desc?.textContent).toBe('Description Content');
    });

    it('should apply custom className', () => {
      const { container } = render(<CardDescription data-testid="test-desc" className="test-class">Description Content</CardDescription>);
      const desc = container.querySelector('[data-testid="test-desc"]');
      expect(desc?.className).toContain('test-class');
    });
  });

  // Test the CardContent component
  describe('CardContent', () => {
    it('should render correctly with default props', () => {
      const { container } = render(<CardContent data-testid="test-content">Content</CardContent>);
      const content = container.querySelector('[data-testid="test-content"]');
      expect(content).not.toBeNull();
      expect(content?.className).toContain('p-6');
      expect(content?.className).toContain('pt-0');
      expect(content?.textContent).toBe('Content');
    });

    it('should apply custom className', () => {
      const { container } = render(<CardContent data-testid="test-content" className="test-class">Content</CardContent>);
      const content = container.querySelector('[data-testid="test-content"]');
      expect(content?.className).toContain('test-class');
    });
  });

  // Test the CardFooter component
  describe('CardFooter', () => {
    it('should render correctly with default props', () => {
      const { container } = render(<CardFooter data-testid="test-footer">Footer Content</CardFooter>);
      const footer = container.querySelector('[data-testid="test-footer"]');
      expect(footer).not.toBeNull();
      expect(footer?.className).toContain('flex');
      expect(footer?.className).toContain('p-6');
      expect(footer?.className).toContain('pt-0');
      expect(footer?.textContent).toBe('Footer Content');
    });

    it('should apply custom className', () => {
      const { container } = render(<CardFooter data-testid="test-footer" className="test-class">Footer Content</CardFooter>);
      const footer = container.querySelector('[data-testid="test-footer"]');
      expect(footer?.className).toContain('test-class');
    });
  });

  // Test all components together in a comprehensive card
  describe('Complete Card', () => {
    it('should render a complete card with all components', () => {
      const { container } = render(
        <Card data-testid="test-card">
          <CardHeader data-testid="test-header">
            <CardTitle data-testid="test-title">Card Title</CardTitle>
            <CardDescription data-testid="test-desc">Card Description</CardDescription>
          </CardHeader>
          <CardContent data-testid="test-content">Card Content</CardContent>
          <CardFooter data-testid="test-footer">Card Footer</CardFooter>
        </Card>
      );
      
      expect(container.querySelector('[data-testid="test-title"]')).not.toBeNull();
      expect(container.querySelector('[data-testid="test-desc"]')).not.toBeNull();
      expect(container.querySelector('[data-testid="test-content"]')).not.toBeNull();
      expect(container.querySelector('[data-testid="test-footer"]')).not.toBeNull();

      expect(container.querySelector('[data-testid="test-title"]')?.textContent).toBe('Card Title');
      expect(container.querySelector('[data-testid="test-desc"]')?.textContent).toBe('Card Description');
      expect(container.querySelector('[data-testid="test-content"]')?.textContent).toBe('Card Content');
      expect(container.querySelector('[data-testid="test-footer"]')?.textContent).toBe('Card Footer');
    });
  });
});
