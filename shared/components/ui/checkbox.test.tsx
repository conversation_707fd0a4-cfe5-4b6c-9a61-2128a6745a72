/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, fireEvent, cleanup } from '@testing-library/react';
import { Checkbox } from './checkbox';
import React from 'react';

vi.mock('@radix-ui/react-checkbox', () => {
  return {
    Root: ({ children, checked, className, disabled, onCheckedChange, ...props }: any) => (
      <div 
        className={className} 
        data-testid="checkbox-root" 
        data-state={checked ? "checked" : "unchecked"}
        disabled={disabled}
        onClick={() => onCheckedChange && onCheckedChange(!checked)}
        {...props}
      >
        {children}
      </div>
    ),
    Indicator: ({ children, className, ...props }: any) => (
      <div className={className} data-testid="checkbox-indicator" {...props}>
        {children}
      </div>
    ),
  };
});

vi.mock('lucide-react', () => ({
  Check: ({ className }: any) => <div className={className} data-testid="check-icon" />,
}));

describe('Checkbox component', () => {
  afterEach(() => {
    cleanup();
  });
  
  it('should render correctly', () => {
    const { container } = render(<Checkbox data-testid="test-checkbox" />);
    const checkbox = container.querySelector('[data-testid="test-checkbox"]');
    expect(checkbox).not.toBeNull();
  });

  it('should apply className correctly', () => {
    const { container } = render(<Checkbox data-testid="test-checkbox" className="test-class" />);
    const checkbox = container.querySelector('[data-testid="test-checkbox"]');
    expect(checkbox?.className).toContain('test-class');
  });

  it('should handle checked state', () => {
    const onCheckedChange = vi.fn();
    const { container } = render(
      <Checkbox data-testid="test-checkbox" checked={true} onCheckedChange={onCheckedChange} />
    );
    
    const checkbox = container.querySelector('[data-testid="test-checkbox"]');
    expect(checkbox?.getAttribute('data-state')).toBe('checked');
    
    if (checkbox) {
      fireEvent.click(checkbox);
      expect(onCheckedChange).toHaveBeenCalledTimes(1);
    }
  });

  it('should handle disabled state', () => {
    const { container } = render(<Checkbox data-testid="test-checkbox" disabled />);
    const checkbox = container.querySelector('[data-testid="test-checkbox"]');
    expect(checkbox?.hasAttribute('disabled')).toBe(true);
  });

  it('should forward ref correctly', () => {
    const ref = React.createRef<HTMLButtonElement>();
    render(<Checkbox ref={ref} />);
    expect(ref.current).not.toBeNull();
  });

  it('should render indicator when checked', () => {
    const { container } = render(<Checkbox data-testid="test-checkbox" checked={true} />);
    
    // Find the indicators in the specific checkbox we're testing
    const checkbox = container.querySelector('[data-testid="test-checkbox"]');
    const indicator = checkbox?.querySelector('[data-testid="checkbox-indicator"]');
    const checkIcon = indicator?.querySelector('[data-testid="check-icon"]');
    
    expect(indicator).not.toBeNull();
    expect(checkIcon).not.toBeNull();
  });
});
