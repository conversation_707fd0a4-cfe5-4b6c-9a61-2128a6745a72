/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import {
  Di<PERSON>,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from './dialog';
import React from 'react';

// Mock Iconify component
vi.mock('../atoms/Iconify', () => ({
  default: ({ name, className }: any) => (
    <span data-testid="iconify" data-icon={name} className={className}>
      Icon
    </span>
  )
}));

// Mock RadixUI components
vi.mock('@radix-ui/react-dialog', () => ({
  Root: ({ children, ...props }: any) => (
    <div data-testid="dialog-root" {...props}>
      {children}
    </div>
  ),
  Trigger: ({ children, ...props }: any) => (
    <button data-testid="dialog-trigger" {...props}>
      {children}
    </button>
  ),
  Portal: ({ children, ...props }: any) => (
    <div data-testid="dialog-portal" {...props}>
      {children}
    </div>
  ),
  Overlay: ({ children, className, ...props }: any) => (
    <div data-testid="dialog-overlay" className={className} {...props}>
      {children}
    </div>
  ),
  Content: ({ children, className, ...props }: any) => (
    <div data-testid="dialog-content" className={className} {...props}>
      {children}
    </div>
  ),
  Title: ({ children, className, ...props }: any) => (
    <h2 data-testid="dialog-title" className={className} {...props}>
      {children}
    </h2>
  ),
  Description: ({ children, className, ...props }: any) => (
    <p data-testid="dialog-description" className={className} {...props}>
      {children}
    </p>
  ),
  Close: ({ children, className, ...props }: any) => (
    <button data-testid="dialog-close" className={className} {...props}>
      {children}
    </button>
  ),
}));

describe('Dialog components', () => {
  it('renders Dialog with all its parts', () => {
    const { container } = render(
      <Dialog>
        <DialogTrigger>Open Dialog</DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dialog Title</DialogTitle>
            <DialogDescription>Dialog Description</DialogDescription>
          </DialogHeader>
          <div>Dialog Content</div>
          <DialogFooter>
            <DialogClose>Close</DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );

    // Check that all dialog components render correctly
    const dialogRoot = container.querySelector('[data-testid="dialog-root"]');
    const dialogTrigger = container.querySelector('[data-testid="dialog-trigger"]');
    const dialogContent = container.querySelector('[data-testid="dialog-content"]');
    const dialogTitle = container.querySelector('[data-testid="dialog-title"]');
    const dialogDescription = container.querySelector('[data-testid="dialog-description"]');
    const dialogClose = container.querySelector('[data-testid="dialog-close"]');

    expect(dialogRoot).not.toBeNull();
    expect(dialogTrigger).not.toBeNull();
    expect(dialogTrigger?.textContent).toBe('Open Dialog');
    expect(dialogContent).not.toBeNull();
    expect(dialogTitle).not.toBeNull();
    expect(dialogTitle?.textContent).toBe('Dialog Title');
    expect(dialogDescription).not.toBeNull();
    expect(dialogDescription?.textContent).toBe('Dialog Description');
    expect(dialogContent?.textContent).toContain('Dialog Content');
    expect(dialogClose).not.toBeNull();
    expect(dialogClose?.textContent?.includes('Close')).toBe(true);
  });

  it('applies custom className to DialogContent', () => {
    const { container } = render(
      <Dialog>
        <DialogContent className="custom-content-class">
          Dialog Content
        </DialogContent>
      </Dialog>
    );

    const content = container.querySelector('[data-testid="dialog-content"]');
    expect(content?.className).toContain('custom-content-class');
  });

  it('applies custom className to DialogHeader', () => {
    const { container } = render(
      <DialogHeader className="custom-header-class">
        Header Content
      </DialogHeader>
    );

    const headerElement = container.firstElementChild;
    expect(headerElement?.className).toContain('custom-header-class');
    expect(headerElement?.textContent).toBe('Header Content');
  });

  it('applies custom className to DialogFooter', () => {
    const { container } = render(
      <DialogFooter className="custom-footer-class">
        Footer Content
      </DialogFooter>
    );

    const footerElement = container.firstElementChild;
    expect(footerElement?.className).toContain('custom-footer-class');
    expect(footerElement?.textContent).toBe('Footer Content');
  });
});
