/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Input } from './input';
import React from 'react';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Input component', () => {
  // Debug render to see what's actually being rendered
  it('renders correctly', () => {
    const { container } = render(<Input placeholder="Enter text" />);
    const input = container.querySelector('input');
    expect(input).not.toBeNull();
    expect(input?.getAttribute('placeholder')).toBe('Enter text');
  });

  it('handles value changes', async () => {
    const onChange = vi.fn();
    const { container } = render(<Input value="" onChange={onChange} />);
    
    const input = container.querySelector('input');
    if (input) {
      await userEvent.type(input, 'test');
      expect(onChange).toHaveBeenCalled();
    } else {
      throw new Error('Input element not found');
    }
  });

  it('respects disabled prop', () => {
    const { container } = render(<Input disabled placeholder="Disabled input" />);
    
    const input = container.querySelector('input');
    expect(input).not.toBeNull();
    expect(input?.hasAttribute('disabled')).toBe(true);
  });

  it('applies custom className', () => {
    const { container } = render(<Input className="custom-class" placeholder="custom input" />);
    
    const input = container.querySelector('input');
    expect(input).not.toBeNull();
    expect(input?.className).toContain('custom-class');
  });

  it('forwards refs', () => {
    const ref = React.createRef<HTMLInputElement>();
    render(<Input ref={ref} />);
    
    expect(ref.current).not.toBeNull();
    expect(ref.current?.tagName).toBe('INPUT');
  });
});
