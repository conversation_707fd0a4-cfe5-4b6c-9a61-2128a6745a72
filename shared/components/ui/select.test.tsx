/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';
import React from 'react';

// Add jest-dom matchers
expect.extend(matchers);

// Mock the select component and its parts
const SelectTriggerId = 'select-trigger';

vi.mock('./select', () => {
  return {
    Select: ({ children, defaultValue, ...props }: any) => (
      <div data-testid="select-root" {...props}>{children}</div>
    ),
    SelectGroup: ({ children, ...props }: any) => (
      <div data-testid="select-group" {...props}>{children}</div>
    ),
    SelectValue: ({ children, placeholder, ...props }: any) => (
      <div data-testid="select-value" {...props}>{children || placeholder}</div>
    ),
    SelectTrigger: ({ children, className, ...props }: any) => {
      const testId = props['data-testid'] || SelectTriggerId;
      return (
        <button data-testid={testId} className={className} {...props}>{children}</button>
      );
    },
    SelectContent: ({ children, ...props }: any) => (
      <div data-testid="select-content" {...props}>{children}</div>
    ),
    SelectLabel: ({ children, ...props }: any) => (
      <div data-testid="select-label" {...props}>{children}</div>
    ),
    SelectItem: ({ children, value, disabled, ...props }: any) => {
      const testId = props['data-testid'] || `select-item-${value}`;
      return (
        <div 
          data-testid={testId} 
          data-value={value} 
          data-disabled={disabled || undefined} 
          {...props}
        >
          {children}
        </div>
      );
    }
  };
});

// Mock Radix UI components
vi.mock('@radix-ui/react-select', () => {
  return {
    Root: ({ children, ...props }: any) => <div>{children}</div>,
    Trigger: ({ children, ...props }: any) => <button>{children}</button>,
    Value: ({ children, ...props }: any) => <span>{children}</span>,
    Portal: ({ children, ...props }: any) => <div>{children}</div>,
    Content: ({ children, ...props }: any) => <div>{children}</div>,
    Group: ({ children, ...props }: any) => <div>{children}</div>,
    Label: ({ children, ...props }: any) => <div>{children}</div>,
    Item: ({ children, ...props }: any) => <div>{children}</div>,
    ItemText: ({ children, ...props }: any) => <span>{children}</span>,
    ItemIndicator: ({ children, ...props }: any) => <span>{children}</span>,
    Separator: ({ ...props }: any) => <hr />,
    Viewport: ({ children, ...props }: any) => <div>{children}</div>,
    ScrollUpButton: ({ ...props }: any) => <button>↑</button>,
    ScrollDownButton: ({ ...props }: any) => <button>↓</button>,
    Icon: () => <span>▼</span>
  };
});

// Now import the mocked components
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from './select';

describe('Select components', () => {
  beforeEach(() => {
    // Clean up before each test to ensure isolation
    cleanup();
  });
  
  it('renders Select with its parts', async () => {
    const { container } = render(
      <Select defaultValue="apple">
        <SelectTrigger data-testid="select-trigger">
          <SelectValue placeholder="Select a fruit" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Fruits</SelectLabel>
            <SelectItem value="apple">Apple</SelectItem>
            <SelectItem value="banana">Banana</SelectItem>
            <SelectItem value="orange">Orange</SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    );

    // Debug to see what's being rendered
    // console.log(container.innerHTML);
    
    expect(container.querySelector('[data-testid="select-trigger"]')).toBeInTheDocument();
  });

  it('renders disabled SelectItem correctly', () => {
    const { container } = render(
      <Select>
        <SelectTrigger>
          <SelectValue placeholder="Select a fruit" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="apple" disabled data-testid="disabled-item">
            Apple
          </SelectItem>
          <SelectItem value="banana">Banana</SelectItem>
        </SelectContent>
      </Select>
    );

    const disabledItem = container.querySelector('[data-testid="disabled-item"]');
    expect(disabledItem).toBeInTheDocument();
    expect(disabledItem).toHaveAttribute('data-disabled');
  });

  it('applies custom className to Select components', () => {
    const { container } = render(
      <Select>
        <SelectTrigger className="custom-trigger" data-testid="custom-select-trigger">
          <SelectValue className="custom-value" placeholder="Select a fruit" />
        </SelectTrigger>
      </Select>
    );

    const trigger = container.querySelector('[data-testid="custom-select-trigger"]');
    expect(trigger).toBeInTheDocument();
    expect(trigger).toHaveClass('custom-trigger');
  });
});
