/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, cleanup } from '@testing-library/react';
import { Separator } from './separator';
import React from 'react';

// Create a more effective mock for Radix UI Separator
vi.mock('@radix-ui/react-separator', () => ({
  Root: ({ className, orientation = 'horizontal', decorative, ...props }: any) => (
    <div
      role="separator"
      data-testid="separator"
      orientation={orientation}
      aria-orientation={orientation}
      className={className}
      decorative={decorative?.toString()}
      {...props}
    >
      {props.children}
    </div>
  ),
}));

describe('Separator component', () => {
  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });
  
  it('renders horizontal separator by default', () => {
    const { container } = render(<Separator />);
    
    const separator = container.querySelector('[role="separator"]');
    expect(separator).not.toBeNull();
    expect(separator?.getAttribute('orientation')).toBe('horizontal');
    expect(separator?.className).toContain('h-[1px]');
    expect(separator?.className).toContain('w-full');
  });

  it('renders vertical separator when orientation is vertical', () => {
    const { container } = render(<Separator orientation="vertical" />);
    
    const separator = container.querySelector('[role="separator"]');
    expect(separator).not.toBeNull();
    expect(separator?.getAttribute('orientation')).toBe('vertical');
    expect(separator?.className).toContain('h-full');
    expect(separator?.className).toContain('w-[1px]');
  });

  it('applies custom className correctly', () => {
    const { container } = render(<Separator className="custom-class" />);
    
    const separator = container.querySelector('[role="separator"]');
    expect(separator).not.toBeNull();
    expect(separator?.className).toContain('custom-class');
  });

  it('sets decorative attribute correctly', () => {
    const { container } = render(<Separator decorative={false} />);
    
    const separator = container.querySelector('[role="separator"]');
    expect(separator).not.toBeNull();
    expect(separator?.getAttribute('decorative')).toBe('false');
  });

  it('passes other props correctly', () => {
    const { container } = render(<Separator data-testid="test-separator" aria-label="Test separator" />);
    
    const separator = container.querySelector('[data-testid="test-separator"]');
    expect(separator).not.toBeNull();
    expect(separator?.getAttribute('aria-label')).toBe('Test separator');
  });
});
