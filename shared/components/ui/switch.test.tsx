/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, fireEvent, cleanup } from '@testing-library/react';
import { Switch } from './switch';
import React from 'react';

// Mock the Switch primitive from Radix UI
vi.mock('@radix-ui/react-switch', () => {
  return {
    Root: ({ className, checked, onCheckedChange, disabled, children, ...props }: any) => (
      <button 
        data-testid="switch-root" 
        className={className} 
        data-state={checked ? 'checked' : 'unchecked'} 
        aria-checked={checked}
        disabled={disabled}
        onClick={() => onCheckedChange && onCheckedChange(!checked)}
        {...props} 
      >
        {children}
      </button>
    ),
    Thumb: ({ className, ...props }: any) => (
      <span data-testid="switch-thumb" className={className} {...props} />
    ),
  };
});

describe('Switch component', () => {
  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });
  
  it('renders with default props', () => {
    const { container } = render(<Switch />);
    
    const switchRoot = container.querySelector('[data-testid="switch-root"]');
    const switchThumb = container.querySelector('[data-testid="switch-thumb"]');
    
    expect(switchRoot).not.toBeNull();
    expect(switchThumb).not.toBeNull();
    expect(switchRoot?.getAttribute('data-state')).toBe('unchecked');
  });

  it('renders in checked state', () => {
    const { container } = render(<Switch checked={true} />);
    
    const switchRoot = container.querySelector('[data-testid="switch-root"]');
    expect(switchRoot).not.toBeNull();
    expect(switchRoot?.getAttribute('data-state')).toBe('checked');
    expect(switchRoot?.getAttribute('aria-checked')).toBe('true');
  });

  it('calls onCheckedChange when clicked', () => {
    const onCheckedChange = vi.fn();
    const { container } = render(<Switch onCheckedChange={onCheckedChange} />);
    
    const switchRoot = container.querySelector('[data-testid="switch-root"]');
    expect(switchRoot).not.toBeNull();
    if (switchRoot) {
      fireEvent.click(switchRoot);
      expect(onCheckedChange).toHaveBeenCalledWith(true);
    }
  });

  it('renders in disabled state', () => {
    const { container } = render(<Switch disabled />);
    
    const switchRoot = container.querySelector('[data-testid="switch-root"]');
    expect(switchRoot).not.toBeNull();
    expect(switchRoot?.hasAttribute('disabled')).toBe(true);
  });

  it('applies custom className', () => {
    const { container } = render(<Switch className="custom-class" />);
    
    const switchRoot = container.querySelector('[data-testid="switch-root"]');
    expect(switchRoot).not.toBeNull();
    expect(switchRoot?.className).toContain('custom-class');
  });
});
