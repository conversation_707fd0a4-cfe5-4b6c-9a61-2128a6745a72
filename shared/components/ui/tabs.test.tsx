/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from './tabs';
import React from 'react';

// Create TabsContext directly instead of in the mock
const TabsContext = React.createContext({ value: '', onValueChange: (val: string) => {} });

// Mock Radix Tabs
vi.mock('@radix-ui/react-tabs', () => {
  return {
    Root: ({ children, defaultValue, value, onValueChange, className, ...props }: any) => {
      const [currentValue, setCurrentValue] = React.useState(value || defaultValue);
      
      const handleValueChange = (val: string) => {
        setCurrentValue(val);
        onValueChange?.(val);
      };
      
      return (
        <TabsContext.Provider value={{ value: currentValue, onValueChange: handleValueChange }}>
          <div 
            data-testid="tabs-root" 
            className={className}
            {...props}
          >
            {children}
          </div>
        </TabsContext.Provider>
      );
    },
    List: ({ children, className, ...props }: any) => (
      <div data-testid="tabs-list" className={className} {...props}>
        {children}
        <div className="flex-1 h-full border-b border-border/50"></div>
      </div>
    ),
    Trigger: ({ children, value, className, ...props }: any) => {
      const context = React.useContext(TabsContext);
      const isActive = context.value === value;
      
      return (
        <button 
          data-testid="tabs-trigger" 
          data-value={value}
          data-state={isActive ? 'active' : 'inactive'}
          className={className}
          onClick={() => context.onValueChange(value)}
          {...props}
        >
          {children}
        </button>
      );
    },
    Content: ({ children, value, className, ...props }: any) => {
      const context = React.useContext(TabsContext);
      const isActive = context.value === value;
      
      if (!isActive) return null;
      
      return (
        <div 
          data-testid="tabs-content" 
          data-value={value} 
          data-state="active"
          className={className} 
          {...props}
        >
          {children}
        </div>
      );
    },
  };
});

describe('Tabs component', () => {
  it('renders tabs with correct structure', () => {
    const { container } = render(
      <Tabs defaultValue="tab1">
        <TabsList>
          <TabsTrigger value="tab1">Tab 1</TabsTrigger>
          <TabsTrigger value="tab2">Tab 2</TabsTrigger>
        </TabsList>
        <TabsContent value="tab1">Tab 1 Content</TabsContent>
        <TabsContent value="tab2">Tab 2 Content</TabsContent>
      </Tabs>
    );

    // Check that structure renders correctly
    const tabsRoot = container.querySelector('[data-testid="tabs-root"]');
    const tabsList = container.querySelector('[data-testid="tabs-list"]');
    const tabsTriggers = container.querySelectorAll('[data-testid="tabs-trigger"]');
    const tabsContent = container.querySelector('[data-testid="tabs-content"]');
    
    expect(tabsRoot).not.toBeNull();
    expect(tabsList).not.toBeNull();
    expect(tabsTriggers.length).toBe(2);
    expect(tabsContent).not.toBeNull();
    
    // Default tab should be shown
    expect(tabsContent?.textContent).toBe('Tab 1 Content');
    expect(container.textContent).not.toContain('Tab 2 Content');
  });

  it('switches tabs when triggers are clicked', () => {
    const { container } = render(
      <Tabs defaultValue="tab1">
        <TabsList>
          <TabsTrigger value="tab1">Tab 1</TabsTrigger>
          <TabsTrigger value="tab2">Tab 2</TabsTrigger>
        </TabsList>
        <TabsContent value="tab1">Tab 1 Content</TabsContent>
        <TabsContent value="tab2">Tab 2 Content</TabsContent>
      </Tabs>
    );

    // Click on the second tab
    const secondTab = container.querySelectorAll('[data-testid="tabs-trigger"]')[1];
    fireEvent.click(secondTab);
    
    // Now the second tab content should be shown
    const tabsContent = container.querySelector('[data-testid="tabs-content"]');
    expect(tabsContent?.textContent).toBe('Tab 2 Content');
    expect(container.textContent).not.toContain('Tab 1 Content');
  });

  it('applies custom className to components', () => {
    const { container } = render(
      <Tabs defaultValue="tab1" className="custom-tabs-class">
        <TabsList className="custom-list-class">
          <TabsTrigger className="custom-trigger-class" value="tab1">Tab 1</TabsTrigger>
        </TabsList>
        <TabsContent className="custom-content-class" value="tab1">Content</TabsContent>
      </Tabs>
    );

    const tabsRoot = container.querySelector('[data-testid="tabs-root"]');
    const tabsList = container.querySelector('[data-testid="tabs-list"]');
    const tabsTrigger = container.querySelector('[data-testid="tabs-trigger"]');
    const tabsContent = container.querySelector('[data-testid="tabs-content"]');

    expect(tabsRoot).not.toBeNull();
    expect(tabsList).not.toBeNull();
    expect(tabsTrigger).not.toBeNull();
    expect(tabsContent).not.toBeNull();
    
    expect(tabsRoot?.className.includes('custom-tabs-class')).toBe(true);
    expect(tabsList?.className.includes('custom-list-class')).toBe(true);
    expect(tabsTrigger?.className.includes('custom-trigger-class')).toBe(true);
    expect(tabsContent?.className.includes('custom-content-class')).toBe(true);
  });
  
  // Add an additional test for more coverage
  it('handles value change callback', () => {
    const onValueChange = vi.fn();
    const { container } = render(
      <Tabs defaultValue="tab1" onValueChange={onValueChange}>
        <TabsList>
          <TabsTrigger value="tab1">Tab 1</TabsTrigger>
          <TabsTrigger value="tab2">Tab 2</TabsTrigger>
        </TabsList>
        <TabsContent value="tab1">Tab 1 Content</TabsContent>
        <TabsContent value="tab2">Tab 2 Content</TabsContent>
      </Tabs>
    );
    
    // Click on the second tab
    const secondTab = container.querySelectorAll('[data-testid="tabs-trigger"]')[1];
    fireEvent.click(secondTab);
    
    // Check if onValueChange was called with the correct value
    expect(onValueChange).toHaveBeenCalledWith('tab2');
  });
});
