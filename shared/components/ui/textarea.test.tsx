/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Textarea } from './textarea';
import React from 'react';

describe('Textarea component', () => {
  it('renders correctly with default props', () => {
    const { container } = render(<Textarea placeholder="Enter your message" />);
    
    const textarea = container.querySelector('textarea');
    expect(textarea).not.toBeNull();
    expect(textarea?.tagName).toBe('TEXTAREA');
    expect(textarea?.getAttribute('placeholder')).toBe('Enter your message');
  });

  it('handles value changes', async () => {
    const onChange = vi.fn();
    const { container } = render(<Textarea value="" onChange={onChange} />);
    
    const textarea = container.querySelector('textarea');
    if (textarea) {
      await userEvent.type(textarea, 'Hello, world!');
      expect(onChange).toHaveBeenCalled();
    } else {
      throw new Error('Textarea element not found');
    }
  });

  it('respects disabled prop', () => {
    const { container } = render(<Textarea disabled />);
    
    const textarea = container.querySelector('textarea');
    expect(textarea?.disabled).toBe(true);
  });

  it('applies custom className', () => {
    const { container } = render(<Textarea className="custom-class" />);
    
    const textarea = container.querySelector('textarea');
    expect(textarea?.className).toContain('custom-class');
  });

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLTextAreaElement>();
    render(<Textarea ref={ref} />);
    
    expect(ref.current).not.toBeNull();
    expect(ref.current?.tagName).toBe('TEXTAREA');
  });

  it('passes other props to the textarea element', () => {
    const { container } = render(<Textarea maxLength={100} rows={5} cols={40} />);
    
    const textarea = container.querySelector('textarea');
    expect(textarea?.getAttribute('maxlength')).toBe('100');
    expect(textarea?.getAttribute('rows')).toBe('5');
    expect(textarea?.getAttribute('cols')).toBe('40');
  });

  it('handles focus events', () => {
    const onFocus = vi.fn();
    const onBlur = vi.fn();
    
    const { container } = render(<Textarea onFocus={onFocus} onBlur={onBlur} />);
    
    const textarea = container.querySelector('textarea');
    if (textarea) {
      fireEvent.focus(textarea);
      expect(onFocus).toHaveBeenCalled();
      
      fireEvent.blur(textarea);
      expect(onBlur).toHaveBeenCalled();
    } else {
      throw new Error('Textarea element not found');
    }
  });
});
