import { cn } from '../../lib/utils'

import * as React from 'react'

// Extend the textarea props type to include data-testid
type TextareaProps = React.ComponentProps<'textarea'> & {
    'data-testid'?: string;
}

const Textarea = React.forwardRef<
    HTMLTextAreaElement,
    TextareaProps
>(({ className, 'data-testid': dataTestId, ...props }, ref) => {
    return (
        <textarea
            className={cn(
                'flex min-h-[60px] w-full rounded-md border border-gray-300 focus:border-transparent bg-transparent px-3 py-2 text-base shadow-[0px_1px_2px_rgba(10,_13,_18,_0.05)] placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
                className,
            )}
            ref={ref}
            data-testid={dataTestId}
            {...props}
        />
    )
})
Textarea.displayName = 'Textarea'

export { Textarea }
