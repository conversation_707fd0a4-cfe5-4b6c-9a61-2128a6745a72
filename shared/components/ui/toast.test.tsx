/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, afterEach } from 'vitest';
import { render, cleanup } from '@testing-library/react';
import { Toast, ToastAction, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from './toast';
import React from 'react';

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  X: () => <div data-testid="x-icon">X Icon</div>,
}));

// Mock Radix Toast Components
vi.mock('@radix-ui/react-toast', () => {
  return {
    Provider: ({ children, ...props }: any) => (
      <div data-testid="toast-provider" {...props}>
        {children}
      </div>
    ),
    Root: ({ children, className, ...props }: any) => (
      <div data-testid="toast-root" className={className} {...props}>
        {children}
      </div>
    ),
    Action: ({ children, className, altText, ...props }: any) => (
      <button data-testid="toast-action" className={className} aria-label={altText} {...props}>
        {children}
      </button>
    ),
    Close: ({ children, className, ...props }: any) => (
      <button data-testid="toast-close" className={className} {...props}>
        {children || <div data-testid="x-icon">X Icon</div>}
      </button>
    ),
    Description: ({ children, className, ...props }: any) => (
      <div data-testid="toast-description" className={className} {...props}>
        {children}
      </div>
    ),
    Title: ({ children, className, ...props }: any) => (
      <div data-testid="toast-title" className={className} {...props}>
        {children}
      </div>
    ),
    Viewport: ({ className, ...props }: any) => (
      <div data-testid="toast-viewport" className={className} {...props} />
    ),
  };
});

// Override createElement to ensure mocks are applied
const originalCreateElement = React.createElement;
React.createElement = function(type: any, props: any, ...children: any[]) {
  // Handle toast components
  if (type && type.displayName) {
    if (type.displayName === 'ToastProvider') {
      return originalCreateElement('div', { 'data-testid': 'toast-provider', ...props }, ...children);
    }
    if (type.displayName === 'ToastRoot') {
      return originalCreateElement('div', { 'data-testid': 'toast-root', ...props }, ...children);
    }
    if (type.displayName === 'ToastAction') {
      return originalCreateElement('button', { 'data-testid': 'toast-action', ...props }, ...children);
    }
    if (type.displayName === 'ToastClose') {
      return originalCreateElement('button', { 'data-testid': 'toast-close', ...props }, ...children);
    }
    if (type.displayName === 'ToastDescription') {
      return originalCreateElement('div', { 'data-testid': 'toast-description', ...props }, ...children);
    }
    if (type.displayName === 'ToastTitle') {
      return originalCreateElement('div', { 'data-testid': 'toast-title', ...props }, ...children);
    }
    if (type.displayName === 'ToastViewport') {
      return originalCreateElement('div', { 'data-testid': 'toast-viewport', ...props }, ...children);
    }
  }
  return originalCreateElement(type, props, ...children);
} as typeof React.createElement;

describe('Toast components', () => {
  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });
  
  it('renders all toast components correctly', () => {
    const { container } = render(
      <>
        <ToastProvider>
          <Toast>
            <ToastTitle>Toast Title</ToastTitle>
            <ToastDescription>Toast Description</ToastDescription>
            <ToastAction altText="Action">Action Button</ToastAction>
            <ToastClose />
          </Toast>
          <ToastViewport />
        </ToastProvider>
      </>
    );

    const toastProvider = container.querySelector('[data-testid="toast-provider"]');
    const toastRoot = container.querySelector('[data-testid="toast-root"]');
    const toastTitle = container.querySelector('[data-testid="toast-title"]');
    
    expect(toastProvider).not.toBeNull();
    expect(toastRoot).not.toBeNull();
    expect(toastTitle).not.toBeNull();
    const toastDescription = container.querySelector('[data-testid="toast-description"]');
    const toastAction = container.querySelector('[data-testid="toast-action"]');
    const toastClose = container.querySelector('[data-testid="toast-close"]');
    const toastViewport = container.querySelector('[data-testid="toast-viewport"]');
    
    expect(toastDescription).not.toBeNull();
    expect(toastAction).not.toBeNull();
    expect(toastClose).not.toBeNull();
    expect(toastViewport).not.toBeNull();
    
    // Check text content
    expect(toastTitle?.textContent).toBe('Toast Title');
    expect(toastDescription?.textContent).toBe('Toast Description');
    expect(toastAction?.textContent).toBe('Action Button');
  });

  it('renders Toast with custom variant', () => {
    const { container } = render(
      <Toast variant="destructive">
        <ToastTitle>Destructive Toast</ToastTitle>
      </Toast>
    );

    const toast = container.querySelector('[data-testid="toast-root"]');
    expect(toast).not.toBeNull();
    expect(toast?.className).toContain('destructive');
  });

  it('applies custom className to Toast components', () => {
    const { container } = render(
      <>
        <Toast className="custom-toast-class">Toast</Toast>
        <ToastTitle className="custom-title-class">Title</ToastTitle>
        <ToastDescription className="custom-desc-class">Description</ToastDescription>
        <ToastAction className="custom-action-class" altText="Test action">Action</ToastAction>
        <ToastClose className="custom-close-class" />
        <ToastViewport className="custom-viewport-class" />
      </>
    );

    // Use container.querySelector with data-testid selectors to find elements
    const toastRoot = container.querySelector('[data-testid="toast-root"]');
    const toastTitle = container.querySelector('[data-testid="toast-title"]');
    const toastDesc = container.querySelector('[data-testid="toast-description"]');
    const toastAction = container.querySelector('[data-testid="toast-action"]');
    const toastClose = container.querySelector('[data-testid="toast-close"]');
    const toastViewport = container.querySelector('[data-testid="toast-viewport"]');
    
    // Check that elements exist and have correct classes
    expect(toastRoot).not.toBeNull();
    expect(toastTitle).not.toBeNull();
    expect(toastDesc).not.toBeNull();
    expect(toastAction).not.toBeNull();
    expect(toastClose).not.toBeNull();
    expect(toastViewport).not.toBeNull();
    
    expect(toastRoot?.className).toContain('custom-toast-class');
    expect(toastTitle?.className).toContain('custom-title-class');
    expect(toastDesc?.className).toContain('custom-desc-class');
    expect(toastAction?.className).toContain('custom-action-class');
    expect(toastClose?.className).toContain('custom-close-class');
    expect(toastViewport?.className).toContain('custom-viewport-class');
  });
});
