import { describe, it, expect } from 'vitest';
import { theme as tailwindConfig } from './tailwind';

describe('Tailwind Config', () => {
  it('should export a valid tailwind config object', () => {
    expect(tailwindConfig).toBeDefined();
    expect(typeof tailwindConfig).toBe('object');
  });

  it('should have container configuration', () => {
    expect(tailwindConfig.container).toBeDefined();
    expect(tailwindConfig.container.center).toBe('true');
    expect(tailwindConfig.container.padding).toBe('2rem');
  });

  it('should have extended fontFamily configuration', () => {
    expect(tailwindConfig.extend.fontFamily).toBeDefined();
    expect(tailwindConfig.extend.fontFamily.inter).toBeInstanceOf(Array);
    expect(tailwindConfig.extend.fontFamily.manrope).toBeInstanceOf(Array);
  });

  it('should have extended colors configuration', () => {
    expect(tailwindConfig.extend.colors).toBeDefined();
    expect(tailwindConfig.extend.colors['base-black']).toBe('#000');
    expect(tailwindConfig.extend.colors['base-white']).toBe('#fff');
  });
});
