/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { SearchProvider, useSearch } from './search-context';
import React from 'react';

describe('SearchContext', () => {
  it('should initialize with closed state', () => {
    const { result } = renderHook(() => useSearch(), {
      wrapper: ({ children }) => <SearchProvider>{children}</SearchProvider>,
    });

    expect(result.current.open).toBe(false);
  });

  it('should toggle the search state when setOpen is called', () => {
    const { result } = renderHook(() => useSearch(), {
      wrapper: ({ children }) => <SearchProvider>{children}</SearchProvider>,
    });

    act(() => {
      result.current.setOpen(true);
    });

    expect(result.current.open).toBe(true);

    act(() => {
      result.current.setOpen(false);
    });

    expect(result.current.open).toBe(false);
  });

  it('should throw error when useSearch is used outside of SearchProvider', () => {
    // Mock console.error to prevent error output during testing
    const consoleErrorMock = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      renderHook(() => useSearch());
    }).toThrow('useSearch has to be used within <SearchContext.Provider>');

    consoleErrorMock.mockRestore();
  });
});
