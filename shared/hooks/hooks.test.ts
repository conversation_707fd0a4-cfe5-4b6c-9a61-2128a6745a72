import { describe, it, expect } from 'vitest';
import { useDebounce } from './use-debounce';
import { useDateLocale } from './use-date-locale';
import { useTableFilter } from './use-table-filter';
import useMediaQuery from './use-media-query';
import { useIsMobile } from './use-mobile';

describe('Hooks', () => {
  describe('useDebounce', () => {
    it('should be defined', () => {
      expect(useDebounce).toBeDefined();
      expect(typeof useDebounce).toBe('function');
    });
  });

  describe('useDateLocale', () => {
    it('should be defined', () => {
      expect(useDateLocale).toBeDefined();
      expect(typeof useDateLocale).toBe('function');
    });
  });

  describe('useTableFilter', () => {
    it('should be defined', () => {
      expect(useTableFilter).toBeDefined();
      expect(typeof useTableFilter).toBe('function');
    });
  });

  describe('useMediaQuery', () => {
    it('should be defined', () => {
      expect(useMediaQuery).toBeDefined();
      expect(typeof useMediaQuery).toBe('function');
    });
  });

  describe('useIsMobile', () => {
    it('should be defined', () => {
      expect(useIsMobile).toBeDefined();
      expect(typeof useIsMobile).toBe('function');
    });
  });
});
