import { beforeAll, vi } from 'vitest';
import { JSDOM } from 'jsdom';

beforeAll(() => {
  const dom = new JSDOM('<!doctype html><html><body></body></html>', {
    url: 'http://localhost/',
  });

  // @ts-expect-error window types doesnt match
  global.window = dom.window;
  global.document = dom.window.document;
  global.navigator = dom.window.navigator;

  // Ensure matchMedia is defined
  global.window.matchMedia = vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  }));

  console.log('jsdom setup applied:', {
    window: !!global.window,
    document: !!global.document,
    navigator: !!global.navigator,
  });
});