import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useDateLocale } from './use-date-locale';
import { enUS, tr } from 'date-fns/locale';

// Helper to access mocked module
let currentLanguage = 'en';

// Create a dynamic mock for the i18n hook
vi.mock('react-i18next', () => {
  return {
    useTranslation: () => ({
      t: (key: string) => key,
      i18n: {
        get language() { return currentLanguage; },
        changeLanguage: (lang: string) => {
          currentLanguage = lang;
          return Promise.resolve();
        },
      },
    }),
  };
});

// Mock React hooks to avoid invalid hook call errors
vi.mock('react', () => {
  return {
    ...vi.importActual('react'),
    useState: vi.fn((initialValue) => [initialValue, vi.fn()]),
    useEffect: vi.fn((fn) => fn()),
  };
});

describe('useDateLocale', () => {
  beforeEach(() => {
    // Reset language to English before each test
    currentLanguage = 'en';
  });
  
  it('should be defined', () => {
    expect(useDateLocale).toBeDefined();
    expect(typeof useDateLocale).toBe('function');
  });

  it('should return English locale by default', () => {
    currentLanguage = 'en';
    const result = useDateLocale();
    expect(result).toBe(enUS);
  });
  
  it('should return Turkish locale when language is Turkish', () => {
    // Set language to Turkish
    currentLanguage = 'tr';
    
    const result = useDateLocale();
    expect(result).toBe(tr);
  });
  
  it('should return English locale for unsupported languages', () => {
    // Set language to something unsupported
    currentLanguage = 'fr';
    
    const result = useDateLocale();
    expect(result).toBe(enUS);
  });
});
