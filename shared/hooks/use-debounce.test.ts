import { describe, it, expect, vi, afterEach, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useDebounce } from './use-debounce';

describe('useDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.clearAllTimers();
    vi.useRealTimers();
  });

  it('should be defined', () => {
    expect(useDebounce).toBeDefined();
    expect(typeof useDebounce).toBe('function');
  });

  it('should return initial value immediately', () => {
    const { result } = renderHook(() => useDebounce('initial', 500));
    expect(result.current).toBe('initial');
  });

  it('should debounce value updates', async () => {
    const { result, rerender } = renderHook(
      ({ value }) => useDebounce(value, 500),
      {
        initialProps: { value: 'initial' }
      }
    );

    expect(result.current).toBe('initial');

    // Update the value
    rerender({ value: 'updated' });
    
    // Value should not change immediately
    expect(result.current).toBe('initial');

    // Fast-forward time
    await act(async () => {
      vi.advanceTimersByTime(500);
    });

    // Now it should be updated
    expect(result.current).toBe('updated');
  });

  it('should cancel previous timeout when value changes', async () => {
    const { result, rerender } = renderHook(
      ({ value }) => useDebounce(value, 500),
      {
        initialProps: { value: 'initial' }
      }
    );

    // Update value multiple times
    rerender({ value: 'update1' });
    act(() => {
      vi.advanceTimersByTime(200);
    });
    
    rerender({ value: 'update2' });
    act(() => {
      vi.advanceTimersByTime(200);
    });
    
    rerender({ value: 'final' });
    
    // Value should still be initial
    expect(result.current).toBe('initial');

    // Complete the final timeout
    await act(async () => {
      vi.advanceTimersByTime(500);
    });

    // Should only have the final value
    expect(result.current).toBe('final');
  });

  it('should cleanup timeout on unmount', () => {
    const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
    
    const { unmount } = renderHook(() => useDebounce('value', 500));
    
    // Unmount the hook
    unmount();
    
    // Cleanup should have been called
    expect(clearTimeoutSpy).toHaveBeenCalled();
  });

  it('should handle delay changes', async () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      {
        initialProps: { value: 'initial', delay: 500 }
      }
    );

    // Change value and delay
    rerender({ value: 'updated', delay: 1000 });
    
    act(() => {
      vi.advanceTimersByTime(500);
    });
    
    // Value should not have changed yet
    expect(result.current).toBe('initial');
    
    await act(async () => {
      vi.advanceTimersByTime(500);
    });
    
    // Now it should be updated
    expect(result.current).toBe('updated');
  });
});
