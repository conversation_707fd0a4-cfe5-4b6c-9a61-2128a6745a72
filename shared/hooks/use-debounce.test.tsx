import { describe, it, expect, vi, afterEach, beforeEach } from 'vitest';
import { render, act } from '@testing-library/react';
import React, { useState } from 'react';
import { useDebounce } from './use-debounce';

// Test component to wrap the hook
function TestComponent({ value, delay }: { value: string; delay: number }) {
  const debouncedValue = useDebounce(value, delay);
  return <div data-testid="debounced-value">{debouncedValue}</div>;
}

describe('useDebounce', () => {
  beforeEach(() => {
    // Ensure timer functions are available
    if (typeof global.setTimeout === 'undefined') {
      global.setTimeout = vi.fn((fn, delay) => {
        const id = Math.random();
        vi.advanceTimersByTime(delay);
        fn();
        return id as any;
      }) as any;
    }
    if (typeof global.clearTimeout === 'undefined') {
      global.clearTimeout = vi.fn() as any;
    }
    
    vi.useFakeTimers({ shouldAdvanceTime: true });
  });

  afterEach(() => {
    vi.clearAllTimers();
    vi.useRealTimers();
  });

  it('should be defined', () => {
    expect(useDebounce).toBeDefined();
    expect(typeof useDebounce).toBe('function');
  });

  it('should return initial value immediately', () => {
    const { getByTestId } = render(<TestComponent value="initial" delay={500} />);
    expect(getByTestId('debounced-value').textContent).toBe('initial');
  });

  it('should debounce value updates', async () => {
    const { getByTestId, rerender } = render(<TestComponent value="initial" delay={500} />);
    
    expect(getByTestId('debounced-value').textContent).toBe('initial');

    // Update the value
    rerender(<TestComponent value="updated" delay={500} />);
    
    // Value should not change immediately
    expect(getByTestId('debounced-value').textContent).toBe('initial');

    // Fast-forward time
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500);
    });

    // Wait for the state update
    expect(getByTestId('debounced-value').textContent).toBe('updated');
  }, 10000);

  it('should cancel previous timeout when value changes', async () => {
    const { getByTestId, rerender } = render(<TestComponent value="initial" delay={500} />);

    // Update value multiple times
    rerender(<TestComponent value="update1" delay={500} />);
    await act(async () => {
      await vi.advanceTimersByTimeAsync(200);
    });
    
    rerender(<TestComponent value="update2" delay={500} />);
    await act(async () => {
      await vi.advanceTimersByTimeAsync(200);
    });
    
    rerender(<TestComponent value="final" delay={500} />);
    
    // Value should still be initial
    expect(getByTestId('debounced-value').textContent).toBe('initial');

    // Complete the final timeout
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500);
    });

    // Should only have the final value
    expect(getByTestId('debounced-value').textContent).toBe('final');
  }, 10000);

  it('should cleanup timeout on unmount', () => {
    const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
    
    const { unmount } = render(<TestComponent value="value" delay={500} />);
    
    // Unmount the component
    unmount();
    
    // Cleanup should have been called
    expect(clearTimeoutSpy).toHaveBeenCalled();
  });

  it('should handle delay changes', async () => {
    const { getByTestId, rerender } = render(<TestComponent value="initial" delay={500} />);

    // Change delay
    rerender(<TestComponent value="updated" delay={1000} />);
    
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500);
    });
    
    // Value should not have changed yet
    expect(getByTestId('debounced-value').textContent).toBe('initial');
    
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500);
    });
    
    // Now it should be updated
    expect(getByTestId('debounced-value').textContent).toBe('updated');
  }, 10000);

  it('should update immediately when value changes back to original', async () => {
    // Component that toggles between values
    function ToggleComponent() {
      const [value, setValue] = useState('initial');
      const debouncedValue = useDebounce(value, 500);
      
      return (
        <div>
          <button onClick={() => setValue(value === 'initial' ? 'changed' : 'initial')}>
            Toggle
          </button>
          <div data-testid="debounced-value">{debouncedValue}</div>
        </div>
      );
    }
    
    const { getByTestId, getByText } = render(<ToggleComponent />);
    
    // Initial value
    expect(getByTestId('debounced-value').textContent).toBe('initial');
    
    // Change value
    act(() => {
      getByText('Toggle').click();
    });
    
    // Advance timer partially
    await act(async () => {
      await vi.advanceTimersByTimeAsync(200);
    });
    
    // Change back to original
    act(() => {
      getByText('Toggle').click();
    });
    
    // Advance timer to complete
    await act(async () => {
      await vi.advanceTimersByTimeAsync(500);
    });
    
    // Should still be initial since we cancelled the change
    expect(getByTestId('debounced-value').textContent).toBe('initial');
  }, 10000);
});
