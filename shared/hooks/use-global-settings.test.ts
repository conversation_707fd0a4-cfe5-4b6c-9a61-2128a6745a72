import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useGlobalSettings, useManyGlobalSettings, globalSettingsKeys } from './use-global-settings';

vi.mock('@tanstack/react-query', () => {
  return {
    useQuery: vi.fn(({ queryKey, queryFn, staleTime, enabled }) => {
    
      useQueryParams = { queryKey, queryFn, staleTime, enabled };
      
      if (!enabled) {
        return {
          data: undefined,
          isLoading: false,
          error: null,
        };
      }
      
    
      if (mockApiResponse) {
        return {
          data: mockApiResponse,
          isLoading: false,
          error: null,
        };
      }
      
    
      if (mockApiError) {
        return {
          data: undefined,
          isLoading: false,
          error: mockApiError,
        };
      }
      
      return {
        data: undefined,
        isLoading: true,
        error: null,
      };
    })
  };
});

let mockApiResponse: any = null;
let mockApiError: Error | null = null;
let useQueryParams: any = null;
let mockApi: any = null;

describe('useGlobalSettings', () => {
  beforeEach(() => {

    mockApiResponse = null;
    mockApiError = null;
    useQueryParams = null;
    

    mockApi = vi.fn().mockImplementation(async (endpoint) => {
      if (mockApiError) {
        throw mockApiError;
      }
      return mockApiResponse;
    });
    
    vi.clearAllMocks();
  });

  it('should exist', () => {
    expect(useGlobalSettings).toBeDefined();
    expect(typeof useGlobalSettings).toBe('function');
  });
  
  it('should call useQuery with correct parameters', () => {
    const keyPath = 'complaints.categories';
    useGlobalSettings(keyPath, mockApi);
    

    expect(useQueryParams.queryKey).toEqual(['globalSettings', keyPath]);
    

    expect(useQueryParams.staleTime).toBe(5 * 60 * 1000);
    

    expect(useQueryParams.enabled).toBe(true);
  });
  
  it('should use custom staleTime when provided', () => {
    const customStaleTime = 10000;
    useGlobalSettings('documents.faq', mockApi, { staleTime: customStaleTime });
    
    expect(useQueryParams.staleTime).toBe(customStaleTime);
  });
  
  it('should use custom enabled flag when provided', () => {
    useGlobalSettings('complaints.categories', mockApi, { enabled: false });
    
    expect(useQueryParams.enabled).toBe(false);
  });
  
  it('should call API with correct endpoint', async () => {
    const keyPath = 'notifications.categories';
    useGlobalSettings(keyPath, mockApi);
    

    await useQueryParams.queryFn();
    

    expect(mockApi).toHaveBeenCalledWith(`/setting/global/${keyPath}`);
  });
  
  it('should return data from API when successful', async () => {

    const mockData = { value: { foo: 'bar' } };
    mockApiResponse = mockData;
    

    const result = useGlobalSettings('documents.pdf', mockApi);
    

    expect(result.data).toEqual(mockData);
    expect(result.isLoading).toBe(false);
    expect(result.error).toBe(null);
  });
  
  it('should return error when API call fails', () => {

    mockApiError = new Error('API Error');
    

    const result = useGlobalSettings('subscriptions.usage.range', mockApi);
    

    expect(result.data).toBeUndefined();
    expect(result.isLoading).toBe(false);
    expect(result.error).toBe(mockApiError);
  });
});

describe('useManyGlobalSettings', () => {
  beforeEach(() => {

    mockApiResponse = null;
    mockApiError = null;
    useQueryParams = null;
    

    mockApi = vi.fn().mockImplementation(async (endpoint) => {
      if (mockApiError) {
        throw mockApiError;
      }
      return { value: `mock-data-for-${endpoint}` };
    });
    
    vi.clearAllMocks();
  });
  
  it('should be defined', () => {
    expect(useManyGlobalSettings).toBeDefined();
    expect(typeof useManyGlobalSettings).toBe('function');
  });
  
  it('should call useQuery with correct parameters', () => {
    const keyPaths = ['complaints.categories', 'documents.faq'];
    useManyGlobalSettings(keyPaths, mockApi);
    

    expect(useQueryParams.queryKey).toEqual(['globalSettings', ...keyPaths]);
    

    expect(useQueryParams.staleTime).toBe(5 * 60 * 1000);
    

    expect(useQueryParams.enabled).toBe(true);
  });
  
  it('should set enabled to false when keyPaths is empty', () => {
    useManyGlobalSettings([], mockApi);
    expect(useQueryParams.enabled).toBe(false);
  });
  
  it('should call API for each keyPath', async () => {
    const keyPaths = ['complaints.categories', 'documents.faq'];
    useManyGlobalSettings(keyPaths, mockApi);
    

    await useQueryParams.queryFn();
    

    expect(mockApi).toHaveBeenCalledTimes(2);
    expect(mockApi).toHaveBeenCalledWith('/setting/global/complaints.categories');
    expect(mockApi).toHaveBeenCalledWith('/setting/global/documents.faq');
  });
});

describe('globalSettingsKeys', () => {
  it('should provide type-safe keys', () => {
    expect(globalSettingsKeys.complaints.categories()).toBe('complaints.categories');
    expect(globalSettingsKeys.documents.faq()).toBe('documents.faq');
    expect(globalSettingsKeys.documents.pdf()).toBe('documents.pdf');
    expect(globalSettingsKeys.notifications.categories()).toBe('notifications.categories');
    expect(globalSettingsKeys.subscriptions.usage.range()).toBe('subscriptions.usage.range');
  });
});
