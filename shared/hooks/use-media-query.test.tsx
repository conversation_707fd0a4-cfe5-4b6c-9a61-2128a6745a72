/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import useMediaQuery from './use-media-query';

describe('useMediaQuery', () => {
  let matchMediaMock: ReturnType<typeof vi.fn>;
  let addEventListenerSpy: ReturnType<typeof vi.fn>;
  let removeEventListenerSpy: ReturnType<typeof vi.fn>;
  let mockMediaQueryList: any;

  beforeEach(() => {
    vi.clearAllMocks();

    addEventListenerSpy = vi.fn();
    removeEventListenerSpy = vi.fn();

    mockMediaQueryList = {
      matches: false,
      media: '',
      addEventListener: addEventListenerSpy,
      removeEventListener: removeEventListenerSpy,
    };

    matchMediaMock = vi.fn().mockImplementation((query) => {
      mockMediaQueryList.media = query;
      return mockMediaQueryList;
    });

    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: matchMediaMock,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should be defined', () => {
    expect(useMediaQuery).toBeDefined();
    expect(typeof useMediaQuery).toBe('function');
  });

  it('should return initial match state', () => {
    mockMediaQueryList.matches = true;
    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    expect(result.current).toBe(true);
    expect(matchMediaMock).toHaveBeenCalledWith('(min-width: 768px)');
  });

  it('should return false when media query does not match', () => {
    mockMediaQueryList.matches = false;
    const { result } = renderHook(() => useMediaQuery('(min-width: 1024px)'));

    expect(result.current).toBe(false);
  });

  it('should add event listener on mount', () => {
    renderHook(() => useMediaQuery('(min-width: 768px)'));

    expect(addEventListenerSpy).toHaveBeenCalledWith('change', expect.any(Function));
  });

  it('should remove event listener on unmount', () => {
    const { unmount } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    unmount();

    expect(removeEventListenerSpy).toHaveBeenCalledWith('change', expect.any(Function));
  });

  it('should update state when media query changes', () => {
    mockMediaQueryList.matches = false;
    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    expect(result.current).toBe(false);

    // Simulate media query change
    act(() => {
      mockMediaQueryList.matches = true;
      const changeHandler = addEventListenerSpy.mock.calls[0][1];
      changeHandler(mockMediaQueryList);
    });

    expect(result.current).toBe(true);
  });

  it('should handle different media queries', () => {
    const queries = [
      '(min-width: 640px)',
      '(min-width: 768px)',
      '(min-width: 1024px)',
      '(max-width: 767px)',
      '(orientation: portrait)',
      '(prefers-color-scheme: dark)',
    ];

    queries.forEach((query) => {
      renderHook(() => useMediaQuery(query));
      expect(matchMediaMock).toHaveBeenCalledWith(query);
    });
  });

  it('should handle query change with new query string', () => {
    const { rerender } = renderHook(
      ({ query }) => useMediaQuery(query),
      { initialProps: { query: '(min-width: 768px)' } }
    );

    expect(matchMediaMock).toHaveBeenCalledWith('(min-width: 768px)');

    // Change the query
    rerender({ query: '(min-width: 1024px)' });

    expect(matchMediaMock).toHaveBeenCalledWith('(min-width: 1024px)');
  });

  it('should cleanup previous listener when query changes', () => {
    const { rerender } = renderHook(
      ({ query }) => useMediaQuery(query),
      { initialProps: { query: '(min-width: 768px)' } }
    );

    expect(addEventListenerSpy).toHaveBeenCalledTimes(1);

    // Change the query
    rerender({ query: '(min-width: 1024px)' });

    expect(removeEventListenerSpy).toHaveBeenCalledTimes(1);
    expect(addEventListenerSpy).toHaveBeenCalledTimes(2);
  });

  it('should handle empty or invalid queries', () => {
    // Test with empty string
    renderHook(() => useMediaQuery(''));
    expect(matchMediaMock).toHaveBeenCalledWith('');

    // Test with invalid query
    renderHook(() => useMediaQuery('invalid-query'));
    expect(matchMediaMock).toHaveBeenCalledWith('invalid-query');
  });

  it('should maintain state across re-renders with same query', () => {
    mockMediaQueryList.matches = true;
    const { result, rerender } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    expect(result.current).toBe(true);

    // Re-render with same query
    rerender();

    expect(result.current).toBe(true);
    // Should not create new event listeners for same query
    expect(addEventListenerSpy).toHaveBeenCalledTimes(1);
  });
});