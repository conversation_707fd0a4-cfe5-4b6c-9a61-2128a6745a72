/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useIsMobile } from './use-mobile';

describe('useIsMobile', () => {
  let matchMediaMock: ReturnType<typeof vi.fn>;
  let addEventListenerSpy: ReturnType<typeof vi.fn>;
  let removeEventListenerSpy: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // Setup mocks for matchMedia and innerWidth
    addEventListenerSpy = vi.fn();
    removeEventListenerSpy = vi.fn();
    
    // Mock innerWidth for mobile detection
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 800, // default to desktop
    });
    
    matchMediaMock = vi.fn().mockImplementation((query) => {
      // Parse the max-width value from the query
      const matches = window.innerWidth < 768;
      return {
        matches,
        media: query,
        addEventListener: addEventListenerSpy,
        removeEventListener: removeEventListenerSpy,
      };
    });
    
    // Replace the global matchMedia implementation
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: matchMediaMock,
    });
  });

  it('should be defined', () => {
    expect(useIsMobile).toBeDefined();
    expect(typeof useIsMobile).toBe('function');
  });
  
  it('should return false for desktop screens', () => {
    Object.defineProperty(window, 'innerWidth', { value: 1024 });
    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(false);
  });
  
  it('should return true for mobile screens', () => {
    Object.defineProperty(window, 'innerWidth', { value: 600 });
    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(true);
  });
  
  it('should add and remove event listeners', () => {
    const { unmount } = renderHook(() => useIsMobile());
    
    // Should add event listener when mounted
    expect(addEventListenerSpy).toHaveBeenCalledWith('change', expect.any(Function));
    
    // Should remove event listener when unmounted
    unmount();
    expect(removeEventListenerSpy).toHaveBeenCalledWith('change', expect.any(Function));
  });
});
