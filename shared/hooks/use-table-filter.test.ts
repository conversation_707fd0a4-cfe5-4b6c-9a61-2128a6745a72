import { describe, it, expect, vi } from 'vitest';
import { useTableFilter } from './use-table-filter';

// Mock React's useState
vi.mock('react', () => {
  return {
    useState: (initialValue: any) => {
      return [initialValue, vi.fn()];
    }
  };
});

describe('useTableFilter', () => {
  it('should be defined', () => {
    expect(useTableFilter).toBeDefined();
    expect(typeof useTableFilter).toBe('function');
  });
  
  it('should return the expected properties', () => {
    const result = useTableFilter();
    
    // Check all returned properties exist
    expect(result).toHaveProperty('searchQuery');
    expect(result).toHaveProperty('setSearchQuery');
    expect(result).toHaveProperty('dateRange');
    expect(result).toHaveProperty('setDateRange');
    expect(result).toHaveProperty('isCalendarOpen');
    expect(result).toHaveProperty('setIsCalendarOpen');
    expect(result).toHaveProperty('isDateSelected');
  });
  
  it('should initialize with default values', () => {
    const { searchQuery, dateRange, isCalendarOpen, isDateSelected } = useTableFilter();
    
    // Check initial values
    expect(searchQuery).toBe('');
    expect(dateRange).toEqual({ from: null, to: null });
    expect(isCalendarOpen).toBe(false);
    expect(isDateSelected).toBe(false);
  });
  
  it('should provide setter functions', () => {
    const { setSearchQuery, setDateRange, setIsCalendarOpen } = useTableFilter();
    
    // Check setter functions are functions
    expect(typeof setSearchQuery).toBe('function');
    expect(typeof setDateRange).toBe('function');
    expect(typeof setIsCalendarOpen).toBe('function');
  });
});
