/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';

// Reset modules before importing to ensure clean state
vi.resetModules();

describe('useToast', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.resetModules();
  });

  it('should initialize with empty toast array', async () => {
    const { useToast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    expect(result.current.toasts).toEqual([]);
    expect(typeof result.current.toast).toBe('function');
    expect(typeof result.current.dismiss).toBe('function');
  });

  it('should add toast and update state', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    act(() => {
      toast({ title: 'Test Toast' });
    });
    
    expect(result.current.toasts).toHaveLength(1);
    expect(result.current.toasts[0].title).toBe('Test Toast');
    expect(result.current.toasts[0].open).toBe(true);
  });

  it('should respect TOAST_LIMIT', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    act(() => {
      toast({ title: 'Toast 1' });
      toast({ title: 'Toast 2' });
    });
    
    // Should only keep 1 toast due to TOAST_LIMIT = 1
    expect(result.current.toasts).toHaveLength(1);
    expect(result.current.toasts[0].title).toBe('Toast 2');
  });

  it('should dismiss specific toast', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    let toastInstance: any;
    act(() => {
      toastInstance = toast({ title: 'Test Toast' });
    });
    
    expect(result.current.toasts[0].open).toBe(true);
    
    act(() => {
      result.current.dismiss(toastInstance.id);
    });
    
    expect(result.current.toasts[0].open).toBe(false);
  });

  it('should dismiss all toasts when no id provided', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    act(() => {
      toast({ title: 'Toast 1' });
    });
    
    expect(result.current.toasts[0].open).toBe(true);
    
    act(() => {
      result.current.dismiss();
    });
    
    expect(result.current.toasts[0].open).toBe(false);
  });

  it('should remove toast after timeout', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    let toastInstance: any;
    act(() => {
      toastInstance = toast({ title: 'Test Toast' });
    });
    
    expect(result.current.toasts).toHaveLength(1);
    
    act(() => {
      toastInstance.dismiss();
    });
    
    expect(result.current.toasts[0].open).toBe(false);
    
    // Fast forward time to trigger removal
    act(() => {
      vi.advanceTimersByTime(1000000);
    });
    
    expect(result.current.toasts).toHaveLength(0);
  });

  it('should update toast properties', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    let toastInstance: any;
    act(() => {
      toastInstance = toast({ title: 'Original Title' });
    });
    
    expect(result.current.toasts[0].title).toBe('Original Title');
    
    act(() => {
      toastInstance.update({ title: 'Updated Title' });
    });
    
    expect(result.current.toasts[0].title).toBe('Updated Title');
  });

  it('should handle onOpenChange callback', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    const onOpenChange = vi.fn();
    
    let toastInstance: any;
    act(() => {
      toastInstance = toast({ 
        title: 'Test Toast',
        onOpenChange 
      });
    });
    
    act(() => {
      result.current.toasts[0].onOpenChange?.(false);
    });
    
    expect(result.current.toasts[0].open).toBe(false);
  });

  it('should handle toast with all properties', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    const mockAction = { type: 'button', props: { children: 'Action' } } as any;
    
    act(() => {
      toast({
        title: 'Complete Toast',
        description: 'Toast description',
        action: mockAction,
        variant: 'destructive',
        className: 'custom-class',
        duration: 5000
      });
    });
    
    expect(result.current.toasts[0]).toMatchObject({
      title: 'Complete Toast',
      description: 'Toast description',
      action: mockAction,
      variant: 'destructive',
      className: 'custom-class',
      duration: 5000,
      open: true
    });
  });

  it('should not add duplicate timeout for same toast', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    const setTimeoutSpy = vi.spyOn(global, 'setTimeout');
    
    let toastInstance: any;
    act(() => {
      toastInstance = toast({ title: 'Test Toast' });
    });
    
    // Dismiss twice
    act(() => {
      toastInstance.dismiss();
      toastInstance.dismiss();
    });
    
    // Should only call setTimeout once for removal
    const removeTimeoutCalls = setTimeoutSpy.mock.calls.filter(call => 
      call[1] === 1000000 // TOAST_REMOVE_DELAY
    );
    expect(removeTimeoutCalls).toHaveLength(1);
  });

  it('should clean up listeners on unmount', async () => {
    const { useToast } = await import('./use-toast');
    
    const { unmount } = renderHook(() => useToast());
    
    // This should not throw an error
    expect(() => unmount()).not.toThrow();
  });

  it('should generate unique toast IDs', async () => {
    const { toast } = await import('./use-toast');
    
    const toast1 = toast({ title: 'Toast 1' });
    const toast2 = toast({ title: 'Toast 2' });
    
    expect(toast1.id).not.toBe(toast2.id);
    expect(toast1.id).toMatch(/^toast-\d+-\d+$/);
    expect(toast2.id).toMatch(/^toast-\d+-\d+$/);
  });

  it('should handle multiple listeners', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result: result1 } = renderHook(() => useToast());
    const { result: result2 } = renderHook(() => useToast());
    
    act(() => {
      toast({ title: 'Shared Toast' });
    });
    
    expect(result1.current.toasts).toHaveLength(1);
    expect(result2.current.toasts).toHaveLength(1);
    expect(result1.current.toasts[0].id).toBe(result2.current.toasts[0].id);
  });

  it('should handle counter overflow in genId', async () => {
    // Access the internal count variable by importing and creating many toasts
    const { toast } = await import('./use-toast');
    
    // Create enough toasts to potentially test counter behavior
    const toasts = [];
    for (let i = 0; i < 5; i++) {
      toasts.push(toast({ title: `Toast ${i}` }));
    }
    
    // All should have unique IDs
    const ids = toasts.map(t => t.id);
    const uniqueIds = new Set(ids);
    expect(uniqueIds.size).toBe(ids.length);
  });

  it('should remove all toasts when toastId is undefined in REMOVE_TOAST', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    const { result } = renderHook(() => useToast());
    
    act(() => {
      toast({ title: 'Toast 1' });
    });
    
    expect(result.current.toasts).toHaveLength(1);
    
    // Dismiss all toasts to trigger removal
    act(() => {
      result.current.dismiss();
    });
    
    expect(result.current.toasts[0].open).toBe(false);
    
    // Fast forward time to trigger removal
    act(() => {
      vi.advanceTimersByTime(1000000);
    });
    
    expect(result.current.toasts).toHaveLength(0);
  });

  it('should handle dismiss all toasts scenario', async () => {
    const { useToast, toast } = await import('./use-toast');
    
    // Reset and create fresh module instance
    vi.resetModules();
    const freshModule = await import('./use-toast');
    
    const { result } = renderHook(() => freshModule.useToast());
    
    // Add a toast first
    act(() => {
      freshModule.toast({ title: 'Toast to dismiss all' });
    });
    
    expect(result.current.toasts).toHaveLength(1);
    expect(result.current.toasts[0].open).toBe(true);
    
    // Dismiss all toasts (no toastId provided)
    act(() => {
      result.current.dismiss();
    });
    
    expect(result.current.toasts[0].open).toBe(false);
    
    // Fast forward to trigger removal
    act(() => {
      vi.advanceTimersByTime(1000000);
    });
    
    expect(result.current.toasts).toHaveLength(0);
  });
});
