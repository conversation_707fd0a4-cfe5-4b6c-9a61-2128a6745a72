import { describe, it, expect, vi, beforeEach } from 'vitest';
import { loginSchema, registerSchema, forgotPasswordSchema, resetPasswordSchema } from './auth';
import * as validation from './validation';

// Mock validateTurkishID function to control test scenarios
vi.mock('./validation', () => ({
  validateTurkishID: vi.fn().mockReturnValue(true),
}));

describe('loginSchema', () => {
  it('should validate a valid login input', () => {
    const validData = {
      email: '<EMAIL>',
      password: 'password123',
    };
    
    const result = loginSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  it('should reject invalid email', () => {
    const invalidData = {
      email: 'not-an-email',
      password: 'password123',
    };
    
    const result = loginSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe('auth:invalid_email');
    }
  });

  it('should reject short password', () => {
    const invalidData = {
      email: '<EMAIL>',
      password: '12345', // less than 6 characters
    };
    
    const result = loginSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe('auth:password_min_length');
    }
  });
});

describe('registerSchema', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should validate a valid registration input', () => {
    const validData = {
      tcNo: '12345678901',
      phone: '5301234567',
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password123',
      acceptTerms: true,
    };
    
    vi.mocked(validation.validateTurkishID).mockReturnValue(true);
    
    const result = registerSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  it('should format phone numbers correctly', () => {
    const validData = {
      tcNo: '12345678901',
      phone: '5301234567',
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password123',
      acceptTerms: true,
    };
    
    vi.mocked(validation.validateTurkishID).mockReturnValue(true);
    
    const result = registerSchema.safeParse(validData);
    expect(result.success).toBe(true);
    
    if (result.success) {
      expect(result.data.phone).toBe('530 123 45 67');
    }
  });

  it('should reject invalid TC No', () => {
    const invalidData = {
      tcNo: '12345678901',
      phone: '5301234567',
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password123',
      acceptTerms: true,
    };
    
    vi.mocked(validation.validateTurkishID).mockReturnValue(false);
    
    const result = registerSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
  });

  it('should reject when passwords do not match', () => {
    const invalidData = {
      tcNo: '12345678901',
      phone: '5301234567',
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password456',
      acceptTerms: true,
    };
    
    vi.mocked(validation.validateTurkishID).mockReturnValue(true);
    
    const result = registerSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      const passwordMatchError = result.error.issues.find(
        (issue) => issue.path.includes('confirmPassword')
      );
      expect(passwordMatchError?.message).toBe('auth:passwords_not_match');
    }
  });
});

describe('forgotPasswordSchema', () => {
  it('should validate valid email', () => {
    const validData = {
      email: '<EMAIL>',
    };
    
    const result = forgotPasswordSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  it('should reject invalid email', () => {
    const invalidData = {
      email: 'not-an-email',
    };
    
    const result = forgotPasswordSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
  });
});

describe('resetPasswordSchema', () => {
  it('should validate when passwords match and meet requirements', () => {
    const validData = {
      password: 'Password123!',
      confirmPassword: 'Password123!',
    };
    
    const result = resetPasswordSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  it('should reject password that does not meet requirements', () => {
    // Looking at the regex: /^(?=.*[a-z])(?=.*[A-Z!@#$%^&*(),.?":{}|<>])(?=.*\d).{8,}$/
    // The regex needs lowercase, uppercase OR special character, and a number
    
    // Test case 1: Without lowercase (should fail)
    const invalidData1 = {
      password: 'PASSWORD123!',
      confirmPassword: 'PASSWORD123!',
    };
    
    const result1 = resetPasswordSchema.safeParse(invalidData1);
    expect(result1.success).toBe(false);

    // Test case 2: Without uppercase or special characters (should fail)
    const invalidData2 = {
      password: 'password123',
      confirmPassword: 'password123',
    };
    
    const result2 = resetPasswordSchema.safeParse(invalidData2);
    expect(result2.success).toBe(false);

    // Test case 3: Without number (should fail)
    const invalidData3 = {
      password: 'Password!',
      confirmPassword: 'Password!',
    };
    
    const result3 = resetPasswordSchema.safeParse(invalidData3);
    expect(result3.success).toBe(false);

    // Test case 4: Too short (should fail)
    const invalidData4 = {
      password: 'Pas1!',
      confirmPassword: 'Pas1!',
    };
    
    const result4 = resetPasswordSchema.safeParse(invalidData4);
    expect(result4.success).toBe(false);
  });

  it('should reject when passwords do not match', () => {
    const invalidData = {
      password: 'Password123!',
      confirmPassword: 'Password456!',
    };
    
    const result = resetPasswordSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
  });
});
