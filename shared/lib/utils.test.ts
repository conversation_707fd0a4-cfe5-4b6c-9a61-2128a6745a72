import { describe, it, expect } from 'vitest';
import { cn, buildQuery } from './utils';

describe('cn', () => {
  it('should be defined', () => {
    expect(cn).toBeDefined();
  });

  it('should merge class names correctly', () => {
    expect(cn('test')).toBe('test');
    expect(cn('test1', 'test2')).toBe('test1 test2');
    expect(cn('test1', { test2: true, test3: false })).toBe('test1 test2');
    expect(cn('test1', ['test2', 'test3'])).toBe('test1 test2 test3');
  });

  it('should handle conditional classes', () => {
    const isActive = true;
    const isDisabled = false;
    
    expect(cn('base', { active: isActive, disabled: isDisabled }))
      .toBe('base active');
  });

  it('should merge tailwind classes correctly', () => {
    expect(cn('px-4 py-2', 'bg-blue-500', 'text-white')).toBe('px-4 py-2 bg-blue-500 text-white');
    expect(cn('px-4', 'px-6')).toBe('px-6'); // tailwind-merge de-duplicates properties
  });
});

describe('buildQuery', () => {
  it('should be defined', () => {
    expect(buildQuery).toBeDefined();
  });

  it('should build query string from params object', () => {
    expect(buildQuery({ a: 1, b: 'test' })).toBe('?a=1&b=test');
  });

  it('should handle empty object', () => {
    expect(buildQuery({})).toBe('');
  });

  it('should filter out null and undefined values', () => {
    expect(buildQuery({ a: 1, b: null, c: undefined, d: 'test' })).toBe('?a=1&d=test');
  });

  it('should properly encode special characters', () => {
    expect(buildQuery({ a: 'test=1', b: 'hello world' })).toBe('?a=test%3D1&b=hello%20world');
  });
});
