{"subsTypeProduction": "Electricity Production", "subsTypeConsumption": "Electricity Consumption", "subsKindIndividual": "Individual", "subsKindCorporate": "Corporate", "subsEmptyTitle": "No Subscriptions Found.", "subsEmptyDescription": "There are no subscriptions yet. Add a new subscription to view your data.", "subsFilterEmptyTitle": "No subscriptions found matching filters", "subsFilterEmptyDescription": "There are no subscriptions matching your selected filters. Please try again by changing the filters.", "subsEmptyButton": "Add new subscription", "subsClearFilterButton": "Clear Filters", "subsAddButton": "Add new", "subsProduction": "Production", "subsConsumption": "Consumption", "subsActionsDescription": "You can edit your subscription information.", "subsActionsEdit": "Edit", "subsActionsRemove": "Delete Subscription", "subsEditTitle": "Edit subscription name", "subsEditDescription": "Enter a new name for the subscription.", "subsEditLabel": "Subscription name", "subsEditHint": "Home", "subsEditButton": "Change", "subsEditSuccessTitle": "Subscription name updated", "subsEditSuccessDescription": "Success! Subscription name has been successfully updated.", "subsEditErrorTitle": "Subscription name could not be updated", "subsEditErrorDescription": "A problem occurred! Please check your internet connection and try again.", "subsRemoveTitle": "Delete subscription?", "subsRemoveDescription": "When you confirm this action, your subscription will be permanently deleted and your access to related services will end.", "subsRemoveButton": "Delete Subscription", "subsRemoveSuccessTitle": "Subscription deleted", "subsRemoveSuccessDescription": "Success! Your subscription has been successfully deleted.", "subsRemoveErrorTitle": "Subscription could not be deleted", "subsRemoveErrorescription": "A problem occurred! Please check your internet connection and try again.", "subsCreateTitle": "Add subscription", "subsCreateButton": "Add subscription", "subsCreateRegionTitle": "Distribution Company", "subsCreateRegionHint": "Distribution Company 1", "subsCreateInstallationIdTitle": "Installation ID", "subsCreateInstallationIdHint": "Ex. J2HABDE75R", "subsCreateKindTitle": "Subscription Type", "subsCreateKindIndividual": "Individual", "subsCreateKindCorporate": "Corporate", "subsCreateTaxIdTitle": "Tax ID Number", "subsCreateTaxIdHint": "Ex. 3334455566", "subsCreateNameTitle": "Subscription Name", "subsCreateNameHint": "Home", "subsCreateSuccessTitle": "Subscription successfully added", "subsCreateSuccessDescription": "Success! You can now easily track your energy consumption.", "subsCreateErrorTitle": "Subscription could not be added", "subsCreateErrorDescription": "A problem occurred! Please check your internet connection and try again.", "title": "Subscriptions", "subscriptions": "Subscriptions", "description": "Manage your subscriptions and facilities efficiently.", "list": "List", "no_subscription_title": "No Subscription Found.", "no_subscription_filter_title": "No Subscription Found", "no_facility_title": "No Facility Found.", "no_subscription_description": "You don't have any subscriptions yet. Add a new subscription to view your data.", "no_subscription_filter_description": "There is no subscription for the filters you choose. Please try again by changing the filters.", "no_facility_description": "You don't have any facilities yet. Add a new facility to view your data.", "add_subscription": "Add new subscription", "add_facility": "Add new facility", "facility_name": "Facility Name", "subscription_name": "Subscription Name", "subscription_name_placeholder": "E.g. Home", "tax_number_placeholder": "E.g. 45654345", "distribution_company_placeholder": "Search and select distribution company", "installation_id_placeholder": "E.g. 123456", "subscription_info": "Subscription Information", "subscription_type": "Subscription Type", "subsDetailTabDetail": "Subscription Details", "subsDetailTabUsage": "Data Query", "subsDetailTabOutage": "Outage and Compensation Information", "subsDetailTabSettings": "Notification Settings", "subsDetailNameTitle": "Subscription Name", "subsDetailRegionTitle": "Distribution Company", "subsDetailAddressTitle": "Address", "subsDetailAddressErrorTitle": "Subscription details could not be loaded", "subsDetailAddressErrorDescription": "A problem occurred! Please check your internet connection and try again.", "subsDetailInstallationIdTitle": "Installation ID", "subsDetailTypeTitle": "Facility Type", "subsDetailConsumerInfoTitle": "Consumer Information", "subsDetailKindTitle": "Subscription Type", "subsDetailKindIndividual": "Individual", "subsDetailKindeCorporate": "Corporate", "subsDetailTCKNTitle": "National ID No", "subsDetailVKNTitle": "Tax ID No", "subsDetailStartDateTitle": "Subscription Start Date", "subsDetailNotifThresholdTitle": "Unexpected Excessive Consumption", "subsDetailNotifThresholdDescription": "You can adjust your excessive consumption limit.", "subsDetailUserLimitTitle": "User Defined Limit", "subsDetailUserLimitDescription": "You can adjust your user limit.", "subsDetailGranularityTitle": "Period Type", "subsDetailDateTitle": "Date", "subsDetailDateHint": "Select date", "subsDetailCompareTitle": "Analysis Type", "subsDetailClear": "Clear", "subsDetailSubmit": "Query", "subsDetailExport": "Export", "subsOutageExportSuccessTitle": "Outage data exported", "subsOutageExportSuccessDescription": "Success! Your outage data has been saved to the selected location.", "subsOutageExportErrorTitle": "Outage data could not be exported", "subsOutageExportErrorDescription": "A problem occurred! Please check your internet connection and try again.", "subscription_type_individual": "Individual Person", "subscription_type_description": "Is this an individual account", "subscription_type_corporate": "Corporate Entity", "distribution_company": "Distribution Company", "tax_number": "TIN/Tax Identification Number", "facilities": "Facilities", "facility_type": "Facility Type", "update_subscription": "Update subscription info", "edit_facility": "Edit Facility", "edit_facility_description": "You can update facility details here.", "edit_subscription": "Edit Subscription", "edit_description": "You can update subscription information.", "application_date": "Subscription Start Date", "application_category": "Application Category", "application_description": "Application Description", "application_description_hint": "Enter the application description you want to convey to the distribution company that will not exceed 2 thousand characters.", "file": "File", "subscription_detail": "Subscription Details", "invoice_no": "Invoice Serial No", "contract_no": "Contract Account No", "installation_type": "Installation Type", "installation_no": "Installation No", "installation_number": "Installation Number", "consumer_info": "CONSUMER INFORMATION", "full_name": "Full Name", "identity_no": "ID No/TIN", "national_or_tax_id": "ID Number / Tax ID", "address": "Address", "consumer_type": "Consumer Group/Class", "phone": "Contact", "reading_info": "Reading Information", "reading_day": "Reading Day", "first_index": "First Index", "last_index": "Last Index", "difference": "Difference", "days": "days", "data_query": "Data Query", "type_label": "Type", "individual": "Individual", "start_date": "Start Date", "created_at": "Created At", "manage_subscriptions": "You can manage your subscriptions here.", "manage_facilities": "You can manage your facilities here.", "subscriptions_count": "{{count}} subscriptions available", "facilities_count": "{{count}} facilities available", "installation_id": "Installation No", "subscription": "Subscription", "select_subscription": "Select Subscription", "subscription_name_required": "Subscription name is required.", "subscription_type_required": "Subscription type is required.", "tax_number_required": "Tax number is required.", "tax_number_digits_only": "Tax Number must contain only digits.", "distribution_company_required": "Distribution company is required.", "installation_id_required": "Installation number is required.", "production": "Production", "subscription_start_date": "Subscription Start Date", "subscription_region": "Subscription Region", "tckn": "National ID", "vkn": "Tax ID", "electricity_consumption": "Electricity Consumption", "electricity_production": "Production", "facility_detail": "Facility Details", "notification_settings": "Notification Settings", "notifications_title": "Notifications", "notifications_description": "You can manage notification settings about your subscription here.", "warning_notifs": "Warning Notifications", "notif_notifs": "Announcement Notifications", "warning_threshold": "Unexpected Excessive Consumption Warning Threshold Value", "kwh_warning_threshold": "User Defined Limit", "pick_threshold": "Select Threshold Value", "usage": {"pick_date": "Select date", "data_title": "Consumption Data", "query_title": "Consumption Query", "query_start_date_error": "Please select a start date", "query_end_date_error": "Please select an end date", "query_end_date_prev_error": "End date should be after start date", "query_clear_button": "Clear", "query_export_button": "Export", "query_apply_button": "Apply", "daterange_title": "Date Range", "daterange_desc": "Select the time period for which you want to view the data", "daterange_label": "Range Type", "range_placeholder": "Select range type", "range_hour": "Hourly", "range_day": "Daily", "range_month": "Monthly", "range_year": "Yearly", "range_month_placeholder": "Select date", "day": "Day", "start_date": "Start Date", "end_date": "End Date", "max_data_disclaimer": {"hour": "Max {{maxRange}} hours of data can be displayed", "day": "Max {{maxRange}} days of data can be displayed", "month": "Max {{maxRange}} months of data can be displayed", "year": "Max {{maxRange}} years of data can be displayed"}, "compare_title": "Compare", "compare_desc": "Compare your usage with similar consumers", "compare_label": "Compare With", "compare_none": "No comparison", "compare_similar_district": "Similar consumers in district", "usage_compare_similar_city": "Similar consumers in city", "table_date": "Date", "usage_chart_label": "Usage", "compare_chart_label": "Comparison", "chart_type_line": "Line Chart", "chart_type_bar": "Bar Chart", "chart_type_table": "Table View", "no_usage_data_title": "No Usage Data Found", "no_usage_data_description": "There is no usage data for the selected period", "query_usage_button": "Query Usage Data", "compare_placeholder": "Select compare type", "query_invalid_date_error": "Please select a valid date", "compare_average": "Average", "compare": {"query": {"hour": "Data from the past {{range}} hours", "day": "Data from the past {{range}} days", "month": "Data from the past {{range}} months", "year": "Data from the past {{range}} years"}, "usage": "Consumption data", "average": "Average data", "similar-consumer-district": "Similar consumers in the district", "similar-consumer-city": "Similar consumers in the city"}, "single_time": "Single-Time Breakdown", "three_time": "Multi-Time Breakdown", "data_type": "Data Type", "export": {"title": "Export Usage Data", "filetype": "File Type", "hourly": {"label": "Hourly Breakdown", "enabled": "Enabled", "disabled": "Disabled"}, "year": "Year"}}, "no_subscriptions_found": "No subscriptions found", "search_subscriptions": "Search subscriptions...", "error": {"invalid": {"name": "Invalid subscription name", "installationId": "Invalid installation ID", "tckn": "Invalid national ID", "vkn": "Invalid tax ID"}, "exists": {"name": "Subscription name already exists", "installationId": "Installation number already exists"}}, "all": "All", "filter": "Filter", "apply": "Apply", "search_subscription": "Search Subscriptions...", "no_subscription_found": "No subscription found", "delete_confirmation": "Are you sure you want to delete this subscription?", "delete_description": "This action cannot be undone.", "outages": "Outages & Compensation Information", "notification_settings_saved": "Notification settings saved successfully.", "download_loading": "Downloading...", "download_failed": "An error occurred while downloading the file.", "download_successful": "The file has been downloaded successfully.", "subsUsageTitle": "Usage Data", "subsTabSingle": "Single Time Breakdown", "subsTabMultiple": "Multiple Time Breakdown", "subsUsageTooltip": "Usage data", "subsUsageTooltipAverage": "Average data", "subsUsageTooltipCity": "Similar consumers in city", "subsUsageTooltipDistrict": "Similar consumers in district", "subsUsageExport": "Export", "subsUsageExportSuccessTitle": "Usage data exported", "subsUsageExportSuccessDescription": "Success! Your usage data has been saved to the selected location.", "subsUsageExportErrorTitle": "Usage data could not be exported", "subsUsageExportErrorDescription": "A problem occurred! Please check your internet connection and try again.", "exportOptionsTitle": "Export Data", "exportOptionsDescription": "Select a format to export your usage data.", "exportOptionsTypeTitle": "File Type", "exportOptionsHourTitle": "Hourly Breakdown", "exportOptionsHourEnabled": "Enabled", "exportOptionsHourDisabled": "Disabled", "exportOptionsSubmit": "Export", "subsFiltersTitle": "Subscription Filters", "subsFiltersSelectButton": "Apply Selection", "subsFiltersClear": "Clear", "subsFiltersRegionTitle": "Distribution Company", "subsFiltersTypeTitle": "Facility Type", "subsFiltersKindTitle": "Subscription Type", "subsFiltersEmptyTitle": "Filters are empty", "subsFiltersEmptyDescription": "No suitable filter found.", "subsRegionsTitle": "Select Distribution Company", "subsRegionsSearchHint": "Search...", "subsRegionsSelectButton": "Confirm Selection", "subsRegionsEmptyTitle": "No distribution company found", "subsRegionsEmptyDescription": "No distribution company found matching your search criteria. Please try a different search.", "notifThresholdEditTitle": "Unexpected Excessive Consumption", "notifThresholdEditDescription": "You can adjust your excessive consumption limit.", "notifThresholdEditButton": "Save", "userLimitEditTitle": "User Defined Limit", "userLimitEditDescription": "You can adjust your user limit.", "userLimitEditHint": "140 kWh", "userLimitEditLabel": "User limit", "userLimitEditButton": "Save", "usageGranularityDay": "Daily", "usageGranularityMonth": "Monthly", "usageGranularityYear": "Yearly", "usageCompareTypePast": "Past Comparison (Last {duration} {granularity})", "usageCompareTypeAverage": "Average Consumption", "usageCompareTypeCity": "Similar consumers in the city", "usageCompareTypeDistrict": "Similar consumers in the district", "characterLimitInstIdTitle": "Installation Id Number Character Limit", "characterLimitInstIdDescription": "Installation Id number must be at least {min} characters", "characterLimitTaxIdTitle": "Tax Id Character Limit", "characterLimitTaxIdDescription": "Tax Id must be {min} characters"}