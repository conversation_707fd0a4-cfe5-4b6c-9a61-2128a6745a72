{"name": "@mass/shared", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/types/index.d.ts"}, "./assets/*": "./dist/assets/*", "./components/*": {"import": "./dist/components/*.js", "types": "./dist/types/components/*.d.ts"}, "./components/atoms/pin-input": {"import": "./dist/components/atoms/pin-input.js", "types": "./dist/types/components/atoms/pin-input.d.ts"}, "./components/atoms/skip-to-main": {"import": "./dist/components/atoms/skip-to-main.js", "types": "./dist/types/components/atoms/skip-to-main.d.ts"}, "./utils/*": {"import": "./dist/utils/*.js", "types": "./dist/types/utils/*.d.ts"}, "./config/*": {"import": "./dist/config/*.js", "types": "./dist/types/config/*.d.ts"}, "./config/tailwind": "./config/tailwind.ts", "./locales/*": "./dist/locales/*", "./*": {"import": "./dist/*.js", "types": "./dist/types/*.d.ts"}}, "scripts": {"build": "vite build", "dev": "vite build --watch", "lint": "eslint src/", "clean": "rm -rf dist", "generate:icons": "pnpm dlx iconify-transpile", "test": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@iconify/react": "^5.2.0", "@iconify/tools": "^4.0.7", "@iconify/types": "^2.0.0", "@mdx-js/react": "^3.1.0", "@mdx-js/rollup": "^3.1.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dismissable-layer": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.4", "@radix-ui/react-visually-hidden": "^1.1.0", "@tabler/icons-react": "^3.24.0", "@tanstack/history": "^1.85.3", "@tanstack/react-query": "^5.62.3", "@tanstack/react-router": "^1.86.1", "@tanstack/react-table": "^8.21.2", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "i18next": "^24.2.2", "js-cookie": "^3.0.5", "lucide-react": "^0.468.0", "mime-types": "^3.0.1", "motion": "^12.4.10", "nanoid": "^5.1.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.0", "react-i18next": "^15.4.1", "recharts": "^2.14.1", "sonner": "^2.0.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@testing-library/user-event": "14.5.1", "@types/glob": "^8.1.0", "@types/mime-types": "^2.1.4", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "3.1.4", "glob": "^10.3.10", "tsup": "^8.0.0", "typescript": "~5.7.2", "vite": "^6.0.9", "vite-plugin-dts": "^4.5.3"}, "overrides": {"@radix-ui/react-focus-scope": "1.1.1"}}