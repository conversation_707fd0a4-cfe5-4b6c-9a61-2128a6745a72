import { vi, beforeEach } from 'vitest';

// Add custom matchers that mimic @testing-library/jest-dom
beforeEach(() => {
  expect.extend({
    toBeInTheDocument(received) {
      const pass = Boolean(received);
      return {
        pass,
        message: () => `expected ${received} to be in the document`,
      };
    },
    
    toHaveAttribute(received, name, value) {
      const hasAttribute = received.hasAttribute(name);
      const attributeValue = received.getAttribute(name);
      
      const pass = value === undefined
        ? hasAttribute
        : hasAttribute && attributeValue === value;
        
      return {
        pass,
        message: () => {
          if (value === undefined) {
            return `expected ${received} to have attribute "${name}"`;
          }
          return `expected ${received} to have attribute "${name}" with value "${value}"`;
        },
      };
    },
    
    toHaveClass(received, className) {
      const classList = received?.className?.split(' ') || [];
      const pass = classList.includes(className);
      
      return {
        pass,
        message: () => `expected ${received} to have class "${className}"`,
      };
    },
    
    toHaveTextContent(received, text) {
      const pass = received?.textContent?.includes(text);
      return {
        pass,
        message: () => `expected ${received} to have text content "${text}"`,
      };
    },
    
    toContain(received, item) {
      const pass = received?.includes(item);
      return {
        pass,
        message: () => `expected ${received} to contain "${item}"`,
      };
    },
    
    toBeDisabled(received) {
      const pass = received.disabled === true;
      return {
        pass,
        message: () => `expected ${received} to be disabled`,
      };
    },
    
    toHaveFocus(received) {
      const pass = document.activeElement === received;
      return {
        pass,
        message: () => `expected ${received} to have focus`,
      };
    },
    
    toHaveValue(received, value) {
      const pass = received.value === value;
      return {
        pass,
        message: () => `expected ${received} to have value "${value}"`,
      };
    }
  });
});

// Mock window.matchMedia
beforeEach(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
});