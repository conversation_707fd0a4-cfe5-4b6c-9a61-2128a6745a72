// Simple test setup file for vitest
import { vi, expect, beforeEach, afterEach } from 'vitest';
import '@testing-library/jest-dom';

// Make beforeEach and afterEach globally available
interface CustomGlobal {
  beforeEach: typeof beforeEach;
  afterEach: typeof afterEach;
}

// Cast globalThis to include our custom functions
(globalThis as unknown as CustomGlobal).beforeEach = beforeEach;
(globalThis as unknown as CustomGlobal).afterEach = afterEach;

// Mock window object for browser APIs
global.window = global.window || {
  matchMedia: vi.fn(() => ({
    matches: false,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
  })),
  localStorage: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn()
  },
  innerWidth: 1024
};

// Setup mocks for i18n
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn()
    }
  })
}));
