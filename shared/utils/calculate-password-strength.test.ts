import { describe, it, expect } from 'vitest';
import { calculatePasswordStrength } from './calculate-password-strength';

describe('calculatePasswordStrength', () => {
  it('should return 0 for empty passwords', () => {
    expect(calculatePasswordStrength('')).toBe(0);
  });

  it('should award points for length criteria', () => {
    expect(calculatePasswordStrength('1234567')).toBe(25); // Too short but has numbers
    expect(calculatePasswordStrength('12345678')).toBe(50); // Meets length requirement + numbers
    expect(calculatePasswordStrength('123456789')).toBe(50); // Also meets length requirement + numbers
  });

  it('should award points for containing numbers', () => {
    expect(calculatePasswordStrength('abcdefgh')).toBe(50); // Length + lowercase
    expect(calculatePasswordStrength('abcdefg1')).toBe(75); // Length + lowercase + number
  });

  it('should award points for containing lowercase characters', () => {
    expect(calculatePasswordStrength('12345678')).toBe(50); // Length + numbers
    expect(calculatePasswordStrength('1234567a')).toBe(75); // Not long enough but has numbers + lowercase
  });

  it('should award points for containing uppercase characters or special characters', () => {
    expect(calculatePasswordStrength('abcdefgh')).toBe(50); // Length + lowercase
    expect(calculatePasswordStrength('abcdefgA')).toBe(75); // Length + lowercase + uppercase
    expect(calculatePasswordStrength('abcdefg!')).toBe(75); // Length + lowercase + special
  });

  it('should return maximum score for strong passwords', () => {
    expect(calculatePasswordStrength('Abcdefg1')).toBe(100); // All criteria met
    expect(calculatePasswordStrength('P@ssw0rd')).toBe(100); // All criteria met
  });

  it('should correctly calculate scores for various password combinations', () => {
    expect(calculatePasswordStrength('123')).toBe(25); // Number only
    expect(calculatePasswordStrength('12345678')).toBe(50); // Length + numbers
    expect(calculatePasswordStrength('abcdefgh')).toBe(50); // Length + lowercase
    expect(calculatePasswordStrength('12345678a')).toBe(75); // Length + lowercase + numbers
    expect(calculatePasswordStrength('12345678A')).toBe(75); // Length + uppercase + numbers
    expect(calculatePasswordStrength('abcdefg1')).toBe(75); // Length + lowercase + number
    expect(calculatePasswordStrength('abcdefgA')).toBe(75); // Length + lowercase + uppercase
    expect(calculatePasswordStrength('abcdef1!')).toBe(100); // Length + lowercase + number + special
    expect(calculatePasswordStrength('abcD123!')).toBe(100); // All criteria met
  });
});
