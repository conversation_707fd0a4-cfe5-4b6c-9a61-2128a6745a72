// filepath: /Users/<USER>/Desktop/development/frontend/shared/utils/validators.test.ts
import { describe, it, expect } from 'vitest';
import { isValidTCKN, isValidVKN } from './validators';

describe('Validators', () => {
  describe('isValidTCKN', () => {
    it('should return false for empty string', () => {
      expect(isValidTCKN('')).toBe(false);
    });

    it('should return false for string with incorrect length', () => {
      expect(isValidTCKN('1234567890')).toBe(false); // 10 digits
      expect(isValidTCKN('123456789012')).toBe(false); // 12 digits
    });

    it('should return false for non-numeric strings', () => {
      expect(isValidTCKN('1234567890a')).toBe(false);
      expect(isValidTCKN('abcdefghijk')).toBe(false);
    });

    it('should return false for TCKNs starting with 0', () => {
      expect(isValidTCKN('01234567890')).toBe(false);
    });

    it('should return false for invalid TCKNs that fail checksum', () => {
      // This is a made-up invalid TCKN
      expect(isValidTCKN('12345678901')).toBe(false);
    });

    // Add more comprehensive tests for valid TCKNs with known checksums
    it('should return true for a valid TCKN (using a known valid pattern)', () => {
      // This is a test pattern that follows the algorithm
      // First 9 digits: 123456782
      // 10th digit: (((1+3+5+7+2) * 7) - (2+4+6+8)) % 10 = (18*7 - 20) % 10 = 106 % 10 = 6
      // 11th digit: (1+2+3+4+5+6+7+8+2+6) % 10 = 44 % 10 = 4
      expect(isValidTCKN('12345678264')).toBe(true);
    });
  });

  describe('isValidVKN', () => {
    it('should return false for empty string', () => {
      expect(isValidVKN('')).toBe(false);
    });

    it('should return false for string with incorrect length', () => {
      expect(isValidVKN('123456789')).toBe(false); // 9 digits
      expect(isValidVKN('12345678901')).toBe(false); // 11 digits
    });

    it('should return false for non-numeric strings', () => {
      expect(isValidVKN('123456789a')).toBe(false);
      expect(isValidVKN('abcdefghij')).toBe(false);
    });

    // Test a valid VKN pattern
    it('should return true for a valid VKN with correct check digit', () => {
      // Based on VKN algorithm: test pattern that should be valid
      // Using a known valid pattern or algorithm implementation
      expect(isValidVKN('1234567890')).toBe(true);
    });
  });
});
