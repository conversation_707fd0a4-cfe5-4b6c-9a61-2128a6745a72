import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleServerError } from './handle-server-error';
import { AxiosError } from 'axios';

// Mock the toast function
vi.mock('../hooks/use-toast', () => ({
  toast: vi.fn()
}));

// Import the mocked module
import { toast } from '../hooks/use-toast';

describe('handleServerError', () => {
  // Reset mock before each test
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should be defined', () => {
    expect(handleServerError).toBeDefined();
    expect(typeof handleServerError).toBe('function');
  });

  it('should display default error message for generic errors', () => {
    // Call with a generic error
    handleServerError(new Error('Some error'));

    // Verify toast was called with default message
    expect(toast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Something went wrong!'
    });
  });

  it('should handle 204 status specially', () => {
    // Create an object with status 204
    const error = { status: 204 };

    // Call with the error
    handleServerError(error);

    // Verify toast was called with content not found message
    expect(toast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Content not found.'
    });
  });

  it('should extract message from Axios errors', () => {
    // Create a mock AxiosError with instanceof behavior
    const axiosError = new AxiosError();
    Object.defineProperty(axiosError, 'response', {
      value: {
        data: {
          title: 'Backend validation failed'
        }
      }
    });

    // Call with the Axios error
    handleServerError(axiosError);

    // Verify toast was called with the Axios error message
    expect(toast).toHaveBeenCalledWith({
      variant: 'destructive',
      title: 'Backend validation failed'
    });
  });
});
