import { describe, it, expect } from 'vitest';
import { calculatePasswordStrength } from './calculate-password-strength';
import { isValidTCKN, isValidVKN, tcknSchema, vknSchema } from './validators';

describe('Validators', () => {
  describe('calculatePasswordStrength', () => {
    it('should return 0 for empty passwords', () => {
      expect(calculatePasswordStrength('')).toBe(0);
    });
    
    it('should return higher score for complex passwords', () => {
      const weakPassword = 'password';
      const strongPassword = 'P@ssw0rd!123';
      
      const weakScore = calculatePasswordStrength(weakPassword);
      const strongScore = calculatePasswordStrength(strongPassword);
      
      expect(strongScore).toBeGreaterThan(weakScore);
    });
    
    it('should correctly calculate password strength based on criteria', () => {
      // Test length requirement
      expect(calculatePasswordStrength('short')).toBe(25);
      expect(calculatePasswordStrength('longenoughpassword')).toBe(50);
      
      // Test number requirement
      expect(calculatePasswordStrength('password1')).toBe(75);
      
      // Test lowercase requirement
      expect(calculatePasswordStrength('12345678')).toBe(50); // Length + numbers
      expect(calculatePasswordStrength('abcdefgh')).toBe(50); // Length + lowercase
      
      // Test uppercase/special character requirement
      expect(calculatePasswordStrength('Password')).toBe(75); // Length + lowercase + uppercase
      expect(calculatePasswordStrength('password!')).toBe(75); // Length + lowercase + special
      
      // Test all criteria
      expect(calculatePasswordStrength('Password1')).toBe(100); // All criteria satisfied
      expect(calculatePasswordStrength('p@ssw0rd')).toBe(100); // All criteria satisfied
    });
  });
  
  describe('isValidTCKN', () => {
    it('should return false for empty string', () => {
      expect(isValidTCKN('')).toBe(false);
    });

    it('should return false for string with incorrect length', () => {
      expect(isValidTCKN('1234567890')).toBe(false); // 10 digits
      expect(isValidTCKN('123456789012')).toBe(false); // 12 digits
    });

    it('should return false for non-numeric strings', () => {
      expect(isValidTCKN('1234567890a')).toBe(false);
      expect(isValidTCKN('123456-7890')).toBe(false);
    });

    it('should return false for TCKNs starting with 0', () => {
      expect(isValidTCKN('01234567890')).toBe(false);
    });

    it('should return false for invalid TCKNs that fail checksum', () => {
      expect(isValidTCKN('11111111111')).toBe(false);
      expect(isValidTCKN('12345678901')).toBe(false);
    });

    it('should return false for TCKNs that fail 10th digit validation', () => {
      expect(isValidTCKN('12345678900')).toBe(false);
    });

    it('should return false for TCKNs that fail 11th digit validation', () => {
      expect(isValidTCKN('12345678902')).toBe(false);
    });

    it('should return true for valid TCKNs', () => {
      // Valid TCKN examples that pass all validation rules
      expect(isValidTCKN('10000000146')).toBe(true);
      expect(isValidTCKN('11111111110')).toBe(true);
    });

    it('should handle null and undefined inputs', () => {
      expect(isValidTCKN(null as any)).toBe(false);
      expect(isValidTCKN(undefined as any)).toBe(false);
    });
  });

  describe('isValidVKN', () => {
    it('should return false for empty string', () => {
      expect(isValidVKN('')).toBe(false);
    });

    it('should return false for string with incorrect length', () => {
      expect(isValidVKN('123456789')).toBe(false); // 9 digits
      expect(isValidVKN('12345678901')).toBe(false); // 11 digits
    });

    it('should return false for non-numeric strings', () => {
      expect(isValidVKN('123456789A')).toBe(false);
      expect(isValidVKN('123-456789')).toBe(false);
    });

    it('should return true for valid 10-digit numeric strings', () => {
      expect(isValidVKN('1234567890')).toBe(true);
      expect(isValidVKN('0123456789')).toBe(true);
      expect(isValidVKN('9876543210')).toBe(true);
    });

    it('should handle null and undefined inputs', () => {
      expect(isValidVKN(null as any)).toBe(false);
      expect(isValidVKN(undefined as any)).toBe(false);
    });

    it('should return false for strings with special characters', () => {
      expect(isValidVKN('123.456789')).toBe(false);
      expect(isValidVKN('123 456789')).toBe(false);
      expect(isValidVKN('123+456789')).toBe(false);
    });
  });

  describe('tcknSchema', () => {
    it('should pass validation for valid TCKN', () => {
      const result = tcknSchema.safeParse('10000000146');
      expect(result.success).toBe(true);
    });

    it('should fail validation for TCKN too short', () => {
      const result = tcknSchema.safeParse('1234567890');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('errors:tckn_too_short');
      }
    });

    it('should fail validation for TCKN too long', () => {
      const result = tcknSchema.safeParse('123456789012');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('errors:tckn_too_long');
      }
    });

    it('should fail validation for non-numeric TCKN', () => {
      const result = tcknSchema.safeParse('1234567890a');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('errors:tckn_numeric_only');
      }
    });

    it('should fail validation for invalid TCKN checksum', () => {
      const result = tcknSchema.safeParse('12345678901');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('errors:tckn_invalid');
      }
    });
  });

  describe('vknSchema', () => {
    it('should pass validation for valid VKN', () => {
      const result = vknSchema.safeParse('1234567890');
      expect(result.success).toBe(true);
    });

    it('should fail validation for VKN too short', () => {
      const result = vknSchema.safeParse('123456789');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('errors:vkn_too_short');
      }
    });

    it('should fail validation for VKN too long', () => {
      const result = vknSchema.safeParse('12345678901');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('errors:vkn_too_long');
      }
    });

    it('should fail validation for non-numeric VKN', () => {
      const result = vknSchema.safeParse('123456789a');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('errors:vkn_numeric_only');
      }
    });

    it('should fail validation for invalid VKN', () => {
      const result = vknSchema.safeParse('');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('errors:vkn_too_short');
      }
    });
  });
});
