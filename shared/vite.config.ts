import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import dts from 'vite-plugin-dts';
import { glob } from 'glob';

const bannerPlugin = () => {
  return {
    name: 'banner',
    generateBundle(_: any, bundle: { [x: string]: any; }) {
      for (const fileName in bundle) {
        if (fileName.endsWith('.js') || fileName.endsWith('.mjs')) {
          const file = bundle[fileName];
          if (file.type === 'chunk') {
            file.code = '"use client";\n' + file.code;
          }
        }
      }
    },
  };
};


const entries = Object.fromEntries(
  glob.sync([
    './index.ts',
    './**/index.ts',
    './**/*.ts',
    './**/*.tsx',
  ], {
    ignore: [
      './**/*.d.ts',
      './**/*.test.ts',
      './**/*.test.tsx',
      './dist/**',
      './node_modules/**',
      './vite.config.ts'
    ],
  }).map(file => [
    file.replace(/\.(tsx?|jsx?)$/, ''),
    path.resolve(__dirname, file)
  ])
);

const nodeBuiltinsPlugin = () => {
  return {
    name: 'node-builtins',
    resolveId(id: string) {
      if (id === 'fsevents' || id.startsWith('node:') || 
          ['fs', 'path', 'crypto', 'os', 'stream', 'tty', 'util', 'url', 'module', 'events'].includes(id)) {
        return { id, external: true };
      }
      return null;
    }
  };
};

const moduleCreateRequirePlugin = () => {
  return {
    name: 'module-create-require',
    resolveId(id: string) {
      if (id === 'module' || id === 'node:module') {
        return { id: 'module', external: true };
      }
      return null;
    },
    load(id: string) {
      if (id === 'module') {
        return `
          export function createRequire() {
            return function require() {
              throw new Error('createRequire is not available in browser context');
            };
          }
          export default { createRequire };
        `;
      }
      return null;
    }
  };
};

export default defineConfig({
  plugins: [
    react({
      jsxRuntime: 'automatic',
      jsxImportSource: 'react',
      babel: {
        plugins: []
      }
    }),
    dts({
      outDir: 'dist/types',
      include: ['index.ts', './**/*.ts', './**/*.tsx'],
      exclude: [
        '**/*.test.ts', 
        '**/*.test.tsx', 
        '**/test/**',
        '**/test/setup.ts',
        'node_modules/**', 
        'dist/**', 
        'vite.config.ts'
      ],
      tsconfigPath: './tsconfig.json',
      rollupTypes: false, 
      insertTypesEntry: true,
      clearPureImport: true
    }),
    bannerPlugin(),
    nodeBuiltinsPlugin(),
    moduleCreateRequirePlugin()
  ],
  optimizeDeps: {
    exclude: ['fsevents', 'fsevents.node'],
    esbuildOptions: {
      define: {
        global: 'globalThis'
      },
      plugins: [
        {
          name: 'node-globals',
          setup(build) {
            build.onResolve({ filter: /^module$/ }, () => {
              return { path: 'module', external: true };
            });
            
            build.onResolve({ filter: /\.node$/ }, () => {
              return { external: true };
            });
            
            build.onResolve({ filter: /^fsevents$/ }, () => {
              return { path: 'fsevents', external: true };
            });
          }
        }
      ]
    }
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV)
  },
  resolve: {
    alias: [
      { find: '@', replacement: path.resolve(__dirname, '.') },
    ]
  },
  build: {
    target: 'es2020',
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: true,
    lib: {
      entry: entries,
      formats: ['es'],
      fileName: (_format, entryName) => `${entryName}.js`,
    },
    rollupOptions: {
      external: [
        'react', 
        'react-dom',
        'react-i18next',
        'i18next',
        /^@radix-ui\//,
        /^@tanstack\//,
        /^@hookform\//,
        /^@mdx-js\//,
        'date-fns',
        'zod',
        'virtual:i18next-loader',
        /^virtual:/,
        'fsevents',
        'fsevents.node',
        'path',
        'node:path',
        'fs',
        'node:fs',
        'module',
        'node:module',
        'crypto',
        'node:crypto',
        'os',
        'node:os',
        'stream',
        'node:stream',
        'tty',
        'node:tty',
        'util',
        'node:util',
        'url',
        'node:url',
        'events',
        'node:events',
        'assert',
        'node:assert',
        'buffer',
        'node:buffer',
        'child_process',
        'node:child_process',
        'http',
        'node:http',
        'https',
        'node:https',
        'net',
        'node:net',
        'tls',
        'node:tls',
        'zlib',
        'node:zlib',
        'process',
        'node:process',
        'v8',
        'node:v8',
        'worker_threads',
        'node:worker_threads',
        /^node:/,
      ],
      output: {
        preserveModules: true,
        preserveModulesRoot: './',
        entryFileNames: '[name].js',
        chunkFileNames: '[name].js',
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        },
      }
    },
    assetsInlineLimit: 0, 
    copyPublicDir: true,
  }
});
