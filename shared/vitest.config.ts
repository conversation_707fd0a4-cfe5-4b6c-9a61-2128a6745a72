import { fileURLToPath } from 'node:url';
import { defineConfig } from 'vitest/config';

export default defineConfig({
    test: {
      environment: 'jsdom',
      globals: true,
      setupFiles: ['./test/setup.ts', './hooks/test-setup.js', './test/components-setup.js'],
      testTransformMode: {
        web: ['.jsx', '.tsx'],
      },
      coverage: {
        provider: 'istanbul',
        reporter: ['text', 'json', 'html', 'lcov'],
        reportsDirectory: '../coverage/shared',
        include: [
          '**/*.{ts,tsx}',
          '!**/*.d.ts',
          '!**/node_modules/**',
          '!**/dist/**'
        ],
        exclude: [
          '**/*.test.{ts,tsx}',
          '**/*.spec.{ts,tsx}',
          '**/test/**',
          '**/types/**',
          '**/ui/**',
          '**/*.config.{ts,js}',
          '**/vite-env.d.ts'
        ],
        all: true,
        clean: true,
        thresholds: {
          global: {
            lines: 70,
            functions: 60,
            branches: 50,
            statements: 70
          },
          'components/organisms/layout/nav-group.tsx': {
            lines: 0,
            functions: 0,
            branches: 0,
            statements: 0
          },
          'components/atoms/__test-utils__/custom-matchers.ts': {
            lines: 20,
            functions: 17,
            branches: 0,
            statements: 19
          },
          'components/organisms/modal/**/*': {
            lines: 53,
            functions: 43,
            branches: 33,
            statements: 53
          },
          'components/organisms/layout/**/*': {
            lines: 52,
            functions: 39,
            branches: 23,
            statements: 52
          },
          'hooks/use-toast.ts': {
            lines: 58,
            functions: 50,
            branches: 40,
            statements: 58
          },
          'hooks/__mocks__/**/*': {
            lines: 0,
            functions: 100,
            branches: 100,
            statements: 0
          },
          'components/charts/bar.tsx': {
            lines: 62,
            functions: 56,
            branches: 38,
            statements: 62
          },
          'components/charts/table.tsx': {
            lines: 68,
            functions: 40,
            branches: 80,
            statements: 70
          },
          'components/organisms/table/index.tsx': {
            lines: 49,
            functions: 53,
            branches: 45,
            statements: 49
          }
        }
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./', import.meta.url)),
      },
    },
});
